@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-purple: #f3f2ff;
  --primary-blue: #0066cc;
  --primary-green: #00a651;
  --text-primary: #1a1a1a;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --background-white: #ffffff;
  --background-gray: #f9fafb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-white);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    --primary-purple: #2d2a3d;
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --border-light: #374151;
    --border-medium: #4b5563;
    --background-white: #1f2937;
    --background-gray: #111827;
  }
}

.app {
  display: flex;
  height: 100vh;
  background-color: #ffffff;
}

/* Sidebar */
.sidebar {
  width: 320px;
  background: linear-gradient(135deg, var(--primary-purple) 0%, #f8f7ff 100%);
  padding: 24px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding: 8px 0;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0052a3 100%);
  border-radius: var(--radius-md);
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
  box-shadow: var(--shadow-md);
  transition: transform 0.2s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.new-search-btn {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 16px 20px;
  margin-bottom: 32px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.new-search-btn:hover {
  background-color: var(--background-gray);
  border-color: var(--border-medium);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.nav-section {
  margin-bottom: 40px;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 4px;
}

.nav-item:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.6);
  transform: translateX(4px);
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
}

.nav-count {
  background-color: rgba(255, 255, 255, 0.8);
  color: var(--text-secondary);
  padding: 4px 10px;
  border-radius: var(--radius-xl);
  font-size: 13px;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.recent-chats {
  margin-top: auto;
}

.recent-chats h3 {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 12px;
  font-weight: 500;
}

.no-chats {
  color: #9ca3af;
  font-size: 14px;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background: linear-gradient(135deg, var(--background-white) 0%, #fafbff 100%);
  min-height: 100vh;
  position: relative;
  overflow-y: auto;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(0, 166, 81, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.header {
  text-align: center;
  margin-bottom: 64px;
  position: relative;
  z-index: 1;
}

/* Enhanced Title Styling */
.title-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.title-decoration {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 100%);
  border-radius: 50%;
  opacity: 0.1;
  z-index: 0;
}

.title-decoration::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: linear-gradient(45deg, var(--primary-blue), var(--primary-green));
  border-radius: 50%;
  opacity: 0.8;
}

.enhanced-title {
  position: relative;
  z-index: 1;
  font-size: 64px;
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.03em;
  text-align: center;
  margin: 0;
  cursor: default;
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    #0052a3  25%,
    var(--primary-green) 50%,
    #007a3d 75%,
    var(--text-primary) 100%
  );
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 8s ease-in-out infinite;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  filter: drop-shadow(0 2px 4px rgba(0, 102, 204, 0.1));
}

.title-word {
  display: inline-block;
  margin-right: 0.3em;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.title-word:last-child {
  margin-right: 0;
}

.title-word:hover {
  transform: translateY(-2px);
  filter: drop-shadow(0 4px 8px rgba(0, 102, 204, 0.2));
}

.title-word:first-child {
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0052a3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-word:nth-child(2) {
  font-weight: 600;
  background: linear-gradient(135deg, #0052a3 0%, var(--primary-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-word:last-child {
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--text-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-accent {
  height: 4px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--primary-blue) 20%,
    var(--primary-green) 50%,
    var(--primary-blue) 80%,
    transparent 100%
  );
  border-radius: 2px;
  margin-top: 16px;
  max-width: 400px;
  box-shadow: 0 2px 8px rgba(0, 102, 204, 0.3);
}

/* Gradient animation */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 50% 100%;
  }
  75% {
    background-position: 100% 0%;
  }
}

/* Enhanced title hover effects */
.enhanced-title:hover .title-word:first-child {
  animation: pulse 1.5s ease-in-out infinite;
}

.enhanced-title:hover .title-word:nth-child(2) {
  animation: pulse 1.5s ease-in-out infinite 0.2s;
}

.enhanced-title:hover .title-word:last-child {
  animation: pulse 1.5s ease-in-out infinite 0.4s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1) translateY(0);
  }
  50% {
    transform: scale(1.05) translateY(-2px);
  }
}

/* Legacy title class for backward compatibility */
.title {
  font-size: 64px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.1;
  margin-bottom: 12px;
  letter-spacing: -0.03em;
}

/* Search Section */
.search-container {
  width: 100%;
  max-width: 700px;
  margin-bottom: 48px;
  position: relative;
  z-index: 1;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 20px 140px 20px 28px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-2xl);
  font-size: 18px;
  font-weight: 400;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--background-white);
  box-shadow: var(--shadow-sm);
  color: var(--text-primary);
}

.search-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1), var(--shadow-lg);
  transform: translateY(-2px);
}

.search-input::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
}

.search-controls {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 12px;
}

.research-badge {
  background: linear-gradient(135deg, var(--background-gray) 0%, #f1f5f9 100%);
  color: var(--text-secondary);
  padding: 6px 12px;
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.search-btn {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--text-primary) 0%, #374151 100%);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.search-btn:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.search-btn:active {
  transform: scale(0.95);
}

.search-icon {
  width: 18px;
  height: 18px;
  color: white;
}

/* Suggestion Chips */
.suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  max-width: 900px;
  position: relative;
  z-index: 1;
}

.suggestion-chip {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-2xl);
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.suggestion-chip::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 102, 204, 0.1), transparent);
  transition: left 0.5s ease;
}

.suggestion-chip:hover {
  background: linear-gradient(135deg, var(--background-white) 0%, var(--background-gray) 100%);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.suggestion-chip:hover::before {
  left: 100%;
}

.suggestion-chip:active {
  transform: translateY(0);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex !important;
  }

  .sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: var(--shadow-xl);
  }

  .sidebar.open {
    left: 0;
  }

  .main-content {
    padding: 80px 20px 24px 20px;
  }

  .enhanced-title {
    font-size: 48px;
  }

  .title-decoration {
    width: 50px;
    height: 50px;
    top: -15px;
  }

  .title-decoration::before {
    width: 20px;
    height: 20px;
  }

  .title-accent {
    max-width: 300px;
    height: 3px;
  }

  .search-input {
    padding: 16px 120px 16px 20px;
    font-size: 16px;
  }

  .suggestions {
    gap: 12px;
  }

  .suggestion-chip {
    padding: 10px 16px;
    font-size: 14px;
  }

  .recent-chats h3 {
    font-size: 14px;
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .enhanced-title {
    font-size: 36px;
    letter-spacing: -0.02em;
  }

  .title-decoration {
    width: 40px;
    height: 40px;
    top: -10px;
  }

  .title-decoration::before {
    width: 16px;
    height: 16px;
  }

  .title-accent {
    max-width: 250px;
    height: 2px;
    margin-top: 12px;
  }

  .title-word {
    margin-right: 0.2em;
  }

  .search-input {
    padding: 14px 100px 14px 16px;
    font-size: 15px;
  }

  .search-btn {
    width: 36px;
    height: 36px;
  }

  .research-badge {
    font-size: 10px;
    padding: 4px 8px;
  }

  .suggestion-chip {
    padding: 8px 14px;
    font-size: 13px;
  }
}

/* Overlay for mobile sidebar */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Enhanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.4s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}
