import { useState } from 'react'
import { Search, ChevronDown, MessageSquare, TrendingUp, Globe, FileText, BarChart3, Users } from 'lucide-react'
import ConversationView from './components/ConversationView'

function App() {
  const [searchQuery, setSearchQuery] = useState('')
  const [conversations, setConversations] = useState([])
  const [currentView, setCurrentView] = useState('search') // 'search' or 'conversation'
  const [activeQuery, setActiveQuery] = useState('')

  const suggestionChips = [
    'EU plastic regulations 2024',
    'CBAM carbon border adjustment',
    'Circular economy packaging',
    'SABIC sustainability strategy',
    'Petrochemical market trends',
    'ESG reporting requirements',
    'Renewable feedstock adoption',
    'Carbon footprint reduction'
  ]

  const handleSearch = (query) => {
    if (query.trim()) {
      setActiveQuery(query)
      setCurrentView('conversation')
      setSearchQuery('')

      // Add to conversations list
      const newConversation = {
        id: Date.now(),
        query: query,
        timestamp: new Date()
      }
      setConversations([newConversation, ...conversations])
    }
  }

  const handleBackToSearch = () => {
    setCurrentView('search')
    setActiveQuery('')
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Logo and Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-borouge-blue rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <span className="font-semibold text-gray-900">Borouge ESG</span>
          </div>
        </div>

        {/* New Search Button */}
        <div className="p-4">
          <button
            onClick={handleBackToSearch}
            className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <MessageSquare className="w-4 h-4" />
            <span>Start new search</span>
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4">
          <div className="space-y-1">
            <div className="flex items-center justify-between py-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Intelligence</span>
              <span className="text-xs text-gray-400">{conversations.length}</span>
            </div>

            <div className="flex items-center justify-between py-2">
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Saved</span>
              <span className="text-xs text-gray-400">0</span>
            </div>
          </div>
        </nav>

        {/* Recent Chats */}
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 mb-2">Recent Chats</div>
          {conversations.length === 0 ? (
            <div className="text-sm text-gray-400">No saved chats yet.</div>
          ) : (
            <div className="space-y-2">
              {conversations.slice(0, 5).map((conversation) => (
                <button
                  key={conversation.id}
                  onClick={() => {
                    setActiveQuery(conversation.query)
                    setCurrentView('conversation')
                  }}
                  className="w-full text-left text-sm text-gray-600 hover:text-gray-900 truncate"
                >
                  {conversation.query}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {currentView === 'search' ? (
          <>
            {/* Header */}
            <header className="bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="p-1 hover:bg-gray-100 rounded">
                    <ChevronDown className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Globe className="w-4 h-4" />
                    <span>Global Markets</span>
                  </div>
                </div>
              </div>
            </header>

            {/* Main Search Interface */}
            <main className="flex-1 flex flex-col items-center justify-center px-6">
              <div className="max-w-2xl w-full text-center">
                {/* Title */}
                <h1 className="text-4xl font-bold text-gray-900 mb-2">
                  The ESG Intelligence Engine for
                </h1>
                <h2 className="text-4xl font-bold text-gray-900 mb-8 flex items-center justify-center">
                  Petrochemicals
                  <ChevronDown className="w-6 h-6 ml-2 text-gray-400" />
                </h2>

                {/* Search Bar */}
                <div className="relative mb-8">
                  <div className="relative">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                      placeholder="EU plastic regulations impact on Borouge operations"
                      className="w-full px-4 py-4 pr-12 text-lg border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-borouge-blue focus:border-transparent"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        INTELLIGENCE
                      </span>
                      <button
                        onClick={() => handleSearch(searchQuery)}
                        className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors"
                      >
                        <Search className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Suggestion Chips */}
                <div className="flex flex-wrap gap-2 justify-center">
                  {suggestionChips.map((chip, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(chip)}
                      className="px-3 py-1 text-sm text-gray-600 bg-white border border-gray-200 rounded-full hover:bg-gray-50 hover:border-gray-300 transition-colors"
                    >
                      {chip}
                    </button>
                  ))}
                </div>

                {/* Features */}
                <div className="mt-12 grid grid-cols-3 gap-6 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-borouge-green" />
                    <span>Market Intelligence</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FileText className="w-4 h-4 text-borouge-green" />
                    <span>Regulatory Updates</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-borouge-green" />
                    <span>Competitor Analysis</span>
                  </div>
                </div>
              </div>
            </main>
          </>
        ) : (
          <ConversationView
            initialQuery={activeQuery}
            onBack={handleBackToSearch}
          />
        )}
      </div>
    </div>
  )
}

export default App
