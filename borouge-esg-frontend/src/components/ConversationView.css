.conversation-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, var(--background-white) 0%, #fafbff 100%);
  max-width: 100%;
  margin: 0 auto;
}

/* Header */
.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 48px;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--background-gray);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.conversation-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #0052a3;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Messages */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px 48px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 90%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.user-message {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0052a3 100%);
  color: white;
  padding: 16px 20px;
  border-radius: var(--radius-xl);
  border-bottom-right-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
}

.message-content {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
}

.ai-message {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  border-bottom-left-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.ai-response {
  padding: 24px;
}

/* Intelligence Report */
.intelligence-report {
  max-width: none;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.report-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.copy-btn {
  padding: 8px;
  background: transparent;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: var(--background-gray);
  color: var(--text-primary);
}

.report-summary {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 24px;
}

/* Key Findings */
.key-findings h4,
.sources-section h4,
.action-items h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.finding-card {
  background: var(--background-gray);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.finding-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.finding-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.finding-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.finding-card .finding-icon {
  background: var(--primary-blue);
}

.finding-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.impact-badge {
  padding: 4px 12px;
  border-radius: var(--radius-xl);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.impact-badge.high {
  background: #fee2e2;
  color: #dc2626;
}

.impact-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.impact-badge.low {
  background: #dcfce7;
  color: #16a34a;
}

.finding-description {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.confidence-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.confidence-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 100px;
}

.confidence-progress {
  flex: 1;
  height: 6px;
  background: var(--border-light);
  border-radius: 3px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-blue) 100%);
  border-radius: 3px;
}

/* Sources */
.sources-section {
  margin-top: 32px;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.source-card {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 16px;
  transition: all 0.2s ease;
}

.source-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.source-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.source-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-blue);
}

.source-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* Action Items */
.action-items {
  margin-top: 32px;
}

.action-items ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.action-items li {
  padding: 12px 16px;
  background: var(--background-gray);
  border-left: 4px solid var(--primary-blue);
  border-radius: var(--radius-md);
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
}

/* Loading */
.loading-message {
  align-self: flex-start;
  max-width: 85%;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  border-bottom-left-radius: var(--radius-sm);
  color: var(--text-secondary);
  font-size: 16px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Input */
.message-input-container {
  padding: 24px 48px;
  background: var(--background-white);
  border-top: 1px solid var(--border-light);
}

.message-input-box {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.message-input {
  flex: 1;
  min-height: 48px;
  max-height: 120px;
  padding: 14px 20px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: 16px;
  font-family: inherit;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
}

.message-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.send-btn {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0052a3 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .conversation-header {
    padding: 16px 20px;
    max-width: 100%;
  }

  .messages-container {
    padding: 16px 20px;
    max-width: 100%;
  }

  .message {
    max-width: 95%;
  }

  .message-input-container {
    padding: 16px 20px;
  }

  .message-input-box {
    max-width: 100%;
  }

  .sources-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .conversation-header {
    padding: 12px 16px;
  }

  .messages-container {
    padding: 12px 16px;
  }

  .message-input-container {
    padding: 12px 16px;
  }
}
