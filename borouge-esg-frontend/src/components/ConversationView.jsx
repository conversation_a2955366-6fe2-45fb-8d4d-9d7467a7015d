import { useState } from 'react'
import { Search, Send, Loader2, ExternalLink, TrendingUp, AlertTriangle, Info } from 'lucide-react'

const ConversationView = ({ initialQuery, onBack }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'user',
      content: initialQuery,
      timestamp: new Date()
    },
    {
      id: 2,
      type: 'assistant',
      content: 'Analyzing EU plastic regulations impact on Borouge operations...',
      isLoading: true,
      timestamp: new Date()
    }
  ])
  const [newMessage, setNewMessage] = useState('')

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        type: 'user',
        content: newMessage,
        timestamp: new Date()
      }
      
      const loadingMessage = {
        id: messages.length + 2,
        type: 'assistant',
        content: 'Processing your request...',
        isLoading: true,
        timestamp: new Date()
      }

      setMessages([...messages, userMessage, loadingMessage])
      setNewMessage('')
    }
  }

  // Mock intelligence response for demonstration
  const mockIntelligenceResponse = {
    summary: "EU plastic regulations are significantly impacting petrochemical operations through new packaging directives and CBAM requirements.",
    keyFindings: [
      {
        type: "regulatory",
        title: "EU Packaging Directive 2024",
        impact: "High",
        description: "New recycled content requirements for plastic packaging affecting 40% of Borouge's EU exports"
      },
      {
        type: "financial",
        title: "CBAM Carbon Costs",
        impact: "Medium",
        description: "Estimated €15-25/tonne additional cost for polyethylene exports to EU starting 2026"
      },
      {
        type: "competitive",
        title: "SABIC Response Strategy",
        impact: "Medium",
        description: "Competitor investing €500M in recycling facilities to meet new requirements"
      }
    ],
    sources: [
      { title: "EU Commission Packaging Directive", url: "europa.eu", date: "2024-01-15" },
      { title: "CBAM Implementation Guidelines", url: "echa.europa.eu", date: "2024-02-01" },
      { title: "SABIC Sustainability Report", url: "sabic.com", date: "2024-01-30" }
    ]
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <button 
            onClick={onBack}
            className="text-sm text-gray-600 hover:text-gray-900"
          >
            ← Back to Search
          </button>
          <div className="text-sm text-gray-500">
            ESG Intelligence Session
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-6 py-4 space-y-6">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-3xl ${message.type === 'user' ? 'bg-borouge-blue text-white' : 'bg-white border border-gray-200'} rounded-lg p-4`}>
              {message.isLoading ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-gray-600">{message.content}</span>
                </div>
              ) : message.type === 'assistant' && message.id === 2 ? (
                // Mock intelligence response
                <div className="space-y-4">
                  <div className="text-lg font-semibold text-gray-900">
                    EU Plastic Regulations Impact Analysis
                  </div>
                  
                  <div className="text-gray-700">
                    {mockIntelligenceResponse.summary}
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Key Findings</h4>
                    {mockIntelligenceResponse.keyFindings.map((finding, index) => (
                      <div key={index} className="border-l-4 border-borouge-blue pl-4">
                        <div className="flex items-center space-x-2 mb-1">
                          {finding.type === 'regulatory' && <AlertTriangle className="w-4 h-4 text-orange-500" />}
                          {finding.type === 'financial' && <TrendingUp className="w-4 h-4 text-green-500" />}
                          {finding.type === 'competitive' && <Info className="w-4 h-4 text-blue-500" />}
                          <span className="font-medium">{finding.title}</span>
                          <span className={`text-xs px-2 py-1 rounded ${
                            finding.impact === 'High' ? 'bg-red-100 text-red-700' :
                            finding.impact === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-green-100 text-green-700'
                          }`}>
                            {finding.impact} Impact
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">{finding.description}</p>
                      </div>
                    ))}
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">Sources</h4>
                    {mockIntelligenceResponse.sources.map((source, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm">
                        <ExternalLink className="w-3 h-3 text-gray-400" />
                        <span className="text-borouge-blue hover:underline cursor-pointer">{source.title}</span>
                        <span className="text-gray-400">•</span>
                        <span className="text-gray-500">{source.url}</span>
                        <span className="text-gray-400">•</span>
                        <span className="text-gray-500">{source.date}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-sm">{message.content}</div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Input */}
      <div className="bg-white border-t border-gray-200 px-6 py-4">
        <div className="flex space-x-3">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Ask a follow-up question about ESG intelligence..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-borouge-blue focus:border-transparent"
          />
          <button
            onClick={handleSendMessage}
            className="px-4 py-2 bg-borouge-blue text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>Send</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default ConversationView
