import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Send,
  Download,
  ExternalLink,
  TrendingUp,
  AlertTriangle,
  Info,
  Loader2,
  Copy,
  Share2
} from 'lucide-react';
import './ConversationView.css';

const ConversationView = ({ initialQuery, onBack }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (initialQuery) {
      // Add initial user message
      const userMessage = {
        id: 1,
        type: 'user',
        content: initialQuery,
        timestamp: new Date()
      };

      setMessages([userMessage]);
      setIsLoading(true);

      // Simulate AI response after delay
      setTimeout(() => {
        const aiResponse = {
          id: 2,
          type: 'assistant',
          content: generateMockResponse(initialQuery),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsLoading(false);
      }, 2000);
    }
  }, [initialQuery]);

  const generateMockResponse = (query) => {
    // Generate comprehensive intelligence reports based on query type
    const getReportByQuery = (query) => {
      const lowerQuery = query.toLowerCase();

      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {
        return generateEURegulationReport();
      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {
        return generateCBAMReport();
      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {
        return generateCircularEconomyReport();
      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {
        return generateCompetitorReport();
      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {
        return generateMarketTrendsReport();
      } else {
        return generateComprehensiveESGReport();
      }
    };

    return getReportByQuery(query);
  };

  const generateEURegulationReport = () => {
    return {
      reportType: "Regulatory Intelligence Report",
      executiveSummary: "The EU's new packaging regulations present significant compliance challenges and opportunities for Borouge's European operations. With 65% of Borouge's polyethylene exports destined for EU markets, immediate strategic action is required to maintain market position and capitalize on emerging opportunities in sustainable packaging solutions.",
      keyFindings: [
        {
          type: "regulatory",
          title: "EU Packaging & Packaging Waste Regulation (PPWR) 2024",
          impact: "High",
          urgency: "Critical",
          description: "New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.",
          details: "The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.",
          confidence: 98,
          timeline: "Implementation: Jan 2025, Full compliance: 2030"
        },
        {
          type: "financial",
          title: "Compliance Investment Requirements",
          impact: "High",
          urgency: "High",
          description: "Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.",
          details: "Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.",
          confidence: 85,
          timeline: "Investment period: 2024-2027"
        },
        {
          type: "market",
          title: "Premium Pricing Opportunity",
          impact: "Medium",
          urgency: "Medium",
          description: "Sustainable packaging commands 15-25% price premium in EU markets, potentially offsetting compliance costs.",
          details: "Early movers in sustainable polyethylene have captured premium pricing. Borouge could achieve €150-250M additional annual revenue through premium sustainable product lines.",
          confidence: 78,
          timeline: "Market opportunity: 2025-2030"
        },
        {
          type: "competitive",
          title: "Competitive Landscape Shift",
          impact: "High",
          urgency: "High",
          description: "SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.",
          details: "SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.",
          confidence: 92,
          timeline: "Competitive threat: Immediate"
        },
        {
          type: "technology",
          title: "Chemical Recycling Technology Gap",
          impact: "Medium",
          urgency: "High",
          description: "Current mechanical recycling insufficient for quality requirements; chemical recycling technology partnerships essential.",
          details: "Pyrolysis and depolymerization technologies can achieve virgin-quality recycled content. Strategic partnerships with Plastic Energy, Quantafuel, or Agilyx recommended.",
          confidence: 88,
          timeline: "Technology deployment: 2025-2026"
        },
        {
          type: "regulatory",
          title: "Extended Producer Responsibility (EPR) Expansion",
          impact: "Medium",
          urgency: "Medium",
          description: "EPR schemes expanding across EU member states, creating additional compliance obligations and costs.",
          details: "EPR fees ranging €50-200 per tonne of plastic packaging. Borouge's annual EPR liability estimated at €25-35M across EU markets.",
          confidence: 90,
          timeline: "Implementation: 2024-2026"
        }
      ],
      marketImpact: {
        revenueAtRisk: "€2.1B (65% of EU sales)",
        investmentRequired: "$800M-1.2B",
        timelineForCompliance: "18 months critical path",
        marketOpportunity: "€150-250M premium pricing potential"
      },
      riskAssessment: {
        high: ["Market access restrictions", "Competitive disadvantage", "Regulatory penalties"],
        medium: ["Supply chain disruption", "Technology integration challenges", "Customer relationship impact"],
        low: ["Reputational impact", "Talent acquisition challenges"]
      },
      strategicRecommendations: [
        {
          priority: "Critical",
          action: "Establish EU Regulatory Compliance Task Force",
          timeline: "Immediate (Q1 2024)",
          investment: "$5M",
          description: "Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets."
        },
        {
          priority: "High",
          action: "Secure Chemical Recycling Technology Partnerships",
          timeline: "6 months (Q2 2024)",
          investment: "$200-300M",
          description: "Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply."
        },
        {
          priority: "High",
          action: "Launch Sustainable Product Line Development",
          timeline: "12 months (Q4 2024)",
          investment: "$150M",
          description: "Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications."
        },
        {
          priority: "Medium",
          action: "Establish European Recycling Hub",
          timeline: "24 months (Q4 2025)",
          investment: "$400-500M",
          description: "Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization."
        }
      ],
      competitiveBenchmarking: [
        {
          company: "SABIC",
          strategy: "€2B circular economy investment, 1M tonnes recycled content by 2030",
          advantage: "First-mover in chemical recycling, strong EU presence",
          weakness: "Higher cost base, limited feedstock security"
        },
        {
          company: "Dow",
          strategy: "Advanced recycling partnerships, circular design principles",
          advantage: "Technology leadership, established partnerships",
          weakness: "Focus on specialty applications, limited commodity exposure"
        },
        {
          company: "LyondellBasell",
          strategy: "Molecular recycling technology, circular economy solutions",
          advantage: "Integrated technology development, scale advantages",
          weakness: "Limited EU manufacturing footprint"
        }
      ],
      sources: [
        { title: "EU Packaging & Packaging Waste Regulation", url: "eur-lex.europa.eu", date: "2024-01-15", type: "regulation", confidence: "Official" },
        { title: "European Environment Agency Circular Economy Report", url: "eea.europa.eu", date: "2024-02-01", type: "research", confidence: "High" },
        { title: "SABIC Circular Economy Strategy Update", url: "sabic.com", date: "2024-01-30", type: "corporate", confidence: "High" },
        { title: "Plastics Europe Market Data 2024", url: "plasticseurope.org", date: "2024-02-15", type: "industry", confidence: "High" },
        { title: "McKinsey Circular Economy in Chemicals", url: "mckinsey.com", date: "2024-01-20", type: "consulting", confidence: "Medium" },
        { title: "Wood Mackenzie Petrochemicals Outlook", url: "woodmac.com", date: "2024-02-10", type: "market", confidence: "High" }
      ]
    };
  };

  const generateCBAMReport = () => {
    return {
      reportType: "Carbon Border Adjustment Mechanism (CBAM) Impact Analysis",
      executiveSummary: "CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.",
      keyFindings: [
        {
          type: "financial",
          title: "Direct CBAM Cost Impact",
          impact: "High",
          urgency: "Critical",
          description: "Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.",
          details: "Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.",
          confidence: 92,
          timeline: "Implementation: January 2026"
        },
        {
          type: "competitive",
          title: "Competitive Advantage Opportunity",
          impact: "High",
          urgency: "High",
          description: "UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.",
          details: "Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.",
          confidence: 88,
          timeline: "Advantage period: 2026-2035"
        },
        {
          type: "technology",
          title: "Carbon Reduction Investment Requirements",
          impact: "Medium",
          urgency: "High",
          description: "€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.",
          details: "Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).",
          confidence: 85,
          timeline: "Investment period: 2024-2030"
        },
        {
          type: "regulatory",
          title: "CBAM Reporting and Verification Requirements",
          impact: "Medium",
          urgency: "High",
          description: "Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.",
          details: "Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.",
          confidence: 95,
          timeline: "Reporting starts: October 2023"
        },
        {
          type: "market",
          title: "Premium Low-Carbon Product Opportunity",
          impact: "High",
          urgency: "Medium",
          description: "Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.",
          details: "Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.",
          confidence: 78,
          timeline: "Market development: 2025-2030"
        }
      ],
      marketImpact: {
        revenueAtRisk: "€45-75M annual CBAM costs",
        investmentRequired: "€300-500M carbon reduction",
        timelineForCompliance: "30 months to full implementation",
        marketOpportunity: "€200-300M low-carbon premium potential"
      },
      strategicRecommendations: [
        {
          priority: "Critical",
          action: "Implement Comprehensive Carbon Accounting System",
          timeline: "6 months (Q2 2024)",
          investment: "$10M",
          description: "Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization."
        },
        {
          priority: "High",
          action: "Accelerate Renewable Energy Integration",
          timeline: "18 months (Q3 2025)",
          investment: "$200M",
          description: "Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity."
        },
        {
          priority: "High",
          action: "Develop Low-Carbon Product Certification",
          timeline: "12 months (Q4 2024)",
          investment: "$5M",
          description: "Establish third-party verified low-carbon product lines for premium EU market positioning."
        }
      ],
      sources: [
        { title: "EU CBAM Regulation 2023/956", url: "eur-lex.europa.eu", date: "2023-05-17", type: "regulation", confidence: "Official" },
        { title: "European Commission CBAM Implementation Guide", url: "taxation-customs.ec.europa.eu", date: "2024-01-10", type: "guidance", confidence: "Official" },
        { title: "IEA Petrochemicals Carbon Intensity Database", url: "iea.org", date: "2024-02-05", type: "research", confidence: "High" }
      ]
    };
  };

  const generateComprehensiveESGReport = () => {
    return {
      reportType: "Comprehensive ESG Intelligence Report",
      executiveSummary: "Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.",
      keyFindings: [
        {
          type: "environmental",
          title: "Decarbonization Pathway Requirements",
          impact: "High",
          urgency: "High",
          description: "Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.",
          details: "Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).",
          confidence: 90,
          timeline: "Transformation period: 2024-2050"
        },
        {
          type: "social",
          title: "UAE Emiratization and Skills Development",
          impact: "Medium",
          urgency: "High",
          description: "UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.",
          details: "Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.",
          confidence: 85,
          timeline: "Achievement target: 2030"
        },
        {
          type: "governance",
          title: "ESG Reporting and Transparency Enhancement",
          impact: "Medium",
          urgency: "Medium",
          description: "Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.",
          details: "Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.",
          confidence: 88,
          timeline: "Full compliance: 2025"
        },
        {
          type: "financial",
          title: "Sustainable Finance and Green Bonds Opportunity",
          impact: "High",
          urgency: "Medium",
          description: "Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).",
          details: "Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.",
          confidence: 82,
          timeline: "Financing window: 2024-2027"
        },
        {
          type: "technology",
          title: "Digital ESG Management Platform",
          impact: "Medium",
          urgency: "Medium",
          description: "Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.",
          details: "Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.",
          confidence: 75,
          timeline: "Implementation: 2024-2025"
        }
      ],
      strategicRecommendations: [
        {
          priority: "Critical",
          action: "Establish Chief Sustainability Officer Role",
          timeline: "Immediate (Q1 2024)",
          investment: "$2M",
          description: "Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance."
        },
        {
          priority: "High",
          action: "Launch Comprehensive Decarbonization Program",
          timeline: "6 months (Q2 2024)",
          investment: "$500M",
          description: "Multi-year program covering renewable energy, process optimization, and breakthrough technology development."
        },
        {
          priority: "High",
          action: "Implement Advanced Emiratization Strategy",
          timeline: "12 months (Q4 2024)",
          investment: "$100M",
          description: "Comprehensive talent development program including partnerships with UAE universities and vocational training institutes."
        }
      ],
      sources: [
        { title: "UAE Vision 2071 Strategic Framework", url: "government.ae", date: "2023-12-01", type: "policy", confidence: "Official" },
        { title: "ADNOC Sustainability Strategy 2030", url: "adnoc.ae", date: "2024-01-15", type: "corporate", confidence: "High" },
        { title: "McKinsey ESG in Chemicals Industry", url: "mckinsey.com", date: "2024-02-01", type: "consulting", confidence: "Medium" }
      ]
    };
  };

  const generateCircularEconomyReport = () => {
    return {
      reportType: "Circular Economy Transition Analysis",
      executiveSummary: "Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.",
      keyFindings: [
        {
          type: "market",
          title: "Recycled Polyethylene Market Growth",
          impact: "High",
          urgency: "High",
          description: "Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.",
          details: "Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.",
          confidence: 88,
          timeline: "Market expansion: 2024-2030"
        }
      ],
      sources: [
        { title: "Ellen MacArthur Foundation Circular Economy Report", url: "ellenmacarthurfoundation.org", date: "2024-01-10", type: "research", confidence: "High" }
      ]
    };
  };

  const generateCompetitorReport = () => {
    return {
      reportType: "Competitive Intelligence Analysis",
      executiveSummary: "SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.",
      keyFindings: [
        {
          type: "competitive",
          title: "SABIC Circular Economy Leadership",
          impact: "High",
          urgency: "Critical",
          description: "SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.",
          details: "SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.",
          confidence: 95,
          timeline: "Competitive threat: Immediate"
        }
      ],
      sources: [
        { title: "SABIC Sustainability Strategy 2030", url: "sabic.com", date: "2024-01-15", type: "corporate", confidence: "High" }
      ]
    };
  };

  const generateMarketTrendsReport = () => {
    return {
      reportType: "Market Trends & Outlook Analysis",
      executiveSummary: "Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.",
      keyFindings: [
        {
          type: "market",
          title: "Sustainable Packaging Demand Surge",
          impact: "High",
          urgency: "High",
          description: "85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.",
          details: "Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.",
          confidence: 90,
          timeline: "Market shift: 2024-2027"
        }
      ],
      sources: [
        { title: "McKinsey Sustainable Packaging Report", url: "mckinsey.com", date: "2024-02-01", type: "consulting", confidence: "High" }
      ]
    };
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        type: 'user',
        content: newMessage,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);
      setNewMessage('');
      setIsLoading(true);

      // Simulate AI response
      setTimeout(() => {
        const aiResponse = {
          id: messages.length + 2,
          type: 'assistant',
          content: generateMockResponse(newMessage),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsLoading(false);
      }, 1500);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const exportToPDF = () => {
    // Mock export functionality
    console.log('Exporting conversation to PDF...');
    // In real implementation, use jsPDF or similar
  };

  const copyMessage = (content) => {
    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));
  };

  return (
    <motion.div
      className="conversation-view"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="conversation-header">
        <motion.button
          className="back-btn"
          onClick={onBack}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft size={20} />
          Back to Search
        </motion.button>

        <div className="conversation-actions">
          <motion.button
            className="action-btn"
            onClick={exportToPDF}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download size={18} />
            Export
          </motion.button>
          <motion.button
            className="action-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Share2 size={18} />
            Share
          </motion.button>
        </div>
      </div>

      {/* Messages */}
      <div className="messages-container">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              className={`message ${message.type}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {message.type === 'user' ? (
                <div className="user-message">
                  <div className="message-content">{message.content}</div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              ) : (
                <div className="ai-message">
                  <div className="ai-response">
                    {typeof message.content === 'object' ? (
                      <div className="intelligence-report">
                        <div className="report-header">
                          <div className="report-title-section">
                            <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>
                            <div className="report-meta">
                              <span className="report-timestamp">Generated: {new Date().toLocaleDateString()}</span>
                              <span className="report-confidence">High Confidence Analysis</span>
                            </div>
                          </div>
                          <button
                            className="copy-btn"
                            onClick={() => copyMessage(message.content)}
                          >
                            <Copy size={16} />
                          </button>
                        </div>

                        {message.content.executiveSummary && (
                          <div className="executive-summary">
                            <h4>Executive Summary</h4>
                            <p>{message.content.executiveSummary}</p>
                          </div>
                        )}

                        {message.content.marketImpact && (
                          <div className="market-impact-section">
                            <h4>Market Impact Analysis</h4>
                            <div className="impact-metrics">
                              <div className="impact-metric">
                                <span className="metric-label">Revenue at Risk</span>
                                <span className="metric-value risk">{message.content.marketImpact.revenueAtRisk}</span>
                              </div>
                              <div className="impact-metric">
                                <span className="metric-label">Investment Required</span>
                                <span className="metric-value investment">{message.content.marketImpact.investmentRequired}</span>
                              </div>
                              <div className="impact-metric">
                                <span className="metric-label">Timeline</span>
                                <span className="metric-value timeline">{message.content.marketImpact.timelineForCompliance}</span>
                              </div>
                              <div className="impact-metric">
                                <span className="metric-label">Market Opportunity</span>
                                <span className="metric-value opportunity">{message.content.marketImpact.marketOpportunity}</span>
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="key-findings">
                          <h4>Key Findings</h4>
                          {message.content.keyFindings.map((finding, index) => (
                            <motion.div
                              key={index}
                              className="finding-card"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                            >
                              <div className="finding-header">
                                <div className="finding-icon">
                                  {finding.type === 'regulatory' && <AlertTriangle size={16} />}
                                  {finding.type === 'financial' && <TrendingUp size={16} />}
                                  {finding.type === 'competitive' && <Info size={16} />}
                                  {finding.type === 'market' && <TrendingUp size={16} />}
                                  {finding.type === 'technology' && <Info size={16} />}
                                  {finding.type === 'environmental' && <AlertTriangle size={16} />}
                                  {finding.type === 'social' && <Info size={16} />}
                                  {finding.type === 'governance' && <Info size={16} />}
                                </div>
                                <div className="finding-title">{finding.title}</div>
                                <div className="finding-badges">
                                  <div className={`impact-badge ${finding.impact.toLowerCase()}`}>
                                    {finding.impact} Impact
                                  </div>
                                  {finding.urgency && (
                                    <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>
                                      {finding.urgency}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <p className="finding-description">{finding.description}</p>
                              {finding.details && (
                                <div className="finding-details">
                                  <p>{finding.details}</p>
                                </div>
                              )}
                              {finding.timeline && (
                                <div className="finding-timeline">
                                  <strong>Timeline:</strong> {finding.timeline}
                                </div>
                              )}
                              <div className="confidence-bar">
                                <div className="confidence-label">Confidence: {finding.confidence}%</div>
                                <div className="confidence-progress">
                                  <motion.div
                                    className="confidence-fill"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${finding.confidence}%` }}
                                    transition={{ duration: 1, delay: 0.5 }}
                                  />
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>

                        {message.content.strategicRecommendations && (
                          <div className="strategic-recommendations">
                            <h4>Strategic Recommendations</h4>
                            {message.content.strategicRecommendations.map((rec, index) => (
                              <motion.div
                                key={index}
                                className="recommendation-card"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 + index * 0.1 }}
                              >
                                <div className="recommendation-header">
                                  <div className={`priority-badge ${rec.priority.toLowerCase()}`}>
                                    {rec.priority} Priority
                                  </div>
                                  <div className="recommendation-investment">{rec.investment}</div>
                                </div>
                                <h5 className="recommendation-action">{rec.action}</h5>
                                <p className="recommendation-description">{rec.description}</p>
                                <div className="recommendation-timeline">
                                  <strong>Timeline:</strong> {rec.timeline}
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        )}

                        {message.content.competitiveBenchmarking && (
                          <div className="competitive-benchmarking">
                            <h4>Competitive Benchmarking</h4>
                            <div className="competitors-grid">
                              {message.content.competitiveBenchmarking.map((comp, index) => (
                                <motion.div
                                  key={index}
                                  className="competitor-card"
                                  initial={{ opacity: 0, scale: 0.95 }}
                                  animate={{ opacity: 1, scale: 1 }}
                                  transition={{ delay: 0.4 + index * 0.1 }}
                                >
                                  <h6 className="competitor-name">{comp.company}</h6>
                                  <div className="competitor-strategy">
                                    <strong>Strategy:</strong> {comp.strategy}
                                  </div>
                                  <div className="competitor-analysis">
                                    <div className="advantage">
                                      <strong>Advantage:</strong> {comp.advantage}
                                    </div>
                                    <div className="weakness">
                                      <strong>Weakness:</strong> {comp.weakness}
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )}

                        {message.content.riskAssessment && (
                          <div className="risk-assessment">
                            <h4>Risk Assessment</h4>
                            <div className="risk-categories">
                              <div className="risk-category high-risk">
                                <h6>High Risk</h6>
                                <ul>
                                  {message.content.riskAssessment.high.map((risk, index) => (
                                    <li key={index}>{risk}</li>
                                  ))}
                                </ul>
                              </div>
                              <div className="risk-category medium-risk">
                                <h6>Medium Risk</h6>
                                <ul>
                                  {message.content.riskAssessment.medium.map((risk, index) => (
                                    <li key={index}>{risk}</li>
                                  ))}
                                </ul>
                              </div>
                              <div className="risk-category low-risk">
                                <h6>Low Risk</h6>
                                <ul>
                                  {message.content.riskAssessment.low.map((risk, index) => (
                                    <li key={index}>{risk}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="sources-section">
                          <h4>Sources & References</h4>
                          <div className="sources-grid">
                            {message.content.sources.map((source, index) => (
                              <motion.div
                                key={index}
                                className="source-card"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 + index * 0.1 }}
                              >
                                <div className="source-header">
                                  <ExternalLink size={14} />
                                  <span className="source-title">{source.title}</span>
                                  {source.confidence && (
                                    <span className={`source-confidence ${source.confidence.toLowerCase()}`}>
                                      {source.confidence}
                                    </span>
                                  )}
                                </div>
                                <div className="source-meta">
                                  <span className="source-url">{source.url}</span>
                                  <span className="source-date">{source.date}</span>
                                  <span className="source-type">{source.type}</span>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>

                        {message.content.actionItems && (
                          <div className="action-items">
                            <h4>Quick Action Items</h4>
                            <ul>
                              {message.content.actionItems.map((item, index) => (
                                <motion.li
                                  key={index}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: 0.5 + index * 0.1 }}
                                >
                                  {item}
                                </motion.li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="simple-response">{message.content}</div>
                    )}
                  </div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {isLoading && (
          <motion.div
            className="loading-message"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="loading-content">
              <Loader2 className="loading-spinner" size={20} />
              <span>Analyzing ESG data and regulations...</span>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="message-input-container">
        <div className="message-input-box">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask a follow-up question about ESG intelligence..."
            className="message-input"
            rows="1"
          />
          <motion.button
            className="send-btn"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isLoading}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Send size={18} />
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default ConversationView;
