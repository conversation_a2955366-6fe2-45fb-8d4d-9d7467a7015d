import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Send,
  Download,
  ExternalLink,
  TrendingUp,
  AlertTriangle,
  Info,
  Loader2,
  Copy,
  Share2
} from 'lucide-react';
import './ConversationView.css';

const ConversationView = ({ initialQuery, onBack }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (initialQuery) {
      // Add initial user message
      const userMessage = {
        id: 1,
        type: 'user',
        content: initialQuery,
        timestamp: new Date()
      };

      setMessages([userMessage]);
      setIsLoading(true);

      // Simulate AI response after delay
      setTimeout(() => {
        const aiResponse = {
          id: 2,
          type: 'assistant',
          content: generateMockResponse(initialQuery),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsLoading(false);
      }, 2000);
    }
  }, [initialQuery]);

  const generateMockResponse = (query) => {
    return {
      summary: "Based on current ESG intelligence analysis, here are the key findings for your query:",
      keyFindings: [
        {
          type: "regulatory",
          title: "EU Packaging Directive 2024",
          impact: "High",
          description: "New recycled content requirements affecting 40% of Borouge's EU exports. Implementation deadline: January 2025.",
          confidence: 95
        },
        {
          type: "financial",
          title: "CBAM Carbon Costs",
          impact: "Medium",
          description: "Estimated €15-25/tonne additional cost for polyethylene exports to EU starting 2026.",
          confidence: 87
        },
        {
          type: "competitive",
          title: "SABIC Response Strategy",
          impact: "Medium",
          description: "Competitor investing €500M in recycling facilities to meet new requirements.",
          confidence: 92
        }
      ],
      sources: [
        { title: "EU Commission Packaging Directive", url: "europa.eu", date: "2024-01-15", type: "regulation" },
        { title: "CBAM Implementation Guidelines", url: "echa.europa.eu", date: "2024-02-01", type: "regulation" },
        { title: "SABIC Sustainability Report", url: "sabic.com", date: "2024-01-30", type: "corporate" }
      ],
      actionItems: [
        "Review current packaging portfolio compliance",
        "Assess CBAM impact on EU export margins",
        "Evaluate recycling technology investments"
      ]
    };
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        type: 'user',
        content: newMessage,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);
      setNewMessage('');
      setIsLoading(true);

      // Simulate AI response
      setTimeout(() => {
        const aiResponse = {
          id: messages.length + 2,
          type: 'assistant',
          content: generateMockResponse(newMessage),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsLoading(false);
      }, 1500);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const exportToPDF = () => {
    // Mock export functionality
    console.log('Exporting conversation to PDF...');
    // In real implementation, use jsPDF or similar
  };

  const copyMessage = (content) => {
    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));
  };

  return (
    <motion.div
      className="conversation-view"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="conversation-header">
        <motion.button
          className="back-btn"
          onClick={onBack}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft size={20} />
          Back to Search
        </motion.button>

        <div className="conversation-actions">
          <motion.button
            className="action-btn"
            onClick={exportToPDF}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download size={18} />
            Export
          </motion.button>
          <motion.button
            className="action-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Share2 size={18} />
            Share
          </motion.button>
        </div>
      </div>

      {/* Messages */}
      <div className="messages-container">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              className={`message ${message.type}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {message.type === 'user' ? (
                <div className="user-message">
                  <div className="message-content">{message.content}</div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              ) : (
                <div className="ai-message">
                  <div className="ai-response">
                    {typeof message.content === 'object' ? (
                      <div className="intelligence-report">
                        <div className="report-header">
                          <h3>ESG Intelligence Report</h3>
                          <button
                            className="copy-btn"
                            onClick={() => copyMessage(message.content)}
                          >
                            <Copy size={16} />
                          </button>
                        </div>

                        <p className="report-summary">{message.content.summary}</p>

                        <div className="key-findings">
                          <h4>Key Findings</h4>
                          {message.content.keyFindings.map((finding, index) => (
                            <motion.div
                              key={index}
                              className="finding-card"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                            >
                              <div className="finding-header">
                                <div className="finding-icon">
                                  {finding.type === 'regulatory' && <AlertTriangle size={16} />}
                                  {finding.type === 'financial' && <TrendingUp size={16} />}
                                  {finding.type === 'competitive' && <Info size={16} />}
                                </div>
                                <div className="finding-title">{finding.title}</div>
                                <div className={`impact-badge ${finding.impact.toLowerCase()}`}>
                                  {finding.impact} Impact
                                </div>
                              </div>
                              <p className="finding-description">{finding.description}</p>
                              <div className="confidence-bar">
                                <div className="confidence-label">Confidence: {finding.confidence}%</div>
                                <div className="confidence-progress">
                                  <motion.div
                                    className="confidence-fill"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${finding.confidence}%` }}
                                    transition={{ duration: 1, delay: 0.5 }}
                                  />
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>

                        <div className="sources-section">
                          <h4>Sources</h4>
                          <div className="sources-grid">
                            {message.content.sources.map((source, index) => (
                              <motion.div
                                key={index}
                                className="source-card"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 + index * 0.1 }}
                              >
                                <div className="source-header">
                                  <ExternalLink size={14} />
                                  <span className="source-title">{source.title}</span>
                                </div>
                                <div className="source-meta">
                                  <span className="source-url">{source.url}</span>
                                  <span className="source-date">{source.date}</span>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>

                        <div className="action-items">
                          <h4>Recommended Actions</h4>
                          <ul>
                            {message.content.actionItems.map((item, index) => (
                              <motion.li
                                key={index}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.5 + index * 0.1 }}
                              >
                                {item}
                              </motion.li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    ) : (
                      <div className="simple-response">{message.content}</div>
                    )}
                  </div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {isLoading && (
          <motion.div
            className="loading-message"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="loading-content">
              <Loader2 className="loading-spinner" size={20} />
              <span>Analyzing ESG data and regulations...</span>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="message-input-container">
        <div className="message-input-box">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask a follow-up question about ESG intelligence..."
            className="message-input"
            rows="1"
          />
          <motion.button
            className="send-btn"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isLoading}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Send size={18} />
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default ConversationView;
