{"ast": null, "code": "import { camelToDash } from '../../dom/utils/camel-to-dash.mjs';\nimport { renderHTML } from '../../html/utils/render.mjs';\nimport { camelCaseAttributes } from './camel-case-attrs.mjs';\nfunction renderSVG(element, renderState, _styleProp, projection) {\n  renderHTML(element, renderState, undefined, projection);\n  for (const key in renderState.attrs) {\n    element.setAttribute(!camelCaseAttributes.has(key) ? camelToDash(key) : key, renderState.attrs[key]);\n  }\n}\nexport { renderSVG };", "map": {"version": 3, "names": ["camelToDash", "renderHTML", "camelCaseAttributes", "renderSVG", "element", "renderState", "_styleProp", "projection", "undefined", "key", "attrs", "setAttribute", "has"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs"], "sourcesContent": ["import { camelToDash } from '../../dom/utils/camel-to-dash.mjs';\nimport { renderHTML } from '../../html/utils/render.mjs';\nimport { camelCaseAttributes } from './camel-case-attrs.mjs';\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    renderHTML(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!camelCaseAttributes.has(key) ? camelToDash(key) : key, renderState.attrs[key]);\n    }\n}\n\nexport { renderSVG };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,mBAAmB,QAAQ,wBAAwB;AAE5D,SAASC,SAASA,CAACC,OAAO,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAE;EAC7DN,UAAU,CAACG,OAAO,EAAEC,WAAW,EAAEG,SAAS,EAAED,UAAU,CAAC;EACvD,KAAK,MAAME,GAAG,IAAIJ,WAAW,CAACK,KAAK,EAAE;IACjCN,OAAO,CAACO,YAAY,CAAC,CAACT,mBAAmB,CAACU,GAAG,CAACH,GAAG,CAAC,GAAGT,WAAW,CAACS,GAAG,CAAC,GAAGA,GAAG,EAAEJ,WAAW,CAACK,KAAK,CAACD,GAAG,CAAC,CAAC;EACxG;AACJ;AAEA,SAASN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}