{"ast": null, "code": "import { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\nfunction handoffOptimizedAppearAnimation(elementId, valueName, frame) {\n  const storeId = appearStoreId(elementId, valueName);\n  const optimisedAnimation = appearAnimationStore.get(storeId);\n  if (!optimisedAnimation) {\n    return null;\n  }\n  const {\n    animation,\n    startTime\n  } = optimisedAnimation;\n  function cancelAnimation() {\n    window.MotionCancelOptimisedAnimation?.(elementId, valueName, frame);\n  }\n  /**\n   * We can cancel the animation once it's finished now that we've synced\n   * with <PERSON>.\n   *\n   * Prefer onfinish over finished as onfinish is backwards compatible with\n   * older browsers.\n   */\n  animation.onfinish = cancelAnimation;\n  if (startTime === null || window.MotionHandoffIsComplete?.(elementId)) {\n    /**\n     * If the startTime is null, this animation is the Paint Ready detection animation\n     * and we can cancel it immediately without handoff.\n     *\n     * Or if we've already handed off the animation then we're now interrupting it.\n     * In which case we need to cancel it.\n     */\n    cancelAnimation();\n    return null;\n  } else {\n    return startTime;\n  }\n}\nexport { handoffOptimizedAppearAnimation };", "map": {"version": 3, "names": ["appearAnimationStore", "appearStoreId", "handoffOptimizedAppearAnimation", "elementId", "valueName", "frame", "storeId", "optimisedAnimation", "get", "animation", "startTime", "cancelAnimation", "window", "MotionCancelOptimisedAnimation", "onfinish", "MotionHandoffIsComplete"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/handoff.mjs"], "sourcesContent": ["import { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\nfunction handoffOptimizedAppearAnimation(elementId, valueName, frame) {\n    const storeId = appearStoreId(elementId, valueName);\n    const optimisedAnimation = appearAnimationStore.get(storeId);\n    if (!optimisedAnimation) {\n        return null;\n    }\n    const { animation, startTime } = optimisedAnimation;\n    function cancelAnimation() {\n        window.MotionCancelOptimisedAnimation?.(elementId, valueName, frame);\n    }\n    /**\n     * We can cancel the animation once it's finished now that we've synced\n     * with <PERSON>.\n     *\n     * Prefer onfinish over finished as onfinish is backwards compatible with\n     * older browsers.\n     */\n    animation.onfinish = cancelAnimation;\n    if (startTime === null || window.MotionHandoffIsComplete?.(elementId)) {\n        /**\n         * If the startTime is null, this animation is the Paint Ready detection animation\n         * and we can cancel it immediately without handoff.\n         *\n         * Or if we've already handed off the animation then we're now interrupting it.\n         * In which case we need to cancel it.\n         */\n        cancelAnimation();\n        return null;\n    }\n    else {\n        return startTime;\n    }\n}\n\nexport { handoffOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,aAAa;AAClD,SAASC,aAAa,QAAQ,gBAAgB;AAE9C,SAASC,+BAA+BA,CAACC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAClE,MAAMC,OAAO,GAAGL,aAAa,CAACE,SAAS,EAAEC,SAAS,CAAC;EACnD,MAAMG,kBAAkB,GAAGP,oBAAoB,CAACQ,GAAG,CAACF,OAAO,CAAC;EAC5D,IAAI,CAACC,kBAAkB,EAAE;IACrB,OAAO,IAAI;EACf;EACA,MAAM;IAAEE,SAAS;IAAEC;EAAU,CAAC,GAAGH,kBAAkB;EACnD,SAASI,eAAeA,CAAA,EAAG;IACvBC,MAAM,CAACC,8BAA8B,GAAGV,SAAS,EAAEC,SAAS,EAAEC,KAAK,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,SAAS,CAACK,QAAQ,GAAGH,eAAe;EACpC,IAAID,SAAS,KAAK,IAAI,IAAIE,MAAM,CAACG,uBAAuB,GAAGZ,SAAS,CAAC,EAAE;IACnE;AACR;AACA;AACA;AACA;AACA;AACA;IACQQ,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC,MACI;IACD,OAAOD,SAAS;EACpB;AACJ;AAEA,SAASR,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}