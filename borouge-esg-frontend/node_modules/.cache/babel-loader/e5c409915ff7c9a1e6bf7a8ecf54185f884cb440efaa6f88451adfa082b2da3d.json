{"ast": null, "code": "function getValueTransition(transition, key) {\n  return transition?.[key] ?? transition?.[\"default\"] ?? transition;\n}\nexport { getValueTransition };", "map": {"version": 3, "names": ["getValueTransition", "transition", "key"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs"], "sourcesContent": ["function getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\nexport { getValueTransition };\n"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,UAAU,EAAEC,GAAG,EAAE;EACzC,OAAQD,UAAU,GAAGC,GAAG,CAAC,IACrBD,UAAU,GAAG,SAAS,CAAC,IACvBA,UAAU;AAClB;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}