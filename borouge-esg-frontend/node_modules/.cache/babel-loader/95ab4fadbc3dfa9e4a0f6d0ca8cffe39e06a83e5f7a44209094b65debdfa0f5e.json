{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowLeft, Send, Download, ExternalLink, TrendingUp, AlertTriangle, Info, Loader2, Copy, Share2, ChevronDown, ChevronUp, Target, DollarSign, Clock, Users } from 'lucide-react';\nimport './ConversationView.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationView = ({\n  initialQuery,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const responseHeaderRef = useRef(null);\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 2000);\n    }\n  }, [initialQuery]);\n  const generateMockResponse = query => {\n    const lowerQuery = query.toLowerCase();\n\n    // Generate multiple prioritized articles based on query\n    if (lowerQuery.includes('eu') || lowerQuery.includes('europe') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n      return generatePrioritizedArticles('eu_regulation', query);\n    } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon') || lowerQuery.includes('border')) {\n      return generatePrioritizedArticles('cbam', query);\n    } else if (lowerQuery.includes('esg') || lowerQuery.includes('comprehensive')) {\n      return generatePrioritizedArticles('comprehensive_esg', query);\n    } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycl')) {\n      return generatePrioritizedArticles('circular_economy', query);\n    } else if (lowerQuery.includes('competitor') || lowerQuery.includes('sabic') || lowerQuery.includes('dow')) {\n      return generatePrioritizedArticles('competitive', query);\n    } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n      return generatePrioritizedArticles('market_trends', query);\n    } else {\n      return generatePrioritizedArticles('general', query); // Default response\n    }\n  };\n  const generatePrioritizedArticles = (category, originalQuery) => {\n    const articles = [];\n    const usedArticleTypes = new Set(); // Track used article types to prevent duplicates\n\n    // Priority 1: Critical regulatory compliance (highest priority)\n    // Priority 2: High financial impact (€100M+ revenue/cost impact)\n    // Priority 3: Immediate competitive threats\n    // Priority 4: Time-sensitive opportunities\n    // Priority 5: Medium-term strategic considerations (lowest priority)\n\n    const addUniqueArticle = (articleGenerator, articleType, priority, priorityLabel) => {\n      if (!usedArticleTypes.has(articleType)) {\n        const article = articleGenerator();\n\n        // Normalize article structure to ensure consistency\n        const normalizedArticle = {\n          ...article,\n          priority,\n          priorityLabel,\n          articleId: articleType,\n          // Ensure all articles have detailedFindings for consistent rendering\n          detailedFindings: article.detailedFindings || article.keyFindings || [],\n          // Ensure all articles have sources\n          sources: article.sources || [],\n          // Ensure all articles have topFindings for the summary section\n          topFindings: article.topFindings || (article.keyFindings ? article.keyFindings.slice(0, 3) : [])\n        };\n        articles.push(normalizedArticle);\n        usedArticleTypes.add(articleType);\n      }\n    };\n    switch (category) {\n      case 'eu_regulation':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n        break;\n      case 'cbam':\n        addUniqueArticle(generateCBAMReport, 'cbam', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        break;\n      case 'comprehensive_esg':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateComprehensiveESGReport, 'comprehensive_esg', 2, 'High Financial Impact');\n        break;\n      case 'circular_economy':\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        break;\n      case 'competitive':\n        addUniqueArticle(generateCompetitorReport, 'competitive', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        break;\n      case 'market_trends':\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 2, 'High Financial Impact');\n        break;\n      default:\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n    }\n\n    // Sort articles by priority (1 = highest, 5 = lowest)\n    articles.sort((a, b) => a.priority - b.priority);\n    return {\n      responseType: 'multi_article',\n      originalQuery: originalQuery,\n      totalArticles: articles.length,\n      articles: articles\n    };\n  };\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [{\n        type: \"regulatory\",\n        title: \"Mandatory Recycled Content Requirements\",\n        impact: \"Critical\",\n        description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n        action: \"Secure recycling partnerships immediately\"\n      }, {\n        type: \"financial\",\n        title: \"Investment Requirements\",\n        impact: \"High\",\n        description: \"$800M-1.2B needed for compliance infrastructure\",\n        action: \"Establish dedicated compliance budget\"\n      }, {\n        type: \"competitive\",\n        title: \"SABIC Competitive Threat\",\n        impact: \"High\",\n        description: \"Risk losing 15-20% EU market share to competitors\",\n        action: \"Accelerate sustainable product development\"\n      }],\n      detailedFindings: [{\n        type: \"regulatory\",\n        title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n        details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n        confidence: 98,\n        timeline: \"Implementation: Jan 2025, Full compliance: 2030\",\n        isBorogueSpecific: false\n      }, {\n        type: \"financial\",\n        title: \"Compliance Investment Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n        details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2027\",\n        isBorogueSpecific: false\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Landscape Shift\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n        details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n        confidence: 92,\n        timeline: \"Competitive threat: Immediate\",\n        isBorogueSpecific: false\n      }, {\n        type: \"market\",\n        title: \"Borouge Strategic Partnership with ALPLA Group\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Exclusive partnership opportunity with ALPLA Group to secure 40% of required recycled content supply for EU compliance.\",\n        details: \"Strategic alliance would provide Borouge with preferential access to high-quality recycled polyolefins from ALPLA's European network, ensuring compliance while creating competitive moat against SABIC and Dow.\",\n        confidence: 94,\n        timeline: \"Partnership agreement needed within 6 months\",\n        isBorogueSpecific: true\n      }, {\n        type: \"technology\",\n        title: \"Borouge Advanced Chemical Recycling Initiative\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Proprietary chemical recycling technology development could position Borouge as market leader in circular polyolefins.\",\n        details: \"Investment in advanced pyrolysis and depolymerization technologies would enable Borouge to process mixed plastic waste into virgin-quality feedstock, creating new revenue streams worth €300-500M annually by 2030.\",\n        confidence: 87,\n        timeline: \"36 months to commercial deployment\",\n        isBorogueSpecific: true\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [{\n        priority: \"Critical\",\n        action: \"Form EU Compliance Task Force\",\n        timeline: \"Next 30 days\",\n        investment: \"$5M\",\n        description: \"Immediate action team to coordinate regulatory response\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Recycling Partnerships\",\n        timeline: \"6 months\",\n        investment: \"$200-300M\",\n        description: \"Lock in technology partnerships before competitors\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line\",\n        timeline: \"12 months\",\n        investment: \"$150M\",\n        description: \"Develop premium recycled content products\"\n      }],\n      allRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish EU Regulatory Compliance Task Force\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$5M\",\n        description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Chemical Recycling Technology Partnerships\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$200-300M\",\n        description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line Development\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$150M\",\n        description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n      }, {\n        priority: \"Medium\",\n        action: \"Establish European Recycling Hub\",\n        timeline: \"24 months (Q4 2025)\",\n        investment: \"$400-500M\",\n        description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n      }],\n      competitiveBenchmarking: [{\n        company: \"SABIC\",\n        strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n        advantage: \"First-mover in chemical recycling, strong EU presence\",\n        weakness: \"Higher cost base, limited feedstock security\"\n      }, {\n        company: \"Dow\",\n        strategy: \"Advanced recycling partnerships, circular design principles\",\n        advantage: \"Technology leadership, established partnerships\",\n        weakness: \"Focus on specialty applications, limited commodity exposure\"\n      }, {\n        company: \"LyondellBasell\",\n        strategy: \"Molecular recycling technology, circular economy solutions\",\n        advantage: \"Integrated technology development, scale advantages\",\n        weakness: \"Limited EU manufacturing footprint\"\n      }],\n      sources: [{\n        title: \"EU Packaging & Packaging Waste Regulation\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2024-01-15\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Environment Agency Circular Economy Report\",\n        url: \"eea.europa.eu\",\n        date: \"2024-02-01\",\n        type: \"research\",\n        confidence: \"High\"\n      }, {\n        title: \"SABIC Circular Economy Strategy Update\",\n        url: \"sabic.com\",\n        date: \"2024-01-30\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"Plastics Europe Market Data 2024\",\n        url: \"plasticseurope.org\",\n        date: \"2024-02-15\",\n        type: \"industry\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey Circular Economy in Chemicals\",\n        url: \"mckinsey.com\",\n        date: \"2024-01-20\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }, {\n        title: \"Wood Mackenzie Petrochemicals Outlook\",\n        url: \"woodmac.com\",\n        date: \"2024-02-10\",\n        type: \"market\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [{\n        type: \"financial\",\n        title: \"Direct CBAM Cost Impact\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n        details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n        confidence: 92,\n        timeline: \"Implementation: January 2026\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Advantage Opportunity\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n        details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n        confidence: 88,\n        timeline: \"Advantage period: 2026-2035\"\n      }, {\n        type: \"technology\",\n        title: \"Carbon Reduction Investment Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n        details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2030\"\n      }, {\n        type: \"regulatory\",\n        title: \"CBAM Reporting and Verification Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n        details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n        confidence: 95,\n        timeline: \"Reporting starts: October 2023\"\n      }, {\n        type: \"market\",\n        title: \"Premium Low-Carbon Product Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n        details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n        confidence: 78,\n        timeline: \"Market development: 2025-2030\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Implement Comprehensive Carbon Accounting System\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$10M\",\n        description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n      }, {\n        priority: \"High\",\n        action: \"Accelerate Renewable Energy Integration\",\n        timeline: \"18 months (Q3 2025)\",\n        investment: \"$200M\",\n        description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n      }, {\n        priority: \"High\",\n        action: \"Develop Low-Carbon Product Certification\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$5M\",\n        description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n      }],\n      sources: [{\n        title: \"EU CBAM Regulation 2023/956\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2023-05-17\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Commission CBAM Implementation Guide\",\n        url: \"taxation-customs.ec.europa.eu\",\n        date: \"2024-01-10\",\n        type: \"guidance\",\n        confidence: \"Official\"\n      }, {\n        title: \"IEA Petrochemicals Carbon Intensity Database\",\n        url: \"iea.org\",\n        date: \"2024-02-05\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [{\n        type: \"environmental\",\n        title: \"Decarbonization Pathway Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n        details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n        confidence: 90,\n        timeline: \"Transformation period: 2024-2050\"\n      }, {\n        type: \"social\",\n        title: \"UAE Emiratization and Skills Development\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n        details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n        confidence: 85,\n        timeline: \"Achievement target: 2030\"\n      }, {\n        type: \"governance\",\n        title: \"ESG Reporting and Transparency Enhancement\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n        details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n        confidence: 88,\n        timeline: \"Full compliance: 2025\"\n      }, {\n        type: \"financial\",\n        title: \"Sustainable Finance and Green Bonds Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n        details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n        confidence: 82,\n        timeline: \"Financing window: 2024-2027\"\n      }, {\n        type: \"technology\",\n        title: \"Digital ESG Management Platform\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n        details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n        confidence: 75,\n        timeline: \"Implementation: 2024-2025\"\n      }],\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish Chief Sustainability Officer Role\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$2M\",\n        description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Comprehensive Decarbonization Program\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$500M\",\n        description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n      }, {\n        priority: \"High\",\n        action: \"Implement Advanced Emiratization Strategy\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$100M\",\n        description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n      }],\n      sources: [{\n        title: \"UAE Vision 2071 Strategic Framework\",\n        url: \"government.ae\",\n        date: \"2023-12-01\",\n        type: \"policy\",\n        confidence: \"Official\"\n      }, {\n        title: \"ADNOC Sustainability Strategy 2030\",\n        url: \"adnoc.ae\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey ESG in Chemicals Industry\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }]\n    };\n  };\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Recycled Polyethylene Market Growth\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n        details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n        confidence: 88,\n        timeline: \"Market expansion: 2024-2030\"\n      }],\n      sources: [{\n        title: \"Ellen MacArthur Foundation Circular Economy Report\",\n        url: \"ellenmacarthurfoundation.org\",\n        date: \"2024-01-10\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [{\n        type: \"competitive\",\n        title: \"SABIC Circular Economy Leadership\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n        details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n        confidence: 95,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      sources: [{\n        title: \"SABIC Sustainability Strategy 2030\",\n        url: \"sabic.com\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Sustainable Packaging Demand Surge\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n        details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n        confidence: 90,\n        timeline: \"Market shift: 2024-2027\"\n      }],\n      sources: [{\n        title: \"McKinsey Sustainable Packaging Report\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        var _responseContent$arti, _responseContent$arti2;\n        const responseContent = generateMockResponse(newMessage);\n        console.log('=== Message Creation Debug ===');\n        console.log('Response articles:', ((_responseContent$arti = responseContent.articles) === null || _responseContent$arti === void 0 ? void 0 : _responseContent$arti.length) || 0);\n        console.log('Article IDs:', ((_responseContent$arti2 = responseContent.articles) === null || _responseContent$arti2 === void 0 ? void 0 : _responseContent$arti2.map(a => a.articleId)) || []);\n        console.log('==============================');\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: responseContent,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 1500);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n  const copyMessage = content => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"conversation-view\",\n    initial: {\n      opacity: 0,\n      x: 20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    exit: {\n      opacity: 0,\n      x: -20\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversation-header\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"back-btn\",\n        onClick: onBack,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), \"Back to Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-actions\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          onClick: exportToPDF,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Share2, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this), \"Share\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.map((message, messageIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `message ${message.type}`,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-response\",\n              children: typeof message.content === 'object' ? message.content.responseType === 'multi_article' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"multi-article-response\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"response-header\",\n                  ref: messageIndex === messages.length - 1 ? responseHeaderRef : null,\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: [\"Intelligence Analysis: \", message.content.originalQuery]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"articles-summary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"articles-count\",\n                      children: [message.content.totalArticles, \" Articles Found\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"priority-note\",\n                      children: \"Sorted by criticality and business impact\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 741,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 27\n                }, this), message.content.articles.map((article, articleIndex) => {\n                  var _article$keyFindings, _article$sources, _article$sources2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"article-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"article-priority-header\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"priority-badge-large\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"priority-number\",\n                          children: [\"#\", articleIndex + 1]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 749,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"priority-label\",\n                          children: article.priorityLabel\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 750,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"intelligence-report simplified\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"report-header\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"report-title-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            children: article.reportType || 'ESG Intelligence Report'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 757,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"report-actions\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"copy-btn secondary\",\n                              onClick: () => copyMessage(article),\n                              title: \"Copy article\",\n                              children: /*#__PURE__*/_jsxDEV(Copy, {\n                                size: 14\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 764,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 759,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 758,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 756,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 33\n                      }, this), (article.problem || article.executiveSummary) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"problem-solution-summary\",\n                        children: [article.problem && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"problem-statement\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"problem-icon\",\n                            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 20\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 776,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 775,\n                            columnNumber: 41\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"problem-content\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                              children: \"Business Challenge\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 779,\n                              columnNumber: 43\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              children: article.problem\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 780,\n                              columnNumber: 43\n                            }, this), article.impact && article.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"impact-highlight\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"impact-text\",\n                                children: article.impact\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 783,\n                                columnNumber: 47\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"urgency-text\",\n                                children: article.urgency\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 784,\n                                columnNumber: 47\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 782,\n                              columnNumber: 45\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 778,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 774,\n                          columnNumber: 39\n                        }, this), article.opportunity && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"opportunity-statement\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"opportunity-icon\",\n                            children: /*#__PURE__*/_jsxDEV(Target, {\n                              size: 20\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 794,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 793,\n                            columnNumber: 41\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"opportunity-content\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                              children: \"Market Opportunity\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 797,\n                              columnNumber: 43\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              children: article.opportunity\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 798,\n                              columnNumber: 43\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 796,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 792,\n                          columnNumber: 39\n                        }, this), article.executiveSummary && !article.problem && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"executive-summary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            children: \"Executive Summary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 805,\n                            columnNumber: 41\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: article.executiveSummary\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 806,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 804,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"key-insights\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Critical Findings\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 814,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"insights-grid\",\n                          children: (article.topFindings || ((_article$keyFindings = article.keyFindings) === null || _article$keyFindings === void 0 ? void 0 : _article$keyFindings.slice(0, 3)) || []).map((finding, index) => {\n                            var _finding$impact, _finding$impact2;\n                            return /*#__PURE__*/_jsxDEV(motion.div, {\n                              className: \"insight-card\",\n                              initial: {\n                                opacity: 0,\n                                y: 10\n                              },\n                              animate: {\n                                opacity: 1,\n                                y: 0\n                              },\n                              transition: {\n                                delay: index * 0.1\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"insight-header\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: `impact-indicator ${(_finding$impact = finding.impact) === null || _finding$impact === void 0 ? void 0 : _finding$impact.toLowerCase()}`,\n                                  children: [finding.impact === 'Critical' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                    size: 16\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 826,\n                                    columnNumber: 79\n                                  }, this), finding.impact === 'High' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                    size: 16\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 827,\n                                    columnNumber: 75\n                                  }, this), finding.impact === 'Medium' && /*#__PURE__*/_jsxDEV(Info, {\n                                    size: 16\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 828,\n                                    columnNumber: 77\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 825,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: `impact-label ${(_finding$impact2 = finding.impact) === null || _finding$impact2 === void 0 ? void 0 : _finding$impact2.toLowerCase()}`,\n                                  children: finding.impact\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 830,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 824,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                                children: finding.title\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 834,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                children: finding.description\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 835,\n                                columnNumber: 41\n                              }, this), finding.action && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"quick-action\",\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Action:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 838,\n                                  columnNumber: 45\n                                }, this), \" \", finding.action]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 837,\n                                columnNumber: 43\n                              }, this)]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 817,\n                              columnNumber: 39\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 815,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"detailed-sections\",\n                        children: [article.detailedFindings && article.detailedFindings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"collapsible-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"section-toggle\",\n                            onClick: () => toggleSection(`${message.id}-${articleIndex}`, 'detailed-findings'),\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Detailed Analysis\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 857,\n                              columnNumber: 41\n                            }, this), expandedSections[`${message.id}-${articleIndex}-detailed-findings`] ? /*#__PURE__*/_jsxDEV(ChevronUp, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 859,\n                              columnNumber: 43\n                            }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 859,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 853,\n                            columnNumber: 39\n                          }, this), expandedSections[`${message.id}-${articleIndex}-detailed-findings`] && /*#__PURE__*/_jsxDEV(motion.div, {\n                            className: \"section-content\",\n                            initial: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            animate: {\n                              opacity: 1,\n                              height: 'auto'\n                            },\n                            exit: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"detailed-findings\",\n                              children: (article.detailedFindings || []).map((finding, index) => {\n                                var _finding$impact3;\n                                return /*#__PURE__*/_jsxDEV(motion.div, {\n                                  className: `finding-card ${finding.isBorogueSpecific ? 'borouge-recommendation' : ''}`,\n                                  \"data-type\": finding.type,\n                                  initial: {\n                                    opacity: 0,\n                                    x: -20\n                                  },\n                                  animate: {\n                                    opacity: 1,\n                                    x: 0\n                                  },\n                                  transition: {\n                                    delay: index * 0.1\n                                  },\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"finding-header\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"finding-icon\",\n                                      style: {\n                                        background: finding.type === 'regulatory' ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)' : finding.type === 'financial' ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)' : finding.type === 'competitive' ? 'linear-gradient(135deg, #0066cc 0%, #3b82f6 100%)' : finding.type === 'market' ? 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)' : finding.type === 'technology' ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)' : finding.type === 'environmental' ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)' : finding.type === 'social' ? 'linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)' : finding.type === 'governance' ? 'linear-gradient(135deg, #4338ca 0%, #6366f1 100%)' : 'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)'\n                                      },\n                                      children: [finding.type === 'regulatory' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 895,\n                                        columnNumber: 79\n                                      }, this), finding.type === 'financial' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 896,\n                                        columnNumber: 78\n                                      }, this), finding.type === 'competitive' && /*#__PURE__*/_jsxDEV(Users, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 897,\n                                        columnNumber: 80\n                                      }, this), finding.type === 'market' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 898,\n                                        columnNumber: 75\n                                      }, this), finding.type === 'technology' && /*#__PURE__*/_jsxDEV(Info, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 899,\n                                        columnNumber: 79\n                                      }, this), finding.type === 'environmental' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 900,\n                                        columnNumber: 82\n                                      }, this), finding.type === 'social' && /*#__PURE__*/_jsxDEV(Users, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 901,\n                                        columnNumber: 75\n                                      }, this), finding.type === 'governance' && /*#__PURE__*/_jsxDEV(Info, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 902,\n                                        columnNumber: 79\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 881,\n                                      columnNumber: 43\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"finding-title\",\n                                      children: finding.title\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 904,\n                                      columnNumber: 43\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"finding-badges\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                        className: `impact-badge ${(_finding$impact3 = finding.impact) === null || _finding$impact3 === void 0 ? void 0 : _finding$impact3.toLowerCase()}`,\n                                        children: [finding.impact, \" Impact\"]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 906,\n                                        columnNumber: 45\n                                      }, this), finding.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                                        className: `urgency-badge ${finding.urgency.toLowerCase()}`,\n                                        children: finding.urgency\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 910,\n                                        columnNumber: 47\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 905,\n                                      columnNumber: 43\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 880,\n                                    columnNumber: 41\n                                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                    className: \"finding-description\",\n                                    children: finding.description\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 916,\n                                    columnNumber: 41\n                                  }, this), finding.details && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"finding-details\",\n                                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                                      children: finding.details\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 919,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 918,\n                                    columnNumber: 43\n                                  }, this), finding.timeline && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"finding-timeline\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                      children: \"Timeline:\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 924,\n                                      columnNumber: 45\n                                    }, this), \" \", finding.timeline]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 923,\n                                    columnNumber: 43\n                                  }, this), finding.confidence && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"confidence-bar\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"confidence-label\",\n                                      children: [\"Confidence: \", finding.confidence, \"%\"]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 929,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"confidence-progress\",\n                                      children: /*#__PURE__*/_jsxDEV(motion.div, {\n                                        className: \"confidence-fill\",\n                                        initial: {\n                                          width: 0\n                                        },\n                                        animate: {\n                                          width: `${finding.confidence}%`\n                                        },\n                                        transition: {\n                                          duration: 1,\n                                          delay: 0.5\n                                        }\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 931,\n                                        columnNumber: 47\n                                      }, this)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 930,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 928,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, index, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 872,\n                                  columnNumber: 39\n                                }, this);\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 870,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 864,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"collapsible-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"section-toggle\",\n                            onClick: () => toggleSection(`${message.id}-${articleIndex}`, 'sources'),\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: [\"Sources & References (\", ((_article$sources = article.sources) === null || _article$sources === void 0 ? void 0 : _article$sources.length) || 0, \")\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 954,\n                              columnNumber: 39\n                            }, this), expandedSections[`${message.id}-${articleIndex}-sources`] ? /*#__PURE__*/_jsxDEV(ChevronUp, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 956,\n                              columnNumber: 41\n                            }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 956,\n                              columnNumber: 67\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 950,\n                            columnNumber: 37\n                          }, this), expandedSections[`${message.id}-${articleIndex}-sources`] && /*#__PURE__*/_jsxDEV(motion.div, {\n                            className: \"section-content\",\n                            initial: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            animate: {\n                              opacity: 1,\n                              height: 'auto'\n                            },\n                            exit: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"sources-section\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"sources-grid\",\n                                children: (_article$sources2 = article.sources) === null || _article$sources2 === void 0 ? void 0 : _article$sources2.map((source, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"source-card\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"source-header\",\n                                    children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                                      size: 14\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 972,\n                                      columnNumber: 51\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-title\",\n                                      children: source.title\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 973,\n                                      columnNumber: 51\n                                    }, this), source.confidence && /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: `source-confidence ${source.confidence.toLowerCase()}`,\n                                      children: source.confidence\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 975,\n                                      columnNumber: 53\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 971,\n                                    columnNumber: 49\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"source-meta\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-url\",\n                                      children: source.url\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 981,\n                                      columnNumber: 51\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-date\",\n                                      children: source.date\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 982,\n                                      columnNumber: 51\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-type\",\n                                      children: source.type\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 983,\n                                      columnNumber: 51\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 980,\n                                    columnNumber: 49\n                                  }, this)]\n                                }, index, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 970,\n                                  columnNumber: 47\n                                }, this))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 968,\n                                columnNumber: 43\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 967,\n                              columnNumber: 41\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 961,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 949,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 31\n                    }, this)]\n                  }, article.articleId || articleIndex, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 29\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"intelligence-report simplified\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"report-title-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: message.content.reportType || 'ESG Intelligence Report'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1001,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"report-actions\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"copy-btn secondary\",\n                        onClick: () => copyMessage(message.content),\n                        title: \"Copy report\",\n                        children: /*#__PURE__*/_jsxDEV(Copy, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1008,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1003,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1002,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1000,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"simple-response\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-message\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(Loader2, {\n            className: \"loading-spinner\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Analyzing ESG data and regulations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-input-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-input-box\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask a follow-up question about ESG intelligence...\",\n          className: \"message-input\",\n          rows: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"send-btn\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || isLoading,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1063,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1056,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1046,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 669,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationView, \"0epH/2K6gs9Tzy97Gdji0lsw1cE=\");\n_c = ConversationView;\nexport default ConversationView;\nvar _c;\n$RefreshReg$(_c, \"ConversationView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "ArrowLeft", "Send", "Download", "ExternalLink", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Loader2", "Copy", "Share2", "ChevronDown", "ChevronUp", "Target", "DollarSign", "Clock", "Users", "jsxDEV", "_jsxDEV", "ConversationView", "initialQuery", "onBack", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "isLoading", "setIsLoading", "expandedSections", "setExpandedSections", "messagesEndRef", "responseHeaderRef", "toggleSection", "messageId", "section", "prev", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "userMessage", "id", "type", "content", "timestamp", "Date", "setTimeout", "aiResponse", "generateMockResponse", "block", "inline", "query", "lowerQuery", "toLowerCase", "includes", "generatePrioritizedArticles", "category", "originalQuery", "articles", "usedArticleTypes", "Set", "addUniqueArticle", "articleGenerator", "articleType", "priority", "priority<PERSON>abel", "has", "article", "normalizedArticle", "articleId", "detailedFindings", "keyFindings", "sources", "topFindings", "slice", "push", "add", "generateEURegulationReport", "generateCBAMReport", "generateComprehensiveESGReport", "generateCircularEconomyReport", "generateCompetitorReport", "generateMarketTrendsReport", "sort", "a", "b", "responseType", "totalArticles", "length", "reportType", "problem", "impact", "urgency", "opportunity", "title", "description", "action", "details", "confidence", "timeline", "isBorogueSpecific", "marketImpact", "revenueAtRisk", "investmentRequired", "timelineForCompliance", "marketOpportunity", "riskAssessment", "high", "medium", "low", "nextSteps", "investment", "allRecommendations", "competitiveBenchmarking", "company", "strategy", "advantage", "weakness", "url", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strategicRecommendations", "handleSendMessage", "trim", "_responseContent$arti", "_responseContent$arti2", "responseContent", "console", "log", "map", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "exportToPDF", "copyMessage", "navigator", "clipboard", "writeText", "JSON", "stringify", "div", "className", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "children", "button", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "messageIndex", "y", "toLocaleTimeString", "hour", "minute", "ref", "articleIndex", "_article$keyFindings", "_article$sources", "_article$sources2", "finding", "index", "_finding$impact", "_finding$impact2", "delay", "height", "_finding$impact3", "style", "background", "width", "source", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ArrowLeft,\n  Send,\n  Download,\n  ExternalLink,\n  TrendingUp,\n  AlertTriangle,\n  Info,\n  Loader2,\n  Copy,\n  Share2,\n  ChevronDown,\n  ChevronUp,\n  Target,\n  DollarSign,\n  Clock,\n  Users\n} from 'lucide-react';\nimport './ConversationView.css';\n\nconst ConversationView = ({ initialQuery, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const responseHeaderRef = useRef(null);\n\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 2000);\n    }\n  }, [initialQuery]);\n\n  const generateMockResponse = (query) => {\n    const lowerQuery = query.toLowerCase();\n\n    // Generate multiple prioritized articles based on query\n    if (lowerQuery.includes('eu') || lowerQuery.includes('europe') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n      return generatePrioritizedArticles('eu_regulation', query);\n    } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon') || lowerQuery.includes('border')) {\n      return generatePrioritizedArticles('cbam', query);\n    } else if (lowerQuery.includes('esg') || lowerQuery.includes('comprehensive')) {\n      return generatePrioritizedArticles('comprehensive_esg', query);\n    } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycl')) {\n      return generatePrioritizedArticles('circular_economy', query);\n    } else if (lowerQuery.includes('competitor') || lowerQuery.includes('sabic') || lowerQuery.includes('dow')) {\n      return generatePrioritizedArticles('competitive', query);\n    } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n      return generatePrioritizedArticles('market_trends', query);\n    } else {\n      return generatePrioritizedArticles('general', query); // Default response\n    }\n  };\n\n  const generatePrioritizedArticles = (category, originalQuery) => {\n    const articles = [];\n    const usedArticleTypes = new Set(); // Track used article types to prevent duplicates\n\n    // Priority 1: Critical regulatory compliance (highest priority)\n    // Priority 2: High financial impact (€100M+ revenue/cost impact)\n    // Priority 3: Immediate competitive threats\n    // Priority 4: Time-sensitive opportunities\n    // Priority 5: Medium-term strategic considerations (lowest priority)\n\n    const addUniqueArticle = (articleGenerator, articleType, priority, priorityLabel) => {\n      if (!usedArticleTypes.has(articleType)) {\n        const article = articleGenerator();\n\n        // Normalize article structure to ensure consistency\n        const normalizedArticle = {\n          ...article,\n          priority,\n          priorityLabel,\n          articleId: articleType,\n          // Ensure all articles have detailedFindings for consistent rendering\n          detailedFindings: article.detailedFindings || article.keyFindings || [],\n          // Ensure all articles have sources\n          sources: article.sources || [],\n          // Ensure all articles have topFindings for the summary section\n          topFindings: article.topFindings || (article.keyFindings ? article.keyFindings.slice(0, 3) : [])\n        };\n\n        articles.push(normalizedArticle);\n        usedArticleTypes.add(articleType);\n      }\n    };\n\n    switch (category) {\n      case 'eu_regulation':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n        break;\n      case 'cbam':\n        addUniqueArticle(generateCBAMReport, 'cbam', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        break;\n      case 'comprehensive_esg':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateComprehensiveESGReport, 'comprehensive_esg', 2, 'High Financial Impact');\n        break;\n      case 'circular_economy':\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        break;\n      case 'competitive':\n        addUniqueArticle(generateCompetitorReport, 'competitive', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        break;\n      case 'market_trends':\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 2, 'High Financial Impact');\n        break;\n      default:\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n    }\n\n    // Sort articles by priority (1 = highest, 5 = lowest)\n    articles.sort((a, b) => a.priority - b.priority);\n\n    return {\n      responseType: 'multi_article',\n      originalQuery: originalQuery,\n      totalArticles: articles.length,\n      articles: articles\n    };\n  };\n\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [\n        {\n          type: \"regulatory\",\n          title: \"Mandatory Recycled Content Requirements\",\n          impact: \"Critical\",\n          description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n          action: \"Secure recycling partnerships immediately\"\n        },\n        {\n          type: \"financial\",\n          title: \"Investment Requirements\",\n          impact: \"High\",\n          description: \"$800M-1.2B needed for compliance infrastructure\",\n          action: \"Establish dedicated compliance budget\"\n        },\n        {\n          type: \"competitive\",\n          title: \"SABIC Competitive Threat\",\n          impact: \"High\",\n          description: \"Risk losing 15-20% EU market share to competitors\",\n          action: \"Accelerate sustainable product development\"\n        }\n      ],\n      detailedFindings: [\n        {\n          type: \"regulatory\",\n          title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n          details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n          confidence: 98,\n          timeline: \"Implementation: Jan 2025, Full compliance: 2030\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"financial\",\n          title: \"Compliance Investment Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n          details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2027\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Landscape Shift\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n          details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n          confidence: 92,\n          timeline: \"Competitive threat: Immediate\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"market\",\n          title: \"Borouge Strategic Partnership with ALPLA Group\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Exclusive partnership opportunity with ALPLA Group to secure 40% of required recycled content supply for EU compliance.\",\n          details: \"Strategic alliance would provide Borouge with preferential access to high-quality recycled polyolefins from ALPLA's European network, ensuring compliance while creating competitive moat against SABIC and Dow.\",\n          confidence: 94,\n          timeline: \"Partnership agreement needed within 6 months\",\n          isBorogueSpecific: true\n        },\n        {\n          type: \"technology\",\n          title: \"Borouge Advanced Chemical Recycling Initiative\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Proprietary chemical recycling technology development could position Borouge as market leader in circular polyolefins.\",\n          details: \"Investment in advanced pyrolysis and depolymerization technologies would enable Borouge to process mixed plastic waste into virgin-quality feedstock, creating new revenue streams worth €300-500M annually by 2030.\",\n          confidence: 87,\n          timeline: \"36 months to commercial deployment\",\n          isBorogueSpecific: true\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [\n        {\n          priority: \"Critical\",\n          action: \"Form EU Compliance Task Force\",\n          timeline: \"Next 30 days\",\n          investment: \"$5M\",\n          description: \"Immediate action team to coordinate regulatory response\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Recycling Partnerships\",\n          timeline: \"6 months\",\n          investment: \"$200-300M\",\n          description: \"Lock in technology partnerships before competitors\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line\",\n          timeline: \"12 months\",\n          investment: \"$150M\",\n          description: \"Develop premium recycled content products\"\n        }\n      ],\n      allRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish EU Regulatory Compliance Task Force\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$5M\",\n          description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Chemical Recycling Technology Partnerships\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$200-300M\",\n          description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line Development\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$150M\",\n          description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n        },\n        {\n          priority: \"Medium\",\n          action: \"Establish European Recycling Hub\",\n          timeline: \"24 months (Q4 2025)\",\n          investment: \"$400-500M\",\n          description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n        }\n      ],\n      competitiveBenchmarking: [\n        {\n          company: \"SABIC\",\n          strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n          advantage: \"First-mover in chemical recycling, strong EU presence\",\n          weakness: \"Higher cost base, limited feedstock security\"\n        },\n        {\n          company: \"Dow\",\n          strategy: \"Advanced recycling partnerships, circular design principles\",\n          advantage: \"Technology leadership, established partnerships\",\n          weakness: \"Focus on specialty applications, limited commodity exposure\"\n        },\n        {\n          company: \"LyondellBasell\",\n          strategy: \"Molecular recycling technology, circular economy solutions\",\n          advantage: \"Integrated technology development, scale advantages\",\n          weakness: \"Limited EU manufacturing footprint\"\n        }\n      ],\n      sources: [\n        { title: \"EU Packaging & Packaging Waste Regulation\", url: \"eur-lex.europa.eu\", date: \"2024-01-15\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Environment Agency Circular Economy Report\", url: \"eea.europa.eu\", date: \"2024-02-01\", type: \"research\", confidence: \"High\" },\n        { title: \"SABIC Circular Economy Strategy Update\", url: \"sabic.com\", date: \"2024-01-30\", type: \"corporate\", confidence: \"High\" },\n        { title: \"Plastics Europe Market Data 2024\", url: \"plasticseurope.org\", date: \"2024-02-15\", type: \"industry\", confidence: \"High\" },\n        { title: \"McKinsey Circular Economy in Chemicals\", url: \"mckinsey.com\", date: \"2024-01-20\", type: \"consulting\", confidence: \"Medium\" },\n        { title: \"Wood Mackenzie Petrochemicals Outlook\", url: \"woodmac.com\", date: \"2024-02-10\", type: \"market\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [\n        {\n          type: \"financial\",\n          title: \"Direct CBAM Cost Impact\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n          details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n          confidence: 92,\n          timeline: \"Implementation: January 2026\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Advantage Opportunity\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n          details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n          confidence: 88,\n          timeline: \"Advantage period: 2026-2035\"\n        },\n        {\n          type: \"technology\",\n          title: \"Carbon Reduction Investment Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n          details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2030\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"CBAM Reporting and Verification Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n          details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n          confidence: 95,\n          timeline: \"Reporting starts: October 2023\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Low-Carbon Product Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n          details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n          confidence: 78,\n          timeline: \"Market development: 2025-2030\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Implement Comprehensive Carbon Accounting System\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$10M\",\n          description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Accelerate Renewable Energy Integration\",\n          timeline: \"18 months (Q3 2025)\",\n          investment: \"$200M\",\n          description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Develop Low-Carbon Product Certification\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$5M\",\n          description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n        }\n      ],\n      sources: [\n        { title: \"EU CBAM Regulation 2023/956\", url: \"eur-lex.europa.eu\", date: \"2023-05-17\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Commission CBAM Implementation Guide\", url: \"taxation-customs.ec.europa.eu\", date: \"2024-01-10\", type: \"guidance\", confidence: \"Official\" },\n        { title: \"IEA Petrochemicals Carbon Intensity Database\", url: \"iea.org\", date: \"2024-02-05\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [\n        {\n          type: \"environmental\",\n          title: \"Decarbonization Pathway Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n          details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n          confidence: 90,\n          timeline: \"Transformation period: 2024-2050\"\n        },\n        {\n          type: \"social\",\n          title: \"UAE Emiratization and Skills Development\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n          details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n          confidence: 85,\n          timeline: \"Achievement target: 2030\"\n        },\n        {\n          type: \"governance\",\n          title: \"ESG Reporting and Transparency Enhancement\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n          details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n          confidence: 88,\n          timeline: \"Full compliance: 2025\"\n        },\n        {\n          type: \"financial\",\n          title: \"Sustainable Finance and Green Bonds Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n          details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n          confidence: 82,\n          timeline: \"Financing window: 2024-2027\"\n        },\n        {\n          type: \"technology\",\n          title: \"Digital ESG Management Platform\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n          details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n          confidence: 75,\n          timeline: \"Implementation: 2024-2025\"\n        }\n      ],\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish Chief Sustainability Officer Role\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$2M\",\n          description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Comprehensive Decarbonization Program\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$500M\",\n          description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Implement Advanced Emiratization Strategy\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$100M\",\n          description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n        }\n      ],\n      sources: [\n        { title: \"UAE Vision 2071 Strategic Framework\", url: \"government.ae\", date: \"2023-12-01\", type: \"policy\", confidence: \"Official\" },\n        { title: \"ADNOC Sustainability Strategy 2030\", url: \"adnoc.ae\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" },\n        { title: \"McKinsey ESG in Chemicals Industry\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"Medium\" }\n      ]\n    };\n  };\n\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Recycled Polyethylene Market Growth\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n          details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n          confidence: 88,\n          timeline: \"Market expansion: 2024-2030\"\n        }\n      ],\n      sources: [\n        { title: \"Ellen MacArthur Foundation Circular Economy Report\", url: \"ellenmacarthurfoundation.org\", date: \"2024-01-10\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [\n        {\n          type: \"competitive\",\n          title: \"SABIC Circular Economy Leadership\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n          details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n          confidence: 95,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      sources: [\n        { title: \"SABIC Sustainability Strategy 2030\", url: \"sabic.com\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Sustainable Packaging Demand Surge\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n          details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n          confidence: 90,\n          timeline: \"Market shift: 2024-2027\"\n        }\n      ],\n      sources: [\n        { title: \"McKinsey Sustainable Packaging Report\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const responseContent = generateMockResponse(newMessage);\n        console.log('=== Message Creation Debug ===');\n        console.log('Response articles:', responseContent.articles?.length || 0);\n        console.log('Article IDs:', responseContent.articles?.map(a => a.articleId) || []);\n        console.log('==============================');\n\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: responseContent,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 1500);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n\n  const copyMessage = (content) => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n\n  return (\n    <motion.div\n      className=\"conversation-view\"\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      exit={{ opacity: 0, x: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"conversation-header\">\n        <motion.button\n          className=\"back-btn\"\n          onClick={onBack}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <ArrowLeft size={20} />\n          Back to Search\n        </motion.button>\n\n        <div className=\"conversation-actions\">\n          <motion.button\n            className=\"action-btn\"\n            onClick={exportToPDF}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Download size={18} />\n            Export\n          </motion.button>\n          <motion.button\n            className=\"action-btn\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Share2 size={18} />\n            Share\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"messages-container\">\n        <AnimatePresence>\n          {messages.map((message, messageIndex) => (\n            <motion.div\n              key={message.id}\n              className={`message ${message.type}`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {message.type === 'user' ? (\n                <div className=\"user-message\">\n                  <div className=\"message-content\">{message.content}</div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"ai-message\">\n                  <div className=\"ai-response\">\n                    {typeof message.content === 'object' ? (\n                      message.content.responseType === 'multi_article' ? (\n                        <div className=\"multi-article-response\">\n                          <div\n                            className=\"response-header\"\n                            ref={messageIndex === messages.length - 1 ? responseHeaderRef : null}\n                          >\n                            <h3>Intelligence Analysis: {message.content.originalQuery}</h3>\n                            <div className=\"articles-summary\">\n                              <span className=\"articles-count\">{message.content.totalArticles} Articles Found</span>\n                              <span className=\"priority-note\">Sorted by criticality and business impact</span>\n                            </div>\n                          </div>\n\n                          {message.content.articles.map((article, articleIndex) => (\n                            <div key={article.articleId || articleIndex} className=\"article-container\">\n                              <div className=\"article-priority-header\">\n                                <div className=\"priority-badge-large\">\n                                  <span className=\"priority-number\">#{articleIndex + 1}</span>\n                                  <span className=\"priority-label\">{article.priorityLabel}</span>\n                                </div>\n                              </div>\n\n                              <div className=\"intelligence-report simplified\">\n                                <div className=\"report-header\">\n                                  <div className=\"report-title-section\">\n                                    <h3>{article.reportType || 'ESG Intelligence Report'}</h3>\n                                    <div className=\"report-actions\">\n                                      <button\n                                        className=\"copy-btn secondary\"\n                                        onClick={() => copyMessage(article)}\n                                        title=\"Copy article\"\n                                      >\n                                        <Copy size={14} />\n                                      </button>\n                                    </div>\n                                  </div>\n                                </div>\n\n                                {/* Problem-Solution Summary */}\n                                {(article.problem || article.executiveSummary) && (\n                                  <div className=\"problem-solution-summary\">\n                                    {article.problem && (\n                                      <div className=\"problem-statement\">\n                                        <div className=\"problem-icon\">\n                                          <AlertTriangle size={20} />\n                                        </div>\n                                        <div className=\"problem-content\">\n                                          <h4>Business Challenge</h4>\n                                          <p>{article.problem}</p>\n                                          {article.impact && article.urgency && (\n                                            <div className=\"impact-highlight\">\n                                              <span className=\"impact-text\">{article.impact}</span>\n                                              <span className=\"urgency-text\">{article.urgency}</span>\n                                            </div>\n                                          )}\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {article.opportunity && (\n                                      <div className=\"opportunity-statement\">\n                                        <div className=\"opportunity-icon\">\n                                          <Target size={20} />\n                                        </div>\n                                        <div className=\"opportunity-content\">\n                                          <h4>Market Opportunity</h4>\n                                          <p>{article.opportunity}</p>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {article.executiveSummary && !article.problem && (\n                                      <div className=\"executive-summary\">\n                                        <h4>Executive Summary</h4>\n                                        <p>{article.executiveSummary}</p>\n                                      </div>\n                                    )}\n                                  </div>\n                                )}\n\n                                {/* Key Insights - Top 3 */}\n                                <div className=\"key-insights\">\n                                  <h4>Critical Findings</h4>\n                                  <div className=\"insights-grid\">\n                                    {(article.topFindings || article.keyFindings?.slice(0, 3) || []).map((finding, index) => (\n                                      <motion.div\n                                        key={index}\n                                        className=\"insight-card\"\n                                        initial={{ opacity: 0, y: 10 }}\n                                        animate={{ opacity: 1, y: 0 }}\n                                        transition={{ delay: index * 0.1 }}\n                                      >\n                                        <div className=\"insight-header\">\n                                          <div className={`impact-indicator ${finding.impact?.toLowerCase()}`}>\n                                            {finding.impact === 'Critical' && <AlertTriangle size={16} />}\n                                            {finding.impact === 'High' && <TrendingUp size={16} />}\n                                            {finding.impact === 'Medium' && <Info size={16} />}\n                                          </div>\n                                          <span className={`impact-label ${finding.impact?.toLowerCase()}`}>\n                                            {finding.impact}\n                                          </span>\n                                        </div>\n                                        <h5>{finding.title}</h5>\n                                        <p>{finding.description}</p>\n                                        {finding.action && (\n                                          <div className=\"quick-action\">\n                                            <strong>Action:</strong> {finding.action}\n                                          </div>\n                                        )}\n                                      </motion.div>\n                                    ))}\n                                  </div>\n                                </div>\n\n\n\n                                {/* Collapsible Detailed Analysis */}\n                                <div className=\"detailed-sections\">\n                                  {/* Detailed Findings - Always show if there are findings */}\n                                  {(article.detailedFindings && article.detailedFindings.length > 0) && (\n                                    <div className=\"collapsible-section\">\n                                      <button\n                                        className=\"section-toggle\"\n                                        onClick={() => toggleSection(`${message.id}-${articleIndex}`, 'detailed-findings')}\n                                      >\n                                        <span>Detailed Analysis</span>\n                                        {expandedSections[`${message.id}-${articleIndex}-detailed-findings`] ?\n                                          <ChevronUp size={16} /> : <ChevronDown size={16} />\n                                        }\n                                      </button>\n\n                                      {expandedSections[`${message.id}-${articleIndex}-detailed-findings`] && (\n                                        <motion.div\n                                          className=\"section-content\"\n                                          initial={{ opacity: 0, height: 0 }}\n                                          animate={{ opacity: 1, height: 'auto' }}\n                                          exit={{ opacity: 0, height: 0 }}\n                                        >\n                                          <div className=\"detailed-findings\">\n                                            {(article.detailedFindings || []).map((finding, index) => (\n                                      <motion.div\n                                        key={index}\n                                        className={`finding-card ${finding.isBorogueSpecific ? 'borouge-recommendation' : ''}`}\n                                        data-type={finding.type}\n                                        initial={{ opacity: 0, x: -20 }}\n                                        animate={{ opacity: 1, x: 0 }}\n                                        transition={{ delay: index * 0.1 }}\n                                      >\n                                        <div className=\"finding-header\">\n                                          <div\n                                            className=\"finding-icon\"\n                                            style={{\n                                              background: finding.type === 'regulatory' ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)' :\n                                                         finding.type === 'financial' ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)' :\n                                                         finding.type === 'competitive' ? 'linear-gradient(135deg, #0066cc 0%, #3b82f6 100%)' :\n                                                         finding.type === 'market' ? 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)' :\n                                                         finding.type === 'technology' ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)' :\n                                                         finding.type === 'environmental' ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)' :\n                                                         finding.type === 'social' ? 'linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)' :\n                                                         finding.type === 'governance' ? 'linear-gradient(135deg, #4338ca 0%, #6366f1 100%)' :\n                                                         'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)'\n                                            }}\n                                          >\n                                            {finding.type === 'regulatory' && <AlertTriangle size={20} />}\n                                            {finding.type === 'financial' && <TrendingUp size={20} />}\n                                            {finding.type === 'competitive' && <Users size={20} />}\n                                            {finding.type === 'market' && <TrendingUp size={20} />}\n                                            {finding.type === 'technology' && <Info size={20} />}\n                                            {finding.type === 'environmental' && <AlertTriangle size={20} />}\n                                            {finding.type === 'social' && <Users size={20} />}\n                                            {finding.type === 'governance' && <Info size={20} />}\n                                          </div>\n                                          <div className=\"finding-title\">{finding.title}</div>\n                                          <div className=\"finding-badges\">\n                                            <div className={`impact-badge ${finding.impact?.toLowerCase()}`}>\n                                              {finding.impact} Impact\n                                            </div>\n                                            {finding.urgency && (\n                                              <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>\n                                                {finding.urgency}\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                        <p className=\"finding-description\">{finding.description}</p>\n                                        {finding.details && (\n                                          <div className=\"finding-details\">\n                                            <p>{finding.details}</p>\n                                          </div>\n                                        )}\n                                        {finding.timeline && (\n                                          <div className=\"finding-timeline\">\n                                            <strong>Timeline:</strong> {finding.timeline}\n                                          </div>\n                                        )}\n                                        {finding.confidence && (\n                                          <div className=\"confidence-bar\">\n                                            <div className=\"confidence-label\">Confidence: {finding.confidence}%</div>\n                                            <div className=\"confidence-progress\">\n                                              <motion.div\n                                                className=\"confidence-fill\"\n                                                initial={{ width: 0 }}\n                                                animate={{ width: `${finding.confidence}%` }}\n                                                transition={{ duration: 1, delay: 0.5 }}\n                                              />\n                                            </div>\n                                          </div>\n                                        )}\n                                      </motion.div>\n                                    ))}\n                                  </div>\n                                </motion.div>\n                              )}\n                            </div>\n                          )}\n\n                                  {/* Sources Section - Always show */}\n                                  <div className=\"collapsible-section\">\n                                    <button\n                                      className=\"section-toggle\"\n                                      onClick={() => toggleSection(`${message.id}-${articleIndex}`, 'sources')}\n                                    >\n                                      <span>Sources & References ({article.sources?.length || 0})</span>\n                                      {expandedSections[`${message.id}-${articleIndex}-sources`] ?\n                                        <ChevronUp size={16} /> : <ChevronDown size={16} />\n                                      }\n                                    </button>\n\n                                    {expandedSections[`${message.id}-${articleIndex}-sources`] && (\n                                      <motion.div\n                                        className=\"section-content\"\n                                        initial={{ opacity: 0, height: 0 }}\n                                        animate={{ opacity: 1, height: 'auto' }}\n                                        exit={{ opacity: 0, height: 0 }}\n                                      >\n                                        <div className=\"sources-section\">\n                                          <div className=\"sources-grid\">\n                                            {article.sources?.map((source, index) => (\n                                              <div key={index} className=\"source-card\">\n                                                <div className=\"source-header\">\n                                                  <ExternalLink size={14} />\n                                                  <span className=\"source-title\">{source.title}</span>\n                                                  {source.confidence && (\n                                                    <span className={`source-confidence ${source.confidence.toLowerCase()}`}>\n                                                      {source.confidence}\n                                                    </span>\n                                                  )}\n                                                </div>\n                                                <div className=\"source-meta\">\n                                                  <span className=\"source-url\">{source.url}</span>\n                                                  <span className=\"source-date\">{source.date}</span>\n                                                  <span className=\"source-type\">{source.type}</span>\n                                                </div>\n                                              </div>\n                                            ))}\n                                          </div>\n                                        </div>\n                                      </motion.div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"intelligence-report simplified\">\n                          <div className=\"report-header\">\n                            <div className=\"report-title-section\">\n                              <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>\n                              <div className=\"report-actions\">\n                                <button\n                                  className=\"copy-btn secondary\"\n                                  onClick={() => copyMessage(message.content)}\n                                  title=\"Copy report\"\n                                >\n                                  <Copy size={14} />\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                          {/* Single article content would go here - keeping existing structure */}\n                        </div>\n                      )\n                    ) : (\n                      <div className=\"simple-response\">{message.content}</div>\n                    )}\n                  </div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            className=\"loading-message\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"loading-content\">\n              <Loader2 className=\"loading-spinner\" size={20} />\n              <span>Analyzing ESG data and regulations...</span>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"message-input-container\">\n        <div className=\"message-input-box\">\n          <textarea\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask a follow-up question about ESG intelligence...\"\n            className=\"message-input\"\n            rows=\"1\"\n          />\n          <motion.button\n            className=\"send-btn\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || isLoading}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Send size={18} />\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ConversationView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,cAAc;AACrB,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMmC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMkC,iBAAiB,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAEtC,MAAMmC,aAAa,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC5CL,mBAAmB,CAACM,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE,GAAG,CAACC,IAAI,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAP,cAAc,CAACQ,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED5C,SAAS,CAAC,MAAM;IACdwC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd1B,SAAS,CAAC,MAAM;IACd,IAAIuB,YAAY,EAAE;MAChB;MACA,MAAMsB,WAAW,GAAG;QAClBC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEzB,YAAY;QACrB0B,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDvB,WAAW,CAAC,CAACkB,WAAW,CAAC,CAAC;MAC1Bd,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAoB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAAC9B,YAAY,CAAC;UAC3C0B,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDvB,WAAW,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CrB,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACAoB,UAAU,CAAC,MAAM;UACf,IAAIhB,iBAAiB,CAACO,OAAO,EAAE;YAC7BP,iBAAiB,CAACO,OAAO,CAACC,cAAc,CAAC;cACvCC,QAAQ,EAAE,QAAQ;cAClBU,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAM8B,oBAAoB,GAAIG,KAAK,IAAK;IACtC,MAAMC,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;;IAEtC;IACA,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MACrI,OAAOC,2BAA2B,CAAC,eAAe,EAAEJ,KAAK,CAAC;IAC5D,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACxG,OAAOC,2BAA2B,CAAC,MAAM,EAAEJ,KAAK,CAAC;IACnD,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7E,OAAOC,2BAA2B,CAAC,mBAAmB,EAAEJ,KAAK,CAAC;IAChE,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3E,OAAOC,2BAA2B,CAAC,kBAAkB,EAAEJ,KAAK,CAAC;IAC/D,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1G,OAAOC,2BAA2B,CAAC,aAAa,EAAEJ,KAAK,CAAC;IAC1D,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxE,OAAOC,2BAA2B,CAAC,eAAe,EAAEJ,KAAK,CAAC;IAC5D,CAAC,MAAM;MACL,OAAOI,2BAA2B,CAAC,SAAS,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAMI,2BAA2B,GAAGA,CAACC,QAAQ,EAAEC,aAAa,KAAK;IAC/D,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEpC;IACA;IACA;IACA;IACA;;IAEA,MAAMC,gBAAgB,GAAGA,CAACC,gBAAgB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,KAAK;MACnF,IAAI,CAACN,gBAAgB,CAACO,GAAG,CAACH,WAAW,CAAC,EAAE;QACtC,MAAMI,OAAO,GAAGL,gBAAgB,CAAC,CAAC;;QAElC;QACA,MAAMM,iBAAiB,GAAG;UACxB,GAAGD,OAAO;UACVH,QAAQ;UACRC,aAAa;UACbI,SAAS,EAAEN,WAAW;UACtB;UACAO,gBAAgB,EAAEH,OAAO,CAACG,gBAAgB,IAAIH,OAAO,CAACI,WAAW,IAAI,EAAE;UACvE;UACAC,OAAO,EAAEL,OAAO,CAACK,OAAO,IAAI,EAAE;UAC9B;UACAC,WAAW,EAAEN,OAAO,CAACM,WAAW,KAAKN,OAAO,CAACI,WAAW,GAAGJ,OAAO,CAACI,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;QACjG,CAAC;QAEDhB,QAAQ,CAACiB,IAAI,CAACP,iBAAiB,CAAC;QAChCT,gBAAgB,CAACiB,GAAG,CAACb,WAAW,CAAC;MACnC;IACF,CAAC;IAED,QAAQP,QAAQ;MACd,KAAK,eAAe;QAClBK,gBAAgB,CAACgB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGhB,gBAAgB,CAACiB,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACxE;MACF,KAAK,MAAM;QACTjB,gBAAgB,CAACiB,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,gCAAgC,CAAC;QACjFjB,gBAAgB,CAACgB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACzF;MACF,KAAK,mBAAmB;QACtBhB,gBAAgB,CAACgB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGhB,gBAAgB,CAACkB,8BAA8B,EAAE,mBAAmB,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACjG;MACF,KAAK,kBAAkB;QACrBlB,gBAAgB,CAACmB,6BAA6B,EAAE,kBAAkB,EAAE,CAAC,EAAE,gCAAgC,CAAC;QACxGnB,gBAAgB,CAACgB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACzF;MACF,KAAK,aAAa;QAChBhB,gBAAgB,CAACoB,wBAAwB,EAAE,aAAa,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAC9FpB,gBAAgB,CAACgB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACzF;MACF,KAAK,eAAe;QAClBhB,gBAAgB,CAACqB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGrB,gBAAgB,CAACmB,6BAA6B,EAAE,kBAAkB,EAAE,CAAC,EAAE,uBAAuB,CAAC;QAC/F;MACF;QACEnB,gBAAgB,CAACgB,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGhB,gBAAgB,CAACiB,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,uBAAuB,CAAC;IAC5E;;IAEA;IACApB,QAAQ,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpB,QAAQ,GAAGqB,CAAC,CAACrB,QAAQ,CAAC;IAEhD,OAAO;MACLsB,YAAY,EAAE,eAAe;MAC7B7B,aAAa,EAAEA,aAAa;MAC5B8B,aAAa,EAAE7B,QAAQ,CAAC8B,MAAM;MAC9B9B,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC;EAED,MAAMmB,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLY,UAAU,EAAE,iCAAiC;MAC7CC,OAAO,EAAE,qDAAqD;MAC9DC,MAAM,EAAE,uEAAuE;MAC/EC,OAAO,EAAE,kCAAkC;MAC3CC,WAAW,EAAE,8DAA8D;MAC3EpB,WAAW,EAAE,CACX;QACE/B,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,yCAAyC;QAChDH,MAAM,EAAE,UAAU;QAClBI,WAAW,EAAE,uEAAuE;QACpFC,MAAM,EAAE;MACV,CAAC,EACD;QACEtD,IAAI,EAAE,WAAW;QACjBoD,KAAK,EAAE,yBAAyB;QAChCH,MAAM,EAAE,MAAM;QACdI,WAAW,EAAE,iDAAiD;QAC9DC,MAAM,EAAE;MACV,CAAC,EACD;QACEtD,IAAI,EAAE,aAAa;QACnBoD,KAAK,EAAE,0BAA0B;QACjCH,MAAM,EAAE,MAAM;QACdI,WAAW,EAAE,mDAAmD;QAChEC,MAAM,EAAE;MACV,CAAC,CACF;MACD1B,gBAAgB,EAAE,CAChB;QACE5B,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,uDAAuD;QAC9DH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBG,WAAW,EAAE,mJAAmJ;QAChKE,OAAO,EAAE,8MAA8M;QACvNC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,iDAAiD;QAC3DC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE1D,IAAI,EAAE,WAAW;QACjBoD,KAAK,EAAE,oCAAoC;QAC3CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,qHAAqH;QAClIE,OAAO,EAAE,0LAA0L;QACnMC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,8BAA8B;QACxCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE1D,IAAI,EAAE,aAAa;QACnBoD,KAAK,EAAE,6BAA6B;QACpCH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,6GAA6G;QAC1HE,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,+BAA+B;QACzCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE1D,IAAI,EAAE,QAAQ;QACdoD,KAAK,EAAE,gDAAgD;QACvDH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBG,WAAW,EAAE,yHAAyH;QACtIE,OAAO,EAAE,kNAAkN;QAC3NC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,8CAA8C;QACxDC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE1D,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,gDAAgD;QACvDH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBG,WAAW,EAAE,wHAAwH;QACrIE,OAAO,EAAE,sNAAsN;QAC/NC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,oCAAoC;QAC9CC,iBAAiB,EAAE;MACrB,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,yBAAyB;QACxCC,kBAAkB,EAAE,YAAY;QAChCC,qBAAqB,EAAE,yBAAyB;QAChDC,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;QACxFC,MAAM,EAAE,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,8BAA8B,CAAC;QACxGC,GAAG,EAAE,CAAC,qBAAqB,EAAE,+BAA+B;MAC9D,CAAC;MACDC,SAAS,EAAE,CACT;QACE9C,QAAQ,EAAE,UAAU;QACpBgC,MAAM,EAAE,+BAA+B;QACvCG,QAAQ,EAAE,cAAc;QACxBY,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,+BAA+B;QACvCG,QAAQ,EAAE,UAAU;QACpBY,UAAU,EAAE,WAAW;QACvBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,iCAAiC;QACzCG,QAAQ,EAAE,WAAW;QACrBY,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDiB,kBAAkB,EAAE,CAClB;QACEhD,QAAQ,EAAE,UAAU;QACpBgC,MAAM,EAAE,+CAA+C;QACvDG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,mDAAmD;QAC3DG,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,WAAW;QACvBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,6CAA6C;QACrDG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,QAAQ;QAClBgC,MAAM,EAAE,kCAAkC;QAC1CG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,WAAW;QACvBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDkB,uBAAuB,EAAE,CACvB;QACEC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,qEAAqE;QAC/EC,SAAS,EAAE,uDAAuD;QAClEC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,6DAA6D;QACvEC,SAAS,EAAE,iDAAiD;QAC5DC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE,4DAA4D;QACtEC,SAAS,EAAE,qDAAqD;QAChEC,QAAQ,EAAE;MACZ,CAAC,CACF;MACD7C,OAAO,EAAE,CACP;QAAEsB,KAAK,EAAE,2CAA2C;QAAEwB,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,YAAY;QAAEwD,UAAU,EAAE;MAAW,CAAC,EAChJ;QAAEJ,KAAK,EAAE,qDAAqD;QAAEwB,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,UAAU;QAAEwD,UAAU,EAAE;MAAO,CAAC,EAChJ;QAAEJ,KAAK,EAAE,wCAAwC;QAAEwB,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,WAAW;QAAEwD,UAAU,EAAE;MAAO,CAAC,EAChI;QAAEJ,KAAK,EAAE,kCAAkC;QAAEwB,GAAG,EAAE,oBAAoB;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,UAAU;QAAEwD,UAAU,EAAE;MAAO,CAAC,EAClI;QAAEJ,KAAK,EAAE,wCAAwC;QAAEwB,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,YAAY;QAAEwD,UAAU,EAAE;MAAS,CAAC,EACtI;QAAEJ,KAAK,EAAE,uCAAuC;QAAEwB,GAAG,EAAE,aAAa;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,QAAQ;QAAEwD,UAAU,EAAE;MAAO,CAAC;IAElI,CAAC;EACH,CAAC;EAED,MAAMpB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACLW,UAAU,EAAE,2DAA2D;MACvE+B,gBAAgB,EAAE,qRAAqR;MACvSjD,WAAW,EAAE,CACX;QACE7B,IAAI,EAAE,WAAW;QACjBoD,KAAK,EAAE,yBAAyB;QAChCH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBG,WAAW,EAAE,8FAA8F;QAC3GE,OAAO,EAAE,6IAA6I;QACtJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,aAAa;QACnBoD,KAAK,EAAE,mCAAmC;QAC1CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,uGAAuG;QACpHE,OAAO,EAAE,0JAA0J;QACnKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,0CAA0C;QACjDH,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,gFAAgF;QAC7FE,OAAO,EAAE,kJAAkJ;QAC3JC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,8CAA8C;QACrDH,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,0GAA0G;QACvHE,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,QAAQ;QACdoD,KAAK,EAAE,wCAAwC;QAC/CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBG,WAAW,EAAE,8FAA8F;QAC3GE,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDE,YAAY,EAAE;QACZC,aAAa,EAAE,2BAA2B;QAC1CC,kBAAkB,EAAE,4BAA4B;QAChDC,qBAAqB,EAAE,kCAAkC;QACzDC,iBAAiB,EAAE;MACrB,CAAC;MACDgB,wBAAwB,EAAE,CACxB;QACEzD,QAAQ,EAAE,UAAU;QACpBgC,MAAM,EAAE,kDAAkD;QAC1DG,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,MAAM;QAClBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,yCAAyC;QACjDG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,0CAA0C;QAClDG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDvB,OAAO,EAAE,CACP;QAAEsB,KAAK,EAAE,6BAA6B;QAAEwB,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,YAAY;QAAEwD,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEJ,KAAK,EAAE,+CAA+C;QAAEwB,GAAG,EAAE,+BAA+B;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,UAAU;QAAEwD,UAAU,EAAE;MAAW,CAAC,EAC9J;QAAEJ,KAAK,EAAE,8CAA8C;QAAEwB,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,UAAU;QAAEwD,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMnB,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,OAAO;MACLU,UAAU,EAAE,uCAAuC;MACnD+B,gBAAgB,EAAE,oWAAoW;MACtXjD,WAAW,EAAE,CACX;QACE7B,IAAI,EAAE,eAAe;QACrBoD,KAAK,EAAE,sCAAsC;QAC7CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,iHAAiH;QAC9HE,OAAO,EAAE,uKAAuK;QAChLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,QAAQ;QACdoD,KAAK,EAAE,0CAA0C;QACjDH,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,uHAAuH;QACpIE,OAAO,EAAE,sKAAsK;QAC/KC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,4CAA4C;QACnDH,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBG,WAAW,EAAE,4GAA4G;QACzHE,OAAO,EAAE,6JAA6J;QACtKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,WAAW;QACjBoD,KAAK,EAAE,iDAAiD;QACxDH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBG,WAAW,EAAE,iHAAiH;QAC9HE,OAAO,EAAE,+JAA+J;QACxKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEzD,IAAI,EAAE,YAAY;QAClBoD,KAAK,EAAE,iCAAiC;QACxCH,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBG,WAAW,EAAE,yHAAyH;QACtIE,OAAO,EAAE,gJAAgJ;QACzJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDsB,wBAAwB,EAAE,CACxB;QACEzD,QAAQ,EAAE,UAAU;QACpBgC,MAAM,EAAE,6CAA6C;QACrDG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,8CAA8C;QACtDG,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,EACD;QACE/B,QAAQ,EAAE,MAAM;QAChBgC,MAAM,EAAE,2CAA2C;QACnDG,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDvB,OAAO,EAAE,CACP;QAAEsB,KAAK,EAAE,qCAAqC;QAAEwB,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,QAAQ;QAAEwD,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEJ,KAAK,EAAE,oCAAoC;QAAEwB,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,WAAW;QAAEwD,UAAU,EAAE;MAAO,CAAC,EAC3H;QAAEJ,KAAK,EAAE,oCAAoC;QAAEwB,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,YAAY;QAAEwD,UAAU,EAAE;MAAS,CAAC;IAEtI,CAAC;EACH,CAAC;EAED,MAAMlB,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,OAAO;MACLS,UAAU,EAAE,sCAAsC;MAClD+B,gBAAgB,EAAE,sQAAsQ;MACxRjD,WAAW,EAAE,CACX;QACE7B,IAAI,EAAE,QAAQ;QACdoD,KAAK,EAAE,qCAAqC;QAC5CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,0EAA0E;QACvFE,OAAO,EAAE,wIAAwI;QACjJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACD3B,OAAO,EAAE,CACP;QAAEsB,KAAK,EAAE,oDAAoD;QAAEwB,GAAG,EAAE,8BAA8B;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,UAAU;QAAEwD,UAAU,EAAE;MAAO,CAAC;IAElK,CAAC;EACH,CAAC;EAED,MAAMjB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,OAAO;MACLQ,UAAU,EAAE,mCAAmC;MAC/C+B,gBAAgB,EAAE,kPAAkP;MACpQjD,WAAW,EAAE,CACX;QACE7B,IAAI,EAAE,aAAa;QACnBoD,KAAK,EAAE,mCAAmC;QAC1CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBG,WAAW,EAAE,wHAAwH;QACrIE,OAAO,EAAE,wHAAwH;QACjIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACD3B,OAAO,EAAE,CACP;QAAEsB,KAAK,EAAE,oCAAoC;QAAEwB,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,WAAW;QAAEwD,UAAU,EAAE;MAAO,CAAC;IAEhI,CAAC;EACH,CAAC;EAED,MAAMhB,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLO,UAAU,EAAE,kCAAkC;MAC9C+B,gBAAgB,EAAE,gPAAgP;MAClQjD,WAAW,EAAE,CACX;QACE7B,IAAI,EAAE,QAAQ;QACdoD,KAAK,EAAE,oCAAoC;QAC3CH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfG,WAAW,EAAE,yHAAyH;QACtIE,OAAO,EAAE,0HAA0H;QACnIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACD3B,OAAO,EAAE,CACP;QAAEsB,KAAK,EAAE,uCAAuC;QAAEwB,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAE7E,IAAI,EAAE,YAAY;QAAEwD,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAInG,UAAU,CAACoG,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMnF,WAAW,GAAG;QAClBC,EAAE,EAAEpB,QAAQ,CAACmE,MAAM,GAAG,CAAC;QACvB9C,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEpB,UAAU;QACnBqB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDvB,WAAW,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,WAAW,CAAC,CAAC;MAC3ChB,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAoB,UAAU,CAAC,MAAM;QAAA,IAAA8E,qBAAA,EAAAC,sBAAA;QACf,MAAMC,eAAe,GAAG9E,oBAAoB,CAACzB,UAAU,CAAC;QACxDwG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,EAAAJ,qBAAA,GAAAE,eAAe,CAACpE,QAAQ,cAAAkE,qBAAA,uBAAxBA,qBAAA,CAA0BpC,MAAM,KAAI,CAAC,CAAC;QACxEuC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,EAAAH,sBAAA,GAAAC,eAAe,CAACpE,QAAQ,cAAAmE,sBAAA,uBAAxBA,sBAAA,CAA0BI,GAAG,CAAC7C,CAAC,IAAIA,CAAC,CAACf,SAAS,CAAC,KAAI,EAAE,CAAC;QAClF0D,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAE7C,MAAMjF,UAAU,GAAG;UACjBN,EAAE,EAAEpB,QAAQ,CAACmE,MAAM,GAAG,CAAC;UACvB9C,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEmF,eAAe;UACxBlF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDvB,WAAW,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CrB,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACAoB,UAAU,CAAC,MAAM;UACf,IAAIhB,iBAAiB,CAACO,OAAO,EAAE;YAC7BP,iBAAiB,CAACO,OAAO,CAACC,cAAc,CAAC;cACvCC,QAAQ,EAAE,QAAQ;cAClBU,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMgF,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBZ,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAR,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMQ,WAAW,GAAI7F,OAAO,IAAK;IAC/B8F,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,OAAOhG,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGiG,IAAI,CAACC,SAAS,CAAClG,OAAO,CAAC,CAAC;EAChG,CAAC;EAED,oBACE3B,OAAA,CAACnB,MAAM,CAACiJ,GAAG;IACTC,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAC7BG,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9BvI,OAAA;MAAK+H,SAAS,EAAC,qBAAqB;MAAAQ,QAAA,gBAClCvI,OAAA,CAACnB,MAAM,CAAC2J,MAAM;QACZT,SAAS,EAAC,UAAU;QACpBU,OAAO,EAAEtI,MAAO;QAChBuI,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAJ,QAAA,gBAE1BvI,OAAA,CAACjB,SAAS;UAAC8J,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBjJ,OAAA;QAAK+H,SAAS,EAAC,sBAAsB;QAAAQ,QAAA,gBACnCvI,OAAA,CAACnB,MAAM,CAAC2J,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAElB,WAAY;UACrBmB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1BvI,OAAA,CAACf,QAAQ;YAAC4J,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChBjJ,OAAA,CAACnB,MAAM,CAAC2J,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1BvI,OAAA,CAACR,MAAM;YAACqJ,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjJ,OAAA;MAAK+H,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,gBACjCvI,OAAA,CAAClB,eAAe;QAAAyJ,QAAA,EACblI,QAAQ,CAAC4G,GAAG,CAAC,CAACiC,OAAO,EAAEC,YAAY,kBAClCnJ,OAAA,CAACnB,MAAM,CAACiJ,GAAG;UAETC,SAAS,EAAE,WAAWmB,OAAO,CAACxH,IAAI,EAAG;UACrCsG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAG,CAAE;UAC/BjB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAE,CAAE;UAC9BhB,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7Bf,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAC,QAAA,EAE7BW,OAAO,CAACxH,IAAI,KAAK,MAAM,gBACtB1B,OAAA;YAAK+H,SAAS,EAAC,cAAc;YAAAQ,QAAA,gBAC3BvI,OAAA;cAAK+H,SAAS,EAAC,iBAAiB;cAAAQ,QAAA,EAAEW,OAAO,CAACvH;YAAO;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDjJ,OAAA;cAAK+H,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BW,OAAO,CAACtH,SAAS,CAACyH,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjJ,OAAA;YAAK+H,SAAS,EAAC,YAAY;YAAAQ,QAAA,gBACzBvI,OAAA;cAAK+H,SAAS,EAAC,aAAa;cAAAQ,QAAA,EACzB,OAAOW,OAAO,CAACvH,OAAO,KAAK,QAAQ,GAClCuH,OAAO,CAACvH,OAAO,CAAC2C,YAAY,KAAK,eAAe,gBAC9CtE,OAAA;gBAAK+H,SAAS,EAAC,wBAAwB;gBAAAQ,QAAA,gBACrCvI,OAAA;kBACE+H,SAAS,EAAC,iBAAiB;kBAC3ByB,GAAG,EAAEL,YAAY,KAAK9I,QAAQ,CAACmE,MAAM,GAAG,CAAC,GAAG1D,iBAAiB,GAAG,IAAK;kBAAAyH,QAAA,gBAErEvI,OAAA;oBAAAuI,QAAA,GAAI,yBAAuB,EAACW,OAAO,CAACvH,OAAO,CAACc,aAAa;kBAAA;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/DjJ,OAAA;oBAAK+H,SAAS,EAAC,kBAAkB;oBAAAQ,QAAA,gBAC/BvI,OAAA;sBAAM+H,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,GAAEW,OAAO,CAACvH,OAAO,CAAC4C,aAAa,EAAC,iBAAe;oBAAA;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFjJ,OAAA;sBAAM+H,SAAS,EAAC,eAAe;sBAAAQ,QAAA,EAAC;oBAAyC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELC,OAAO,CAACvH,OAAO,CAACe,QAAQ,CAACuE,GAAG,CAAC,CAAC9D,OAAO,EAAEsG,YAAY;kBAAA,IAAAC,oBAAA,EAAAC,gBAAA,EAAAC,iBAAA;kBAAA,oBAClD5J,OAAA;oBAA6C+H,SAAS,EAAC,mBAAmB;oBAAAQ,QAAA,gBACxEvI,OAAA;sBAAK+H,SAAS,EAAC,yBAAyB;sBAAAQ,QAAA,eACtCvI,OAAA;wBAAK+H,SAAS,EAAC,sBAAsB;wBAAAQ,QAAA,gBACnCvI,OAAA;0BAAM+H,SAAS,EAAC,iBAAiB;0BAAAQ,QAAA,GAAC,GAAC,EAACkB,YAAY,GAAG,CAAC;wBAAA;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC5DjJ,OAAA;0BAAM+H,SAAS,EAAC,gBAAgB;0BAAAQ,QAAA,EAAEpF,OAAO,CAACF;wBAAa;0BAAA6F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENjJ,OAAA;sBAAK+H,SAAS,EAAC,gCAAgC;sBAAAQ,QAAA,gBAC7CvI,OAAA;wBAAK+H,SAAS,EAAC,eAAe;wBAAAQ,QAAA,eAC5BvI,OAAA;0BAAK+H,SAAS,EAAC,sBAAsB;0BAAAQ,QAAA,gBACnCvI,OAAA;4BAAAuI,QAAA,EAAKpF,OAAO,CAACsB,UAAU,IAAI;0BAAyB;4BAAAqE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1DjJ,OAAA;4BAAK+H,SAAS,EAAC,gBAAgB;4BAAAQ,QAAA,eAC7BvI,OAAA;8BACE+H,SAAS,EAAC,oBAAoB;8BAC9BU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAACrE,OAAO,CAAE;8BACpC2B,KAAK,EAAC,cAAc;8BAAAyD,QAAA,eAEpBvI,OAAA,CAACT,IAAI;gCAACsJ,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACZ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAGL,CAAC9F,OAAO,CAACuB,OAAO,IAAIvB,OAAO,CAACqD,gBAAgB,kBAC3CxG,OAAA;wBAAK+H,SAAS,EAAC,0BAA0B;wBAAAQ,QAAA,GACtCpF,OAAO,CAACuB,OAAO,iBACd1E,OAAA;0BAAK+H,SAAS,EAAC,mBAAmB;0BAAAQ,QAAA,gBAChCvI,OAAA;4BAAK+H,SAAS,EAAC,cAAc;4BAAAQ,QAAA,eAC3BvI,OAAA,CAACZ,aAAa;8BAACyJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACNjJ,OAAA;4BAAK+H,SAAS,EAAC,iBAAiB;4BAAAQ,QAAA,gBAC9BvI,OAAA;8BAAAuI,QAAA,EAAI;4BAAkB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC3BjJ,OAAA;8BAAAuI,QAAA,EAAIpF,OAAO,CAACuB;4BAAO;8BAAAoE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,EACvB9F,OAAO,CAACwB,MAAM,IAAIxB,OAAO,CAACyB,OAAO,iBAChC5E,OAAA;8BAAK+H,SAAS,EAAC,kBAAkB;8BAAAQ,QAAA,gBAC/BvI,OAAA;gCAAM+H,SAAS,EAAC,aAAa;gCAAAQ,QAAA,EAAEpF,OAAO,CAACwB;8BAAM;gCAAAmE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eACrDjJ,OAAA;gCAAM+H,SAAS,EAAC,cAAc;gCAAAQ,QAAA,EAAEpF,OAAO,CAACyB;8BAAO;gCAAAkE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAEA9F,OAAO,CAAC0B,WAAW,iBAClB7E,OAAA;0BAAK+H,SAAS,EAAC,uBAAuB;0BAAAQ,QAAA,gBACpCvI,OAAA;4BAAK+H,SAAS,EAAC,kBAAkB;4BAAAQ,QAAA,eAC/BvI,OAAA,CAACL,MAAM;8BAACkJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACNjJ,OAAA;4BAAK+H,SAAS,EAAC,qBAAqB;4BAAAQ,QAAA,gBAClCvI,OAAA;8BAAAuI,QAAA,EAAI;4BAAkB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC3BjJ,OAAA;8BAAAuI,QAAA,EAAIpF,OAAO,CAAC0B;4BAAW;8BAAAiE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAEA9F,OAAO,CAACqD,gBAAgB,IAAI,CAACrD,OAAO,CAACuB,OAAO,iBAC3C1E,OAAA;0BAAK+H,SAAS,EAAC,mBAAmB;0BAAAQ,QAAA,gBAChCvI,OAAA;4BAAAuI,QAAA,EAAI;0BAAiB;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1BjJ,OAAA;4BAAAuI,QAAA,EAAIpF,OAAO,CAACqD;0BAAgB;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CACN,eAGDjJ,OAAA;wBAAK+H,SAAS,EAAC,cAAc;wBAAAQ,QAAA,gBAC3BvI,OAAA;0BAAAuI,QAAA,EAAI;wBAAiB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1BjJ,OAAA;0BAAK+H,SAAS,EAAC,eAAe;0BAAAQ,QAAA,EAC3B,CAACpF,OAAO,CAACM,WAAW,MAAAiG,oBAAA,GAAIvG,OAAO,CAACI,WAAW,cAAAmG,oBAAA,uBAAnBA,oBAAA,CAAqBhG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,EAAEuD,GAAG,CAAC,CAAC4C,OAAO,EAAEC,KAAK;4BAAA,IAAAC,eAAA,EAAAC,gBAAA;4BAAA,oBAClFhK,OAAA,CAACnB,MAAM,CAACiJ,GAAG;8BAETC,SAAS,EAAC,cAAc;8BACxBC,OAAO,EAAE;gCAAEC,OAAO,EAAE,CAAC;gCAAEmB,CAAC,EAAE;8BAAG,CAAE;8BAC/BjB,OAAO,EAAE;gCAAEF,OAAO,EAAE,CAAC;gCAAEmB,CAAC,EAAE;8BAAE,CAAE;8BAC9Bf,UAAU,EAAE;gCAAE4B,KAAK,EAAEH,KAAK,GAAG;8BAAI,CAAE;8BAAAvB,QAAA,gBAEnCvI,OAAA;gCAAK+H,SAAS,EAAC,gBAAgB;gCAAAQ,QAAA,gBAC7BvI,OAAA;kCAAK+H,SAAS,EAAE,qBAAAgC,eAAA,GAAoBF,OAAO,CAAClF,MAAM,cAAAoF,eAAA,uBAAdA,eAAA,CAAgB1H,WAAW,CAAC,CAAC,EAAG;kCAAAkG,QAAA,GACjEsB,OAAO,CAAClF,MAAM,KAAK,UAAU,iBAAI3E,OAAA,CAACZ,aAAa;oCAACyJ,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EAC5DY,OAAO,CAAClF,MAAM,KAAK,MAAM,iBAAI3E,OAAA,CAACb,UAAU;oCAAC0J,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EACrDY,OAAO,CAAClF,MAAM,KAAK,QAAQ,iBAAI3E,OAAA,CAACX,IAAI;oCAACwJ,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC/C,CAAC,eACNjJ,OAAA;kCAAM+H,SAAS,EAAE,iBAAAiC,gBAAA,GAAgBH,OAAO,CAAClF,MAAM,cAAAqF,gBAAA,uBAAdA,gBAAA,CAAgB3H,WAAW,CAAC,CAAC,EAAG;kCAAAkG,QAAA,EAC9DsB,OAAO,CAAClF;gCAAM;kCAAAmE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACX,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC,eACNjJ,OAAA;gCAAAuI,QAAA,EAAKsB,OAAO,CAAC/E;8BAAK;gCAAAgE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eACxBjJ,OAAA;gCAAAuI,QAAA,EAAIsB,OAAO,CAAC9E;8BAAW;gCAAA+D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,EAC3BY,OAAO,CAAC7E,MAAM,iBACbhF,OAAA;gCAAK+H,SAAS,EAAC,cAAc;gCAAAQ,QAAA,gBAC3BvI,OAAA;kCAAAuI,QAAA,EAAQ;gCAAO;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAAC7E,MAAM;8BAAA;gCAAA8D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CACN;4BAAA,GAtBIa,KAAK;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAuBA,CAAC;0BAAA,CACd;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAKNjJ,OAAA;wBAAK+H,SAAS,EAAC,mBAAmB;wBAAAQ,QAAA,GAE9BpF,OAAO,CAACG,gBAAgB,IAAIH,OAAO,CAACG,gBAAgB,CAACkB,MAAM,GAAG,CAAC,iBAC/DxE,OAAA;0BAAK+H,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,gBAClCvI,OAAA;4BACE+H,SAAS,EAAC,gBAAgB;4BAC1BU,OAAO,EAAEA,CAAA,KAAM1H,aAAa,CAAC,GAAGmI,OAAO,CAACzH,EAAE,IAAIgI,YAAY,EAAE,EAAE,mBAAmB,CAAE;4BAAAlB,QAAA,gBAEnFvI,OAAA;8BAAAuI,QAAA,EAAM;4BAAiB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EAC7BtI,gBAAgB,CAAC,GAAGuI,OAAO,CAACzH,EAAE,IAAIgI,YAAY,oBAAoB,CAAC,gBAClEzJ,OAAA,CAACN,SAAS;8BAACmJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACP,WAAW;8BAACoJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAE/C,CAAC,EAERtI,gBAAgB,CAAC,GAAGuI,OAAO,CAACzH,EAAE,IAAIgI,YAAY,oBAAoB,CAAC,iBAClEzJ,OAAA,CAACnB,MAAM,CAACiJ,GAAG;4BACTC,SAAS,EAAC,iBAAiB;4BAC3BC,OAAO,EAAE;8BAAEC,OAAO,EAAE,CAAC;8BAAEiC,MAAM,EAAE;4BAAE,CAAE;4BACnC/B,OAAO,EAAE;8BAAEF,OAAO,EAAE,CAAC;8BAAEiC,MAAM,EAAE;4BAAO,CAAE;4BACxC9B,IAAI,EAAE;8BAAEH,OAAO,EAAE,CAAC;8BAAEiC,MAAM,EAAE;4BAAE,CAAE;4BAAA3B,QAAA,eAEhCvI,OAAA;8BAAK+H,SAAS,EAAC,mBAAmB;8BAAAQ,QAAA,EAC/B,CAACpF,OAAO,CAACG,gBAAgB,IAAI,EAAE,EAAE2D,GAAG,CAAC,CAAC4C,OAAO,EAAEC,KAAK;gCAAA,IAAAK,gBAAA;gCAAA,oBAC3DnK,OAAA,CAACnB,MAAM,CAACiJ,GAAG;kCAETC,SAAS,EAAE,gBAAgB8B,OAAO,CAACzE,iBAAiB,GAAG,wBAAwB,GAAG,EAAE,EAAG;kCACvF,aAAWyE,OAAO,CAACnI,IAAK;kCACxBsG,OAAO,EAAE;oCAAEC,OAAO,EAAE,CAAC;oCAAEC,CAAC,EAAE,CAAC;kCAAG,CAAE;kCAChCC,OAAO,EAAE;oCAAEF,OAAO,EAAE,CAAC;oCAAEC,CAAC,EAAE;kCAAE,CAAE;kCAC9BG,UAAU,EAAE;oCAAE4B,KAAK,EAAEH,KAAK,GAAG;kCAAI,CAAE;kCAAAvB,QAAA,gBAEnCvI,OAAA;oCAAK+H,SAAS,EAAC,gBAAgB;oCAAAQ,QAAA,gBAC7BvI,OAAA;sCACE+H,SAAS,EAAC,cAAc;sCACxBqC,KAAK,EAAE;wCACLC,UAAU,EAAER,OAAO,CAACnI,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACpFmI,OAAO,CAACnI,IAAI,KAAK,WAAW,GAAG,mDAAmD,GAClFmI,OAAO,CAACnI,IAAI,KAAK,aAAa,GAAG,mDAAmD,GACpFmI,OAAO,CAACnI,IAAI,KAAK,QAAQ,GAAG,mDAAmD,GAC/EmI,OAAO,CAACnI,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACnFmI,OAAO,CAACnI,IAAI,KAAK,eAAe,GAAG,mDAAmD,GACtFmI,OAAO,CAACnI,IAAI,KAAK,QAAQ,GAAG,mDAAmD,GAC/EmI,OAAO,CAACnI,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACnF;sCACb,CAAE;sCAAA6G,QAAA,GAEDsB,OAAO,CAACnI,IAAI,KAAK,YAAY,iBAAI1B,OAAA,CAACZ,aAAa;wCAACyJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EAC5DY,OAAO,CAACnI,IAAI,KAAK,WAAW,iBAAI1B,OAAA,CAACb,UAAU;wCAAC0J,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACxDY,OAAO,CAACnI,IAAI,KAAK,aAAa,iBAAI1B,OAAA,CAACF,KAAK;wCAAC+I,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACrDY,OAAO,CAACnI,IAAI,KAAK,QAAQ,iBAAI1B,OAAA,CAACb,UAAU;wCAAC0J,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACrDY,OAAO,CAACnI,IAAI,KAAK,YAAY,iBAAI1B,OAAA,CAACX,IAAI;wCAACwJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACnDY,OAAO,CAACnI,IAAI,KAAK,eAAe,iBAAI1B,OAAA,CAACZ,aAAa;wCAACyJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EAC/DY,OAAO,CAACnI,IAAI,KAAK,QAAQ,iBAAI1B,OAAA,CAACF,KAAK;wCAAC+I,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EAChDY,OAAO,CAACnI,IAAI,KAAK,YAAY,iBAAI1B,OAAA,CAACX,IAAI;wCAACwJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACjD,CAAC,eACNjJ,OAAA;sCAAK+H,SAAS,EAAC,eAAe;sCAAAQ,QAAA,EAAEsB,OAAO,CAAC/E;oCAAK;sCAAAgE,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAM,CAAC,eACpDjJ,OAAA;sCAAK+H,SAAS,EAAC,gBAAgB;sCAAAQ,QAAA,gBAC7BvI,OAAA;wCAAK+H,SAAS,EAAE,iBAAAoC,gBAAA,GAAgBN,OAAO,CAAClF,MAAM,cAAAwF,gBAAA,uBAAdA,gBAAA,CAAgB9H,WAAW,CAAC,CAAC,EAAG;wCAAAkG,QAAA,GAC7DsB,OAAO,CAAClF,MAAM,EAAC,SAClB;sCAAA;wCAAAmE,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAK,CAAC,EACLY,OAAO,CAACjF,OAAO,iBACd5E,OAAA;wCAAK+H,SAAS,EAAE,iBAAiB8B,OAAO,CAACjF,OAAO,CAACvC,WAAW,CAAC,CAAC,EAAG;wCAAAkG,QAAA,EAC9DsB,OAAO,CAACjF;sCAAO;wCAAAkE,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACb,CACN;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACE,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CAAC,eACNjJ,OAAA;oCAAG+H,SAAS,EAAC,qBAAqB;oCAAAQ,QAAA,EAAEsB,OAAO,CAAC9E;kCAAW;oCAAA+D,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAI,CAAC,EAC3DY,OAAO,CAAC5E,OAAO,iBACdjF,OAAA;oCAAK+H,SAAS,EAAC,iBAAiB;oCAAAQ,QAAA,eAC9BvI,OAAA;sCAAAuI,QAAA,EAAIsB,OAAO,CAAC5E;oCAAO;sCAAA6D,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAI;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrB,CACN,EACAY,OAAO,CAAC1E,QAAQ,iBACfnF,OAAA;oCAAK+H,SAAS,EAAC,kBAAkB;oCAAAQ,QAAA,gBAC/BvI,OAAA;sCAAAuI,QAAA,EAAQ;oCAAS;sCAAAO,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAAC1E,QAAQ;kCAAA;oCAAA2D,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACzC,CACN,EACAY,OAAO,CAAC3E,UAAU,iBACjBlF,OAAA;oCAAK+H,SAAS,EAAC,gBAAgB;oCAAAQ,QAAA,gBAC7BvI,OAAA;sCAAK+H,SAAS,EAAC,kBAAkB;sCAAAQ,QAAA,GAAC,cAAY,EAACsB,OAAO,CAAC3E,UAAU,EAAC,GAAC;oCAAA;sCAAA4D,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAK,CAAC,eACzEjJ,OAAA;sCAAK+H,SAAS,EAAC,qBAAqB;sCAAAQ,QAAA,eAClCvI,OAAA,CAACnB,MAAM,CAACiJ,GAAG;wCACTC,SAAS,EAAC,iBAAiB;wCAC3BC,OAAO,EAAE;0CAAEsC,KAAK,EAAE;wCAAE,CAAE;wCACtBnC,OAAO,EAAE;0CAAEmC,KAAK,EAAE,GAAGT,OAAO,CAAC3E,UAAU;wCAAI,CAAE;wCAC7CmD,UAAU,EAAE;0CAAEC,QAAQ,EAAE,CAAC;0CAAE2B,KAAK,EAAE;wCAAI;sCAAE;wCAAAnB,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACzC;oCAAC;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACC,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CACN;gCAAA,GAlEIa,KAAK;kCAAAhB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAmEA,CAAC;8BAAA,CACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACI,CACb;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN,eAGOjJ,OAAA;0BAAK+H,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,gBAClCvI,OAAA;4BACE+H,SAAS,EAAC,gBAAgB;4BAC1BU,OAAO,EAAEA,CAAA,KAAM1H,aAAa,CAAC,GAAGmI,OAAO,CAACzH,EAAE,IAAIgI,YAAY,EAAE,EAAE,SAAS,CAAE;4BAAAlB,QAAA,gBAEzEvI,OAAA;8BAAAuI,QAAA,GAAM,wBAAsB,EAAC,EAAAoB,gBAAA,GAAAxG,OAAO,CAACK,OAAO,cAAAmG,gBAAA,uBAAfA,gBAAA,CAAiBnF,MAAM,KAAI,CAAC,EAAC,GAAC;4BAAA;8BAAAsE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EACjEtI,gBAAgB,CAAC,GAAGuI,OAAO,CAACzH,EAAE,IAAIgI,YAAY,UAAU,CAAC,gBACxDzJ,OAAA,CAACN,SAAS;8BAACmJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACP,WAAW;8BAACoJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAE/C,CAAC,EAERtI,gBAAgB,CAAC,GAAGuI,OAAO,CAACzH,EAAE,IAAIgI,YAAY,UAAU,CAAC,iBACxDzJ,OAAA,CAACnB,MAAM,CAACiJ,GAAG;4BACTC,SAAS,EAAC,iBAAiB;4BAC3BC,OAAO,EAAE;8BAAEC,OAAO,EAAE,CAAC;8BAAEiC,MAAM,EAAE;4BAAE,CAAE;4BACnC/B,OAAO,EAAE;8BAAEF,OAAO,EAAE,CAAC;8BAAEiC,MAAM,EAAE;4BAAO,CAAE;4BACxC9B,IAAI,EAAE;8BAAEH,OAAO,EAAE,CAAC;8BAAEiC,MAAM,EAAE;4BAAE,CAAE;4BAAA3B,QAAA,eAEhCvI,OAAA;8BAAK+H,SAAS,EAAC,iBAAiB;8BAAAQ,QAAA,eAC9BvI,OAAA;gCAAK+H,SAAS,EAAC,cAAc;gCAAAQ,QAAA,GAAAqB,iBAAA,GAC1BzG,OAAO,CAACK,OAAO,cAAAoG,iBAAA,uBAAfA,iBAAA,CAAiB3C,GAAG,CAAC,CAACsD,MAAM,EAAET,KAAK,kBAClC9J,OAAA;kCAAiB+H,SAAS,EAAC,aAAa;kCAAAQ,QAAA,gBACtCvI,OAAA;oCAAK+H,SAAS,EAAC,eAAe;oCAAAQ,QAAA,gBAC5BvI,OAAA,CAACd,YAAY;sCAAC2J,IAAI,EAAE;oCAAG;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC,eAC1BjJ,OAAA;sCAAM+H,SAAS,EAAC,cAAc;sCAAAQ,QAAA,EAAEgC,MAAM,CAACzF;oCAAK;sCAAAgE,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,EACnDsB,MAAM,CAACrF,UAAU,iBAChBlF,OAAA;sCAAM+H,SAAS,EAAE,qBAAqBwC,MAAM,CAACrF,UAAU,CAAC7C,WAAW,CAAC,CAAC,EAAG;sCAAAkG,QAAA,EACrEgC,MAAM,CAACrF;oCAAU;sCAAA4D,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACd,CACP;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACE,CAAC,eACNjJ,OAAA;oCAAK+H,SAAS,EAAC,aAAa;oCAAAQ,QAAA,gBAC1BvI,OAAA;sCAAM+H,SAAS,EAAC,YAAY;sCAAAQ,QAAA,EAAEgC,MAAM,CAACjE;oCAAG;sCAAAwC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,eAChDjJ,OAAA;sCAAM+H,SAAS,EAAC,aAAa;sCAAAQ,QAAA,EAAEgC,MAAM,CAAChE;oCAAI;sCAAAuC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,eAClDjJ,OAAA;sCAAM+H,SAAS,EAAC,aAAa;sCAAAQ,QAAA,EAAEgC,MAAM,CAAC7I;oCAAI;sCAAAoH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC/C,CAAC;gCAAA,GAdEa,KAAK;kCAAAhB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAeV,CACN;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACI,CACb;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAvPE9F,OAAO,CAACE,SAAS,IAAIoG,YAAY;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwPtC,CAAC;gBAAA,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENjJ,OAAA;gBAAK+H,SAAS,EAAC,gCAAgC;gBAAAQ,QAAA,eAC7CvI,OAAA;kBAAK+H,SAAS,EAAC,eAAe;kBAAAQ,QAAA,eAC5BvI,OAAA;oBAAK+H,SAAS,EAAC,sBAAsB;oBAAAQ,QAAA,gBACnCvI,OAAA;sBAAAuI,QAAA,EAAKW,OAAO,CAACvH,OAAO,CAAC8C,UAAU,IAAI;oBAAyB;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClEjJ,OAAA;sBAAK+H,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,eAC7BvI,OAAA;wBACE+H,SAAS,EAAC,oBAAoB;wBAC9BU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC0B,OAAO,CAACvH,OAAO,CAAE;wBAC5CmD,KAAK,EAAC,aAAa;wBAAAyD,QAAA,eAEnBvI,OAAA,CAACT,IAAI;0BAACsJ,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CACN,gBAEDjJ,OAAA;gBAAK+H,SAAS,EAAC,iBAAiB;gBAAAQ,QAAA,EAAEW,OAAO,CAACvH;cAAO;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACxD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjJ,OAAA;cAAK+H,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BW,OAAO,CAACtH,SAAS,CAACyH,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN,GAtTIC,OAAO,CAACzH,EAAE;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuTL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEjBxI,SAAS,iBACRT,OAAA,CAACnB,MAAM,CAACiJ,GAAG;QACTC,SAAS,EAAC,iBAAiB;QAC3BC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAG,CAAE;QAC/BjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAAAb,QAAA,eAE9BvI,OAAA;UAAK+H,SAAS,EAAC,iBAAiB;UAAAQ,QAAA,gBAC9BvI,OAAA,CAACV,OAAO;YAACyI,SAAS,EAAC,iBAAiB;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDjJ,OAAA;YAAAuI,QAAA,EAAM;UAAqC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDjJ,OAAA;QAAKwJ,GAAG,EAAE3I;MAAe;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNjJ,OAAA;MAAK+H,SAAS,EAAC,yBAAyB;MAAAQ,QAAA,eACtCvI,OAAA;QAAK+H,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChCvI,OAAA;UACEwK,KAAK,EAAEjK,UAAW;UAClBkK,QAAQ,EAAGtD,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAEzD,cAAe;UAC3B0D,WAAW,EAAC,oDAAoD;UAChE7C,SAAS,EAAC,eAAe;UACzB8C,IAAI,EAAC;QAAG;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFjJ,OAAA,CAACnB,MAAM,CAAC2J,MAAM;UACZT,SAAS,EAAC,UAAU;UACpBU,OAAO,EAAE/B,iBAAkB;UAC3BoE,QAAQ,EAAE,CAACvK,UAAU,CAACoG,IAAI,CAAC,CAAC,IAAIlG,SAAU;UAC1CiI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,eAE1BvI,OAAA,CAAChB,IAAI;YAAC6J,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAC7I,EAAA,CAthCIH,gBAAgB;AAAA8K,EAAA,GAAhB9K,gBAAgB;AAwhCtB,eAAeA,gBAAgB;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}