{"ast": null, "code": "class WithPromise {\n  constructor() {\n    this.updateFinished();\n  }\n  get finished() {\n    return this._finished;\n  }\n  updateFinished() {\n    this._finished = new Promise(resolve => {\n      this.resolve = resolve;\n    });\n  }\n  notifyFinished() {\n    this.resolve();\n  }\n  /**\n   * Allows the animation to be awaited.\n   *\n   * @deprecated Use `finished` instead.\n   */\n  then(onResolve, onReject) {\n    return this.finished.then(onResolve, onReject);\n  }\n}\nexport { WithPromise };", "map": {"version": 3, "names": ["WithPromise", "constructor", "updateFinished", "finished", "_finished", "Promise", "resolve", "notifyFinished", "then", "onResolve", "onReject"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/animation/utils/WithPromise.mjs"], "sourcesContent": ["class WithPromise {\n    constructor() {\n        this.updateFinished();\n    }\n    get finished() {\n        return this._finished;\n    }\n    updateFinished() {\n        this._finished = new Promise((resolve) => {\n            this.resolve = resolve;\n        });\n    }\n    notifyFinished() {\n        this.resolve();\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve, onReject);\n    }\n}\n\nexport { WithPromise };\n"], "mappings": "AAAA,MAAMA,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACAF,cAAcA,CAAA,EAAG;IACb,IAAI,CAACE,SAAS,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;MACtC,IAAI,CAACA,OAAO,GAAGA,OAAO;IAC1B,CAAC,CAAC;EACN;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAACD,OAAO,CAAC,CAAC;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIE,IAAIA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACtB,OAAO,IAAI,CAACP,QAAQ,CAACK,IAAI,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAClD;AACJ;AAEA,SAASV,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}