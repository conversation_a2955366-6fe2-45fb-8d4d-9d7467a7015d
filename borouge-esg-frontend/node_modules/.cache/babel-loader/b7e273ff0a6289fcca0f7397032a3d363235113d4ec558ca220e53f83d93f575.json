{"ast": null, "code": "import { animations } from '../../../motion/features/animations.mjs';\nimport { drag } from '../../../motion/features/drag.mjs';\nimport { gestureAnimations } from '../../../motion/features/gestures.mjs';\nimport { layout } from '../../../motion/features/layout.mjs';\nimport { createMotionComponentFactory } from '../create-factory.mjs';\nimport { createDomVisualElement } from '../../dom/create-visual-element.mjs';\nconst createMotionComponent = /*@__PURE__*/createMotionComponentFactory({\n  ...animations,\n  ...gestureAnimations,\n  ...drag,\n  ...layout\n}, createDomVisualElement);\nexport { createMotionComponent };", "map": {"version": 3, "names": ["animations", "drag", "gestureAnimations", "layout", "createMotionComponentFactory", "createDomVisualElement", "createMotionComponent"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/components/motion/create.mjs"], "sourcesContent": ["import { animations } from '../../../motion/features/animations.mjs';\nimport { drag } from '../../../motion/features/drag.mjs';\nimport { gestureAnimations } from '../../../motion/features/gestures.mjs';\nimport { layout } from '../../../motion/features/layout.mjs';\nimport { createMotionComponentFactory } from '../create-factory.mjs';\nimport { createDomVisualElement } from '../../dom/create-visual-element.mjs';\n\nconst createMotionComponent = /*@__PURE__*/ createMotionComponentFactory({\n    ...animations,\n    ...gestureAnimations,\n    ...drag,\n    ...layout,\n}, createDomVisualElement);\n\nexport { createMotionComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,yCAAyC;AACpE,SAASC,IAAI,QAAQ,mCAAmC;AACxD,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,MAAM,QAAQ,qCAAqC;AAC5D,SAASC,4BAA4B,QAAQ,uBAAuB;AACpE,SAASC,sBAAsB,QAAQ,qCAAqC;AAE5E,MAAMC,qBAAqB,GAAG,aAAcF,4BAA4B,CAAC;EACrE,GAAGJ,UAAU;EACb,GAAGE,iBAAiB;EACpB,GAAGD,IAAI;EACP,GAAGE;AACP,CAAC,EAAEE,sBAAsB,CAAC;AAE1B,SAASC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}