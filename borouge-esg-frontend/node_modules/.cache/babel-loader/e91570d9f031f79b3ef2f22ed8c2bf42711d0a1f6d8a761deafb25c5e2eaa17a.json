{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"11 19 2 12 11 5 11 19\",\n  key: \"14yba5\"\n}], [\"polygon\", {\n  points: \"22 19 13 12 22 5 22 19\",\n  key: \"1pi1cj\"\n}]];\nconst Rewind = createLucideIcon(\"rewind\", __iconNode);\nexport { __iconNode, Rewind as default };", "map": {"version": 3, "names": ["__iconNode", "points", "key", "Rewind", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/rewind.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['polygon', { points: '11 19 2 12 11 5 11 19', key: '14yba5' }],\n  ['polygon', { points: '22 19 13 12 22 5 22 19', key: '1pi1cj' }],\n];\n\n/**\n * @component @name Rewind\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjExIDE5IDIgMTIgMTEgNSAxMSAxOSIgLz4KICA8cG9seWdvbiBwb2ludHM9IjIyIDE5IDEzIDEyIDIyIDUgMjIgMTkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rewind\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rewind = createLucideIcon('rewind', __iconNode);\n\nexport default Rewind;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,SAAW;EAAEC,MAAA,EAAQ,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,SAAW;EAAED,MAAA,EAAQ,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACjE;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}