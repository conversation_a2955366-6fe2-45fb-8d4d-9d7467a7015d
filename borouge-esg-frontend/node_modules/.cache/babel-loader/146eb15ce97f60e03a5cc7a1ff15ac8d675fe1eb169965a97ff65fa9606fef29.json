{"ast": null, "code": "import { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\nlet id = 0;\nconst AnimateSharedLayout = ({\n  children\n}) => {\n  React.useEffect(() => {\n    invariant(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n  }, []);\n  return jsx(LayoutGroup, {\n    id: useConstant(() => `asl-${id++}`),\n    children: children\n  });\n};\nexport { AnimateSharedLayout };", "map": {"version": 3, "names": ["jsx", "invariant", "React", "useConstant", "LayoutGroup", "id", "AnimateSharedLayout", "children", "useEffect"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/components/AnimateSharedLayout.mjs"], "sourcesContent": ["import { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\n\nlet id = 0;\nconst AnimateSharedLayout = ({ children }) => {\n    React.useEffect(() => {\n        invariant(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n    }, []);\n    return (jsx(LayoutGroup, { id: useConstant(() => `asl-${id++}`), children: children }));\n};\n\nexport { AnimateSharedLayout };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,IAAIC,EAAE,GAAG,CAAC;AACV,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC1CL,KAAK,CAACM,SAAS,CAAC,MAAM;IAClBP,SAAS,CAAC,KAAK,EAAE,yGAAyG,CAAC;EAC/H,CAAC,EAAE,EAAE,CAAC;EACN,OAAQD,GAAG,CAACI,WAAW,EAAE;IAAEC,EAAE,EAAEF,WAAW,CAAC,MAAM,OAAOE,EAAE,EAAE,EAAE,CAAC;IAAEE,QAAQ,EAAEA;EAAS,CAAC,CAAC;AAC1F,CAAC;AAED,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}