{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowLeft, Send, Download, ExternalLink, TrendingUp, AlertTriangle, Info, Loader2, Copy, Share2, ChevronDown, ChevronUp, Target, DollarSign, Clock, Users } from 'lucide-react';\nimport './ConversationView.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationView = ({\n  initialQuery,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const responseHeaderRef = useRef(null);\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 2000);\n    }\n  }, [initialQuery]);\n  const generateMockResponse = query => {\n    const lowerQuery = query.toLowerCase();\n\n    // Generate multiple prioritized articles based on query\n    if (lowerQuery.includes('eu') || lowerQuery.includes('europe') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n      return generatePrioritizedArticles('eu_regulation', query);\n    } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon') || lowerQuery.includes('border')) {\n      return generatePrioritizedArticles('cbam', query);\n    } else if (lowerQuery.includes('esg') || lowerQuery.includes('comprehensive')) {\n      return generatePrioritizedArticles('comprehensive_esg', query);\n    } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycl')) {\n      return generatePrioritizedArticles('circular_economy', query);\n    } else if (lowerQuery.includes('competitor') || lowerQuery.includes('sabic') || lowerQuery.includes('dow')) {\n      return generatePrioritizedArticles('competitive', query);\n    } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n      return generatePrioritizedArticles('market_trends', query);\n    } else {\n      return generatePrioritizedArticles('general', query); // Default response\n    }\n  };\n  const generatePrioritizedArticles = (category, originalQuery) => {\n    const articles = [];\n    const usedArticleTypes = new Set(); // Track used article types to prevent duplicates\n\n    // Priority 1: Critical regulatory compliance (highest priority)\n    // Priority 2: High financial impact (€100M+ revenue/cost impact)\n    // Priority 3: Immediate competitive threats\n    // Priority 4: Time-sensitive opportunities\n    // Priority 5: Medium-term strategic considerations (lowest priority)\n\n    const addUniqueArticle = (articleGenerator, articleType, priority, priorityLabel) => {\n      if (!usedArticleTypes.has(articleType)) {\n        articles.push({\n          ...articleGenerator(),\n          priority,\n          priorityLabel,\n          articleId: articleType // Add unique identifier\n        });\n        usedArticleTypes.add(articleType);\n      }\n    };\n    switch (category) {\n      case 'eu_regulation':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 3, 'Immediate Competitive Threat');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 4, 'Time-Sensitive Opportunity');\n        break;\n      case 'cbam':\n        addUniqueArticle(generateCBAMReport, 'cbam', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 3, 'Immediate Competitive Threat');\n        break;\n      case 'comprehensive_esg':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateComprehensiveESGReport, 'comprehensive_esg', 2, 'High Financial Impact');\n        addUniqueArticle(generateCBAMReport, 'cbam', 3, 'Immediate Competitive Threat');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 4, 'Time-Sensitive Opportunity');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 5, 'Medium-Term Strategic');\n        break;\n      case 'circular_economy':\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 3, 'Immediate Competitive Threat');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 4, 'Time-Sensitive Opportunity');\n        break;\n      case 'competitive':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 2, 'High Financial Impact');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 3, 'Immediate Competitive Threat');\n        break;\n      case 'market_trends':\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 2, 'High Financial Impact');\n        addUniqueArticle(generateComprehensiveESGReport, 'comprehensive_esg', 3, 'Immediate Competitive Threat');\n        break;\n      default:\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 3, 'Immediate Competitive Threat');\n    }\n\n    // Sort articles by priority (1 = highest, 5 = lowest)\n    articles.sort((a, b) => a.priority - b.priority);\n\n    // Debug logging to check for duplicates\n    console.log('Generated articles for query:', originalQuery);\n    console.log('Articles:', articles.map(a => ({\n      id: a.articleId,\n      type: a.reportType,\n      priority: a.priority\n    })));\n    return {\n      responseType: 'multi_article',\n      originalQuery: originalQuery,\n      totalArticles: articles.length,\n      articles: articles\n    };\n  };\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [{\n        type: \"regulatory\",\n        title: \"Mandatory Recycled Content Requirements\",\n        impact: \"Critical\",\n        description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n        action: \"Secure recycling partnerships immediately\"\n      }, {\n        type: \"financial\",\n        title: \"Investment Requirements\",\n        impact: \"High\",\n        description: \"$800M-1.2B needed for compliance infrastructure\",\n        action: \"Establish dedicated compliance budget\"\n      }, {\n        type: \"competitive\",\n        title: \"SABIC Competitive Threat\",\n        impact: \"High\",\n        description: \"Risk losing 15-20% EU market share to competitors\",\n        action: \"Accelerate sustainable product development\"\n      }],\n      detailedFindings: [{\n        type: \"regulatory\",\n        title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n        details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n        confidence: 98,\n        timeline: \"Implementation: Jan 2025, Full compliance: 2030\",\n        isBorogueSpecific: false\n      }, {\n        type: \"financial\",\n        title: \"Compliance Investment Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n        details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2027\",\n        isBorogueSpecific: false\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Landscape Shift\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n        details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n        confidence: 92,\n        timeline: \"Competitive threat: Immediate\",\n        isBorogueSpecific: false\n      }, {\n        type: \"market\",\n        title: \"Borouge Strategic Partnership with ALPLA Group\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Exclusive partnership opportunity with ALPLA Group to secure 40% of required recycled content supply for EU compliance.\",\n        details: \"Strategic alliance would provide Borouge with preferential access to high-quality recycled polyolefins from ALPLA's European network, ensuring compliance while creating competitive moat against SABIC and Dow.\",\n        confidence: 94,\n        timeline: \"Partnership agreement needed within 6 months\",\n        isBorogueSpecific: true\n      }, {\n        type: \"technology\",\n        title: \"Borouge Advanced Chemical Recycling Initiative\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Proprietary chemical recycling technology development could position Borouge as market leader in circular polyolefins.\",\n        details: \"Investment in advanced pyrolysis and depolymerization technologies would enable Borouge to process mixed plastic waste into virgin-quality feedstock, creating new revenue streams worth €300-500M annually by 2030.\",\n        confidence: 87,\n        timeline: \"36 months to commercial deployment\",\n        isBorogueSpecific: true\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [{\n        priority: \"Critical\",\n        action: \"Form EU Compliance Task Force\",\n        timeline: \"Next 30 days\",\n        investment: \"$5M\",\n        description: \"Immediate action team to coordinate regulatory response\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Recycling Partnerships\",\n        timeline: \"6 months\",\n        investment: \"$200-300M\",\n        description: \"Lock in technology partnerships before competitors\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line\",\n        timeline: \"12 months\",\n        investment: \"$150M\",\n        description: \"Develop premium recycled content products\"\n      }],\n      allRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish EU Regulatory Compliance Task Force\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$5M\",\n        description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Chemical Recycling Technology Partnerships\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$200-300M\",\n        description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line Development\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$150M\",\n        description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n      }, {\n        priority: \"Medium\",\n        action: \"Establish European Recycling Hub\",\n        timeline: \"24 months (Q4 2025)\",\n        investment: \"$400-500M\",\n        description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n      }],\n      competitiveBenchmarking: [{\n        company: \"SABIC\",\n        strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n        advantage: \"First-mover in chemical recycling, strong EU presence\",\n        weakness: \"Higher cost base, limited feedstock security\"\n      }, {\n        company: \"Dow\",\n        strategy: \"Advanced recycling partnerships, circular design principles\",\n        advantage: \"Technology leadership, established partnerships\",\n        weakness: \"Focus on specialty applications, limited commodity exposure\"\n      }, {\n        company: \"LyondellBasell\",\n        strategy: \"Molecular recycling technology, circular economy solutions\",\n        advantage: \"Integrated technology development, scale advantages\",\n        weakness: \"Limited EU manufacturing footprint\"\n      }],\n      sources: [{\n        title: \"EU Packaging & Packaging Waste Regulation\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2024-01-15\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Environment Agency Circular Economy Report\",\n        url: \"eea.europa.eu\",\n        date: \"2024-02-01\",\n        type: \"research\",\n        confidence: \"High\"\n      }, {\n        title: \"SABIC Circular Economy Strategy Update\",\n        url: \"sabic.com\",\n        date: \"2024-01-30\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"Plastics Europe Market Data 2024\",\n        url: \"plasticseurope.org\",\n        date: \"2024-02-15\",\n        type: \"industry\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey Circular Economy in Chemicals\",\n        url: \"mckinsey.com\",\n        date: \"2024-01-20\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }, {\n        title: \"Wood Mackenzie Petrochemicals Outlook\",\n        url: \"woodmac.com\",\n        date: \"2024-02-10\",\n        type: \"market\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [{\n        type: \"financial\",\n        title: \"Direct CBAM Cost Impact\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n        details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n        confidence: 92,\n        timeline: \"Implementation: January 2026\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Advantage Opportunity\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n        details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n        confidence: 88,\n        timeline: \"Advantage period: 2026-2035\"\n      }, {\n        type: \"technology\",\n        title: \"Carbon Reduction Investment Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n        details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2030\"\n      }, {\n        type: \"regulatory\",\n        title: \"CBAM Reporting and Verification Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n        details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n        confidence: 95,\n        timeline: \"Reporting starts: October 2023\"\n      }, {\n        type: \"market\",\n        title: \"Premium Low-Carbon Product Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n        details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n        confidence: 78,\n        timeline: \"Market development: 2025-2030\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Implement Comprehensive Carbon Accounting System\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$10M\",\n        description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n      }, {\n        priority: \"High\",\n        action: \"Accelerate Renewable Energy Integration\",\n        timeline: \"18 months (Q3 2025)\",\n        investment: \"$200M\",\n        description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n      }, {\n        priority: \"High\",\n        action: \"Develop Low-Carbon Product Certification\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$5M\",\n        description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n      }],\n      sources: [{\n        title: \"EU CBAM Regulation 2023/956\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2023-05-17\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Commission CBAM Implementation Guide\",\n        url: \"taxation-customs.ec.europa.eu\",\n        date: \"2024-01-10\",\n        type: \"guidance\",\n        confidence: \"Official\"\n      }, {\n        title: \"IEA Petrochemicals Carbon Intensity Database\",\n        url: \"iea.org\",\n        date: \"2024-02-05\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [{\n        type: \"environmental\",\n        title: \"Decarbonization Pathway Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n        details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n        confidence: 90,\n        timeline: \"Transformation period: 2024-2050\"\n      }, {\n        type: \"social\",\n        title: \"UAE Emiratization and Skills Development\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n        details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n        confidence: 85,\n        timeline: \"Achievement target: 2030\"\n      }, {\n        type: \"governance\",\n        title: \"ESG Reporting and Transparency Enhancement\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n        details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n        confidence: 88,\n        timeline: \"Full compliance: 2025\"\n      }, {\n        type: \"financial\",\n        title: \"Sustainable Finance and Green Bonds Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n        details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n        confidence: 82,\n        timeline: \"Financing window: 2024-2027\"\n      }, {\n        type: \"technology\",\n        title: \"Digital ESG Management Platform\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n        details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n        confidence: 75,\n        timeline: \"Implementation: 2024-2025\"\n      }],\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish Chief Sustainability Officer Role\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$2M\",\n        description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Comprehensive Decarbonization Program\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$500M\",\n        description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n      }, {\n        priority: \"High\",\n        action: \"Implement Advanced Emiratization Strategy\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$100M\",\n        description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n      }],\n      sources: [{\n        title: \"UAE Vision 2071 Strategic Framework\",\n        url: \"government.ae\",\n        date: \"2023-12-01\",\n        type: \"policy\",\n        confidence: \"Official\"\n      }, {\n        title: \"ADNOC Sustainability Strategy 2030\",\n        url: \"adnoc.ae\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey ESG in Chemicals Industry\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }]\n    };\n  };\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Recycled Polyethylene Market Growth\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n        details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n        confidence: 88,\n        timeline: \"Market expansion: 2024-2030\"\n      }],\n      sources: [{\n        title: \"Ellen MacArthur Foundation Circular Economy Report\",\n        url: \"ellenmacarthurfoundation.org\",\n        date: \"2024-01-10\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [{\n        type: \"competitive\",\n        title: \"SABIC Circular Economy Leadership\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n        details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n        confidence: 95,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      sources: [{\n        title: \"SABIC Sustainability Strategy 2030\",\n        url: \"sabic.com\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Sustainable Packaging Demand Surge\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n        details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n        confidence: 90,\n        timeline: \"Market shift: 2024-2027\"\n      }],\n      sources: [{\n        title: \"McKinsey Sustainable Packaging Report\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 1500);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n  const copyMessage = content => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"conversation-view\",\n    initial: {\n      opacity: 0,\n      x: 20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    exit: {\n      opacity: 0,\n      x: -20\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversation-header\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"back-btn\",\n        onClick: onBack,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this), \"Back to Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-actions\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          onClick: exportToPDF,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Share2, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this), \"Share\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.map((message, messageIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `message ${message.type}`,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-response\",\n              children: typeof message.content === 'object' ? message.content.responseType === 'multi_article' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"multi-article-response\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"response-header\",\n                  ref: messageIndex === messages.length - 1 ? responseHeaderRef : null,\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: [\"Intelligence Analysis: \", message.content.originalQuery]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"articles-summary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"articles-count\",\n                      children: [message.content.totalArticles, \" Articles Found\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"priority-note\",\n                      children: \"Sorted by criticality and business impact\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 27\n                }, this), message.content.articles.map((article, articleIndex) => {\n                  var _article$keyFindings, _article$sources, _article$sources2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"article-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"article-priority-header\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"priority-badge-large\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"priority-number\",\n                          children: [\"#\", articleIndex + 1]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 747,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"priority-label\",\n                          children: article.priorityLabel\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 748,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"intelligence-report simplified\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"report-header\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"report-title-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            children: article.reportType || 'ESG Intelligence Report'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 755,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"report-actions\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"copy-btn secondary\",\n                              onClick: () => copyMessage(article),\n                              title: \"Copy article\",\n                              children: /*#__PURE__*/_jsxDEV(Copy, {\n                                size: 14\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 762,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 757,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 756,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 753,\n                        columnNumber: 33\n                      }, this), (article.problem || article.executiveSummary) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"problem-solution-summary\",\n                        children: [article.problem && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"problem-statement\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"problem-icon\",\n                            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 20\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 774,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 773,\n                            columnNumber: 41\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"problem-content\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                              children: \"Business Challenge\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 777,\n                              columnNumber: 43\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              children: article.problem\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 778,\n                              columnNumber: 43\n                            }, this), article.impact && article.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"impact-highlight\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"impact-text\",\n                                children: article.impact\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 781,\n                                columnNumber: 47\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"urgency-text\",\n                                children: article.urgency\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 782,\n                                columnNumber: 47\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 780,\n                              columnNumber: 45\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 776,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 772,\n                          columnNumber: 39\n                        }, this), article.opportunity && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"opportunity-statement\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"opportunity-icon\",\n                            children: /*#__PURE__*/_jsxDEV(Target, {\n                              size: 20\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 792,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 791,\n                            columnNumber: 41\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"opportunity-content\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                              children: \"Market Opportunity\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 795,\n                              columnNumber: 43\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              children: article.opportunity\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 796,\n                              columnNumber: 43\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 794,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 790,\n                          columnNumber: 39\n                        }, this), article.executiveSummary && !article.problem && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"executive-summary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            children: \"Executive Summary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 803,\n                            columnNumber: 41\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: article.executiveSummary\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 804,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 802,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 770,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"key-insights\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Critical Findings\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 812,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"insights-grid\",\n                          children: (article.topFindings || ((_article$keyFindings = article.keyFindings) === null || _article$keyFindings === void 0 ? void 0 : _article$keyFindings.slice(0, 3)) || []).map((finding, index) => {\n                            var _finding$impact, _finding$impact2;\n                            return /*#__PURE__*/_jsxDEV(motion.div, {\n                              className: \"insight-card\",\n                              initial: {\n                                opacity: 0,\n                                y: 10\n                              },\n                              animate: {\n                                opacity: 1,\n                                y: 0\n                              },\n                              transition: {\n                                delay: index * 0.1\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"insight-header\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: `impact-indicator ${(_finding$impact = finding.impact) === null || _finding$impact === void 0 ? void 0 : _finding$impact.toLowerCase()}`,\n                                  children: [finding.impact === 'Critical' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                    size: 16\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 824,\n                                    columnNumber: 79\n                                  }, this), finding.impact === 'High' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                    size: 16\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 825,\n                                    columnNumber: 75\n                                  }, this), finding.impact === 'Medium' && /*#__PURE__*/_jsxDEV(Info, {\n                                    size: 16\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 826,\n                                    columnNumber: 77\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 823,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: `impact-label ${(_finding$impact2 = finding.impact) === null || _finding$impact2 === void 0 ? void 0 : _finding$impact2.toLowerCase()}`,\n                                  children: finding.impact\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 828,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 822,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                                children: finding.title\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 832,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                children: finding.description\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 833,\n                                columnNumber: 41\n                              }, this), finding.action && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"quick-action\",\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Action:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 836,\n                                  columnNumber: 45\n                                }, this), \" \", finding.action]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 835,\n                                columnNumber: 43\n                              }, this)]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 815,\n                              columnNumber: 39\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 813,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 811,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"detailed-sections\",\n                        children: [article.detailedFindings && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"collapsible-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"section-toggle\",\n                            onClick: () => toggleSection(`${message.id}-${articleIndex}`, 'detailed-findings'),\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Detailed Analysis\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 855,\n                              columnNumber: 41\n                            }, this), expandedSections[`${message.id}-${articleIndex}-detailed-findings`] ? /*#__PURE__*/_jsxDEV(ChevronUp, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 857,\n                              columnNumber: 43\n                            }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 857,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 851,\n                            columnNumber: 39\n                          }, this), expandedSections[`${message.id}-${articleIndex}-detailed-findings`] && /*#__PURE__*/_jsxDEV(motion.div, {\n                            className: \"section-content\",\n                            initial: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            animate: {\n                              opacity: 1,\n                              height: 'auto'\n                            },\n                            exit: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"detailed-findings\",\n                              children: (article.detailedFindings || []).map((finding, index) => {\n                                var _finding$impact3;\n                                return /*#__PURE__*/_jsxDEV(motion.div, {\n                                  className: `finding-card ${finding.isBorogueSpecific ? 'borouge-recommendation' : ''}`,\n                                  \"data-type\": finding.type,\n                                  initial: {\n                                    opacity: 0,\n                                    x: -20\n                                  },\n                                  animate: {\n                                    opacity: 1,\n                                    x: 0\n                                  },\n                                  transition: {\n                                    delay: index * 0.1\n                                  },\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"finding-header\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"finding-icon\",\n                                      style: {\n                                        background: finding.type === 'regulatory' ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)' : finding.type === 'financial' ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)' : finding.type === 'competitive' ? 'linear-gradient(135deg, #0066cc 0%, #3b82f6 100%)' : finding.type === 'market' ? 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)' : finding.type === 'technology' ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)' : finding.type === 'environmental' ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)' : finding.type === 'social' ? 'linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)' : finding.type === 'governance' ? 'linear-gradient(135deg, #4338ca 0%, #6366f1 100%)' : 'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)'\n                                      },\n                                      children: [finding.type === 'regulatory' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 893,\n                                        columnNumber: 79\n                                      }, this), finding.type === 'financial' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 894,\n                                        columnNumber: 78\n                                      }, this), finding.type === 'competitive' && /*#__PURE__*/_jsxDEV(Users, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 895,\n                                        columnNumber: 80\n                                      }, this), finding.type === 'market' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 896,\n                                        columnNumber: 75\n                                      }, this), finding.type === 'technology' && /*#__PURE__*/_jsxDEV(Info, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 897,\n                                        columnNumber: 79\n                                      }, this), finding.type === 'environmental' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 898,\n                                        columnNumber: 82\n                                      }, this), finding.type === 'social' && /*#__PURE__*/_jsxDEV(Users, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 899,\n                                        columnNumber: 75\n                                      }, this), finding.type === 'governance' && /*#__PURE__*/_jsxDEV(Info, {\n                                        size: 20\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 900,\n                                        columnNumber: 79\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 879,\n                                      columnNumber: 43\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"finding-title\",\n                                      children: finding.title\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 902,\n                                      columnNumber: 43\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"finding-badges\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                        className: `impact-badge ${(_finding$impact3 = finding.impact) === null || _finding$impact3 === void 0 ? void 0 : _finding$impact3.toLowerCase()}`,\n                                        children: [finding.impact, \" Impact\"]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 904,\n                                        columnNumber: 45\n                                      }, this), finding.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                                        className: `urgency-badge ${finding.urgency.toLowerCase()}`,\n                                        children: finding.urgency\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 908,\n                                        columnNumber: 47\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 903,\n                                      columnNumber: 43\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 878,\n                                    columnNumber: 41\n                                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                    className: \"finding-description\",\n                                    children: finding.description\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 914,\n                                    columnNumber: 41\n                                  }, this), finding.details && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"finding-details\",\n                                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                                      children: finding.details\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 917,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 916,\n                                    columnNumber: 43\n                                  }, this), finding.timeline && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"finding-timeline\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                      children: \"Timeline:\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 922,\n                                      columnNumber: 45\n                                    }, this), \" \", finding.timeline]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 921,\n                                    columnNumber: 43\n                                  }, this), finding.confidence && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"confidence-bar\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"confidence-label\",\n                                      children: [\"Confidence: \", finding.confidence, \"%\"]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 927,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"confidence-progress\",\n                                      children: /*#__PURE__*/_jsxDEV(motion.div, {\n                                        className: \"confidence-fill\",\n                                        initial: {\n                                          width: 0\n                                        },\n                                        animate: {\n                                          width: `${finding.confidence}%`\n                                        },\n                                        transition: {\n                                          duration: 1,\n                                          delay: 0.5\n                                        }\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 929,\n                                        columnNumber: 47\n                                      }, this)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 928,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 926,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, index, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 870,\n                                  columnNumber: 39\n                                }, this);\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 868,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 862,\n                            columnNumber: 41\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"collapsible-section\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"section-toggle\",\n                            onClick: () => toggleSection(`${message.id}-${articleIndex}`, 'sources'),\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: [\"Sources & References (\", ((_article$sources = article.sources) === null || _article$sources === void 0 ? void 0 : _article$sources.length) || 0, \")\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 952,\n                              columnNumber: 39\n                            }, this), expandedSections[`${message.id}-${articleIndex}-sources`] ? /*#__PURE__*/_jsxDEV(ChevronUp, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 954,\n                              columnNumber: 41\n                            }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                              size: 16\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 954,\n                              columnNumber: 67\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 948,\n                            columnNumber: 37\n                          }, this), expandedSections[`${message.id}-${articleIndex}-sources`] && /*#__PURE__*/_jsxDEV(motion.div, {\n                            className: \"section-content\",\n                            initial: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            animate: {\n                              opacity: 1,\n                              height: 'auto'\n                            },\n                            exit: {\n                              opacity: 0,\n                              height: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"sources-section\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"sources-grid\",\n                                children: (_article$sources2 = article.sources) === null || _article$sources2 === void 0 ? void 0 : _article$sources2.map((source, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"source-card\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"source-header\",\n                                    children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                                      size: 14\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 970,\n                                      columnNumber: 51\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-title\",\n                                      children: source.title\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 971,\n                                      columnNumber: 51\n                                    }, this), source.confidence && /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: `source-confidence ${source.confidence.toLowerCase()}`,\n                                      children: source.confidence\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 973,\n                                      columnNumber: 53\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 969,\n                                    columnNumber: 49\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"source-meta\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-url\",\n                                      children: source.url\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 979,\n                                      columnNumber: 51\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-date\",\n                                      children: source.date\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 980,\n                                      columnNumber: 51\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"source-type\",\n                                      children: source.type\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 981,\n                                      columnNumber: 51\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 978,\n                                    columnNumber: 49\n                                  }, this)]\n                                }, index, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 968,\n                                  columnNumber: 47\n                                }, this))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 966,\n                                columnNumber: 43\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 965,\n                              columnNumber: 41\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 959,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 947,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 847,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 752,\n                      columnNumber: 31\n                    }, this)]\n                  }, article.articleId || articleIndex, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 29\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"intelligence-report simplified\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"report-title-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: message.content.reportType || 'ESG Intelligence Report'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 999,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"report-actions\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"copy-btn secondary\",\n                        onClick: () => copyMessage(message.content),\n                        title: \"Copy report\",\n                        children: /*#__PURE__*/_jsxDEV(Copy, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1006,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1001,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1000,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 996,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"simple-response\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 709,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-message\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(Loader2, {\n            className: \"loading-spinner\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Analyzing ESG data and regulations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1028,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1040,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-input-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-input-box\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask a follow-up question about ESG intelligence...\",\n          className: \"message-input\",\n          rows: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"send-btn\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || isLoading,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1054,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 667,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationView, \"0epH/2K6gs9Tzy97Gdji0lsw1cE=\");\n_c = ConversationView;\nexport default ConversationView;\nvar _c;\n$RefreshReg$(_c, \"ConversationView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "ArrowLeft", "Send", "Download", "ExternalLink", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Loader2", "Copy", "Share2", "ChevronDown", "ChevronUp", "Target", "DollarSign", "Clock", "Users", "jsxDEV", "_jsxDEV", "ConversationView", "initialQuery", "onBack", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "isLoading", "setIsLoading", "expandedSections", "setExpandedSections", "messagesEndRef", "responseHeaderRef", "toggleSection", "messageId", "section", "prev", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "userMessage", "id", "type", "content", "timestamp", "Date", "setTimeout", "aiResponse", "generateMockResponse", "block", "inline", "query", "lowerQuery", "toLowerCase", "includes", "generatePrioritizedArticles", "category", "originalQuery", "articles", "usedArticleTypes", "Set", "addUniqueArticle", "articleGenerator", "articleType", "priority", "priority<PERSON>abel", "has", "push", "articleId", "add", "generateEURegulationReport", "generateCBAMReport", "generateCompetitorReport", "generateCircularEconomyReport", "generateMarketTrendsReport", "generateComprehensiveESGReport", "sort", "a", "b", "console", "log", "map", "reportType", "responseType", "totalArticles", "length", "problem", "impact", "urgency", "opportunity", "topFindings", "title", "description", "action", "detailedFindings", "details", "confidence", "timeline", "isBorogueSpecific", "marketImpact", "revenueAtRisk", "investmentRequired", "timelineForCompliance", "marketOpportunity", "riskAssessment", "high", "medium", "low", "nextSteps", "investment", "allRecommendations", "competitiveBenchmarking", "company", "strategy", "advantage", "weakness", "sources", "url", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyFindings", "strategicRecommendations", "handleSendMessage", "trim", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "exportToPDF", "copyMessage", "navigator", "clipboard", "writeText", "JSON", "stringify", "div", "className", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "children", "button", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "messageIndex", "y", "toLocaleTimeString", "hour", "minute", "ref", "article", "articleIndex", "_article$keyFindings", "_article$sources", "_article$sources2", "slice", "finding", "index", "_finding$impact", "_finding$impact2", "delay", "height", "_finding$impact3", "style", "background", "width", "source", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ArrowLeft,\n  Send,\n  Download,\n  ExternalLink,\n  TrendingUp,\n  AlertTriangle,\n  Info,\n  Loader2,\n  Copy,\n  Share2,\n  ChevronDown,\n  ChevronUp,\n  Target,\n  DollarSign,\n  Clock,\n  Users\n} from 'lucide-react';\nimport './ConversationView.css';\n\nconst ConversationView = ({ initialQuery, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const responseHeaderRef = useRef(null);\n\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 2000);\n    }\n  }, [initialQuery]);\n\n  const generateMockResponse = (query) => {\n    const lowerQuery = query.toLowerCase();\n\n    // Generate multiple prioritized articles based on query\n    if (lowerQuery.includes('eu') || lowerQuery.includes('europe') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n      return generatePrioritizedArticles('eu_regulation', query);\n    } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon') || lowerQuery.includes('border')) {\n      return generatePrioritizedArticles('cbam', query);\n    } else if (lowerQuery.includes('esg') || lowerQuery.includes('comprehensive')) {\n      return generatePrioritizedArticles('comprehensive_esg', query);\n    } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycl')) {\n      return generatePrioritizedArticles('circular_economy', query);\n    } else if (lowerQuery.includes('competitor') || lowerQuery.includes('sabic') || lowerQuery.includes('dow')) {\n      return generatePrioritizedArticles('competitive', query);\n    } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n      return generatePrioritizedArticles('market_trends', query);\n    } else {\n      return generatePrioritizedArticles('general', query); // Default response\n    }\n  };\n\n  const generatePrioritizedArticles = (category, originalQuery) => {\n    const articles = [];\n    const usedArticleTypes = new Set(); // Track used article types to prevent duplicates\n\n    // Priority 1: Critical regulatory compliance (highest priority)\n    // Priority 2: High financial impact (€100M+ revenue/cost impact)\n    // Priority 3: Immediate competitive threats\n    // Priority 4: Time-sensitive opportunities\n    // Priority 5: Medium-term strategic considerations (lowest priority)\n\n    const addUniqueArticle = (articleGenerator, articleType, priority, priorityLabel) => {\n      if (!usedArticleTypes.has(articleType)) {\n        articles.push({\n          ...articleGenerator(),\n          priority,\n          priorityLabel,\n          articleId: articleType // Add unique identifier\n        });\n        usedArticleTypes.add(articleType);\n      }\n    };\n\n    switch (category) {\n      case 'eu_regulation':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 3, 'Immediate Competitive Threat');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 4, 'Time-Sensitive Opportunity');\n        break;\n      case 'cbam':\n        addUniqueArticle(generateCBAMReport, 'cbam', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 3, 'Immediate Competitive Threat');\n        break;\n      case 'comprehensive_esg':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateComprehensiveESGReport, 'comprehensive_esg', 2, 'High Financial Impact');\n        addUniqueArticle(generateCBAMReport, 'cbam', 3, 'Immediate Competitive Threat');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 4, 'Time-Sensitive Opportunity');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 5, 'Medium-Term Strategic');\n        break;\n      case 'circular_economy':\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 2, 'High Financial Impact');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 3, 'Immediate Competitive Threat');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 4, 'Time-Sensitive Opportunity');\n        break;\n      case 'competitive':\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 2, 'High Financial Impact');\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 3, 'Immediate Competitive Threat');\n        break;\n      case 'market_trends':\n        addUniqueArticle(generateMarketTrendsReport, 'market_trends', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCircularEconomyReport, 'circular_economy', 2, 'High Financial Impact');\n        addUniqueArticle(generateComprehensiveESGReport, 'comprehensive_esg', 3, 'Immediate Competitive Threat');\n        break;\n      default:\n        addUniqueArticle(generateEURegulationReport, 'eu_regulation', 1, 'Critical Regulatory Compliance');\n        addUniqueArticle(generateCBAMReport, 'cbam', 2, 'High Financial Impact');\n        addUniqueArticle(generateCompetitorReport, 'competitive', 3, 'Immediate Competitive Threat');\n    }\n\n    // Sort articles by priority (1 = highest, 5 = lowest)\n    articles.sort((a, b) => a.priority - b.priority);\n\n    // Debug logging to check for duplicates\n    console.log('Generated articles for query:', originalQuery);\n    console.log('Articles:', articles.map(a => ({ id: a.articleId, type: a.reportType, priority: a.priority })));\n\n    return {\n      responseType: 'multi_article',\n      originalQuery: originalQuery,\n      totalArticles: articles.length,\n      articles: articles\n    };\n  };\n\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [\n        {\n          type: \"regulatory\",\n          title: \"Mandatory Recycled Content Requirements\",\n          impact: \"Critical\",\n          description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n          action: \"Secure recycling partnerships immediately\"\n        },\n        {\n          type: \"financial\",\n          title: \"Investment Requirements\",\n          impact: \"High\",\n          description: \"$800M-1.2B needed for compliance infrastructure\",\n          action: \"Establish dedicated compliance budget\"\n        },\n        {\n          type: \"competitive\",\n          title: \"SABIC Competitive Threat\",\n          impact: \"High\",\n          description: \"Risk losing 15-20% EU market share to competitors\",\n          action: \"Accelerate sustainable product development\"\n        }\n      ],\n      detailedFindings: [\n        {\n          type: \"regulatory\",\n          title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n          details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n          confidence: 98,\n          timeline: \"Implementation: Jan 2025, Full compliance: 2030\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"financial\",\n          title: \"Compliance Investment Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n          details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2027\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Landscape Shift\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n          details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n          confidence: 92,\n          timeline: \"Competitive threat: Immediate\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"market\",\n          title: \"Borouge Strategic Partnership with ALPLA Group\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Exclusive partnership opportunity with ALPLA Group to secure 40% of required recycled content supply for EU compliance.\",\n          details: \"Strategic alliance would provide Borouge with preferential access to high-quality recycled polyolefins from ALPLA's European network, ensuring compliance while creating competitive moat against SABIC and Dow.\",\n          confidence: 94,\n          timeline: \"Partnership agreement needed within 6 months\",\n          isBorogueSpecific: true\n        },\n        {\n          type: \"technology\",\n          title: \"Borouge Advanced Chemical Recycling Initiative\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Proprietary chemical recycling technology development could position Borouge as market leader in circular polyolefins.\",\n          details: \"Investment in advanced pyrolysis and depolymerization technologies would enable Borouge to process mixed plastic waste into virgin-quality feedstock, creating new revenue streams worth €300-500M annually by 2030.\",\n          confidence: 87,\n          timeline: \"36 months to commercial deployment\",\n          isBorogueSpecific: true\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [\n        {\n          priority: \"Critical\",\n          action: \"Form EU Compliance Task Force\",\n          timeline: \"Next 30 days\",\n          investment: \"$5M\",\n          description: \"Immediate action team to coordinate regulatory response\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Recycling Partnerships\",\n          timeline: \"6 months\",\n          investment: \"$200-300M\",\n          description: \"Lock in technology partnerships before competitors\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line\",\n          timeline: \"12 months\",\n          investment: \"$150M\",\n          description: \"Develop premium recycled content products\"\n        }\n      ],\n      allRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish EU Regulatory Compliance Task Force\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$5M\",\n          description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Chemical Recycling Technology Partnerships\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$200-300M\",\n          description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line Development\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$150M\",\n          description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n        },\n        {\n          priority: \"Medium\",\n          action: \"Establish European Recycling Hub\",\n          timeline: \"24 months (Q4 2025)\",\n          investment: \"$400-500M\",\n          description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n        }\n      ],\n      competitiveBenchmarking: [\n        {\n          company: \"SABIC\",\n          strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n          advantage: \"First-mover in chemical recycling, strong EU presence\",\n          weakness: \"Higher cost base, limited feedstock security\"\n        },\n        {\n          company: \"Dow\",\n          strategy: \"Advanced recycling partnerships, circular design principles\",\n          advantage: \"Technology leadership, established partnerships\",\n          weakness: \"Focus on specialty applications, limited commodity exposure\"\n        },\n        {\n          company: \"LyondellBasell\",\n          strategy: \"Molecular recycling technology, circular economy solutions\",\n          advantage: \"Integrated technology development, scale advantages\",\n          weakness: \"Limited EU manufacturing footprint\"\n        }\n      ],\n      sources: [\n        { title: \"EU Packaging & Packaging Waste Regulation\", url: \"eur-lex.europa.eu\", date: \"2024-01-15\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Environment Agency Circular Economy Report\", url: \"eea.europa.eu\", date: \"2024-02-01\", type: \"research\", confidence: \"High\" },\n        { title: \"SABIC Circular Economy Strategy Update\", url: \"sabic.com\", date: \"2024-01-30\", type: \"corporate\", confidence: \"High\" },\n        { title: \"Plastics Europe Market Data 2024\", url: \"plasticseurope.org\", date: \"2024-02-15\", type: \"industry\", confidence: \"High\" },\n        { title: \"McKinsey Circular Economy in Chemicals\", url: \"mckinsey.com\", date: \"2024-01-20\", type: \"consulting\", confidence: \"Medium\" },\n        { title: \"Wood Mackenzie Petrochemicals Outlook\", url: \"woodmac.com\", date: \"2024-02-10\", type: \"market\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [\n        {\n          type: \"financial\",\n          title: \"Direct CBAM Cost Impact\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n          details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n          confidence: 92,\n          timeline: \"Implementation: January 2026\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Advantage Opportunity\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n          details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n          confidence: 88,\n          timeline: \"Advantage period: 2026-2035\"\n        },\n        {\n          type: \"technology\",\n          title: \"Carbon Reduction Investment Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n          details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2030\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"CBAM Reporting and Verification Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n          details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n          confidence: 95,\n          timeline: \"Reporting starts: October 2023\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Low-Carbon Product Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n          details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n          confidence: 78,\n          timeline: \"Market development: 2025-2030\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Implement Comprehensive Carbon Accounting System\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$10M\",\n          description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Accelerate Renewable Energy Integration\",\n          timeline: \"18 months (Q3 2025)\",\n          investment: \"$200M\",\n          description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Develop Low-Carbon Product Certification\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$5M\",\n          description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n        }\n      ],\n      sources: [\n        { title: \"EU CBAM Regulation 2023/956\", url: \"eur-lex.europa.eu\", date: \"2023-05-17\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Commission CBAM Implementation Guide\", url: \"taxation-customs.ec.europa.eu\", date: \"2024-01-10\", type: \"guidance\", confidence: \"Official\" },\n        { title: \"IEA Petrochemicals Carbon Intensity Database\", url: \"iea.org\", date: \"2024-02-05\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [\n        {\n          type: \"environmental\",\n          title: \"Decarbonization Pathway Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n          details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n          confidence: 90,\n          timeline: \"Transformation period: 2024-2050\"\n        },\n        {\n          type: \"social\",\n          title: \"UAE Emiratization and Skills Development\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n          details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n          confidence: 85,\n          timeline: \"Achievement target: 2030\"\n        },\n        {\n          type: \"governance\",\n          title: \"ESG Reporting and Transparency Enhancement\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n          details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n          confidence: 88,\n          timeline: \"Full compliance: 2025\"\n        },\n        {\n          type: \"financial\",\n          title: \"Sustainable Finance and Green Bonds Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n          details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n          confidence: 82,\n          timeline: \"Financing window: 2024-2027\"\n        },\n        {\n          type: \"technology\",\n          title: \"Digital ESG Management Platform\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n          details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n          confidence: 75,\n          timeline: \"Implementation: 2024-2025\"\n        }\n      ],\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish Chief Sustainability Officer Role\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$2M\",\n          description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Comprehensive Decarbonization Program\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$500M\",\n          description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Implement Advanced Emiratization Strategy\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$100M\",\n          description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n        }\n      ],\n      sources: [\n        { title: \"UAE Vision 2071 Strategic Framework\", url: \"government.ae\", date: \"2023-12-01\", type: \"policy\", confidence: \"Official\" },\n        { title: \"ADNOC Sustainability Strategy 2030\", url: \"adnoc.ae\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" },\n        { title: \"McKinsey ESG in Chemicals Industry\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"Medium\" }\n      ]\n    };\n  };\n\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Recycled Polyethylene Market Growth\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n          details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n          confidence: 88,\n          timeline: \"Market expansion: 2024-2030\"\n        }\n      ],\n      sources: [\n        { title: \"Ellen MacArthur Foundation Circular Economy Report\", url: \"ellenmacarthurfoundation.org\", date: \"2024-01-10\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [\n        {\n          type: \"competitive\",\n          title: \"SABIC Circular Economy Leadership\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n          details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n          confidence: 95,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      sources: [\n        { title: \"SABIC Sustainability Strategy 2030\", url: \"sabic.com\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Sustainable Packaging Demand Surge\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n          details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n          confidence: 90,\n          timeline: \"Market shift: 2024-2027\"\n        }\n      ],\n      sources: [\n        { title: \"McKinsey Sustainable Packaging Report\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n\n        // Scroll to response header after a brief delay to allow rendering\n        setTimeout(() => {\n          if (responseHeaderRef.current) {\n            responseHeaderRef.current.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }, 100);\n      }, 1500);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n\n  const copyMessage = (content) => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n\n  return (\n    <motion.div\n      className=\"conversation-view\"\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      exit={{ opacity: 0, x: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"conversation-header\">\n        <motion.button\n          className=\"back-btn\"\n          onClick={onBack}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <ArrowLeft size={20} />\n          Back to Search\n        </motion.button>\n\n        <div className=\"conversation-actions\">\n          <motion.button\n            className=\"action-btn\"\n            onClick={exportToPDF}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Download size={18} />\n            Export\n          </motion.button>\n          <motion.button\n            className=\"action-btn\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Share2 size={18} />\n            Share\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"messages-container\">\n        <AnimatePresence>\n          {messages.map((message, messageIndex) => (\n            <motion.div\n              key={message.id}\n              className={`message ${message.type}`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {message.type === 'user' ? (\n                <div className=\"user-message\">\n                  <div className=\"message-content\">{message.content}</div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"ai-message\">\n                  <div className=\"ai-response\">\n                    {typeof message.content === 'object' ? (\n                      message.content.responseType === 'multi_article' ? (\n                        <div className=\"multi-article-response\">\n                          <div\n                            className=\"response-header\"\n                            ref={messageIndex === messages.length - 1 ? responseHeaderRef : null}\n                          >\n                            <h3>Intelligence Analysis: {message.content.originalQuery}</h3>\n                            <div className=\"articles-summary\">\n                              <span className=\"articles-count\">{message.content.totalArticles} Articles Found</span>\n                              <span className=\"priority-note\">Sorted by criticality and business impact</span>\n                            </div>\n                          </div>\n\n                          {message.content.articles.map((article, articleIndex) => (\n                            <div key={article.articleId || articleIndex} className=\"article-container\">\n                              <div className=\"article-priority-header\">\n                                <div className=\"priority-badge-large\">\n                                  <span className=\"priority-number\">#{articleIndex + 1}</span>\n                                  <span className=\"priority-label\">{article.priorityLabel}</span>\n                                </div>\n                              </div>\n\n                              <div className=\"intelligence-report simplified\">\n                                <div className=\"report-header\">\n                                  <div className=\"report-title-section\">\n                                    <h3>{article.reportType || 'ESG Intelligence Report'}</h3>\n                                    <div className=\"report-actions\">\n                                      <button\n                                        className=\"copy-btn secondary\"\n                                        onClick={() => copyMessage(article)}\n                                        title=\"Copy article\"\n                                      >\n                                        <Copy size={14} />\n                                      </button>\n                                    </div>\n                                  </div>\n                                </div>\n\n                                {/* Problem-Solution Summary */}\n                                {(article.problem || article.executiveSummary) && (\n                                  <div className=\"problem-solution-summary\">\n                                    {article.problem && (\n                                      <div className=\"problem-statement\">\n                                        <div className=\"problem-icon\">\n                                          <AlertTriangle size={20} />\n                                        </div>\n                                        <div className=\"problem-content\">\n                                          <h4>Business Challenge</h4>\n                                          <p>{article.problem}</p>\n                                          {article.impact && article.urgency && (\n                                            <div className=\"impact-highlight\">\n                                              <span className=\"impact-text\">{article.impact}</span>\n                                              <span className=\"urgency-text\">{article.urgency}</span>\n                                            </div>\n                                          )}\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {article.opportunity && (\n                                      <div className=\"opportunity-statement\">\n                                        <div className=\"opportunity-icon\">\n                                          <Target size={20} />\n                                        </div>\n                                        <div className=\"opportunity-content\">\n                                          <h4>Market Opportunity</h4>\n                                          <p>{article.opportunity}</p>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {article.executiveSummary && !article.problem && (\n                                      <div className=\"executive-summary\">\n                                        <h4>Executive Summary</h4>\n                                        <p>{article.executiveSummary}</p>\n                                      </div>\n                                    )}\n                                  </div>\n                                )}\n\n                                {/* Key Insights - Top 3 */}\n                                <div className=\"key-insights\">\n                                  <h4>Critical Findings</h4>\n                                  <div className=\"insights-grid\">\n                                    {(article.topFindings || article.keyFindings?.slice(0, 3) || []).map((finding, index) => (\n                                      <motion.div\n                                        key={index}\n                                        className=\"insight-card\"\n                                        initial={{ opacity: 0, y: 10 }}\n                                        animate={{ opacity: 1, y: 0 }}\n                                        transition={{ delay: index * 0.1 }}\n                                      >\n                                        <div className=\"insight-header\">\n                                          <div className={`impact-indicator ${finding.impact?.toLowerCase()}`}>\n                                            {finding.impact === 'Critical' && <AlertTriangle size={16} />}\n                                            {finding.impact === 'High' && <TrendingUp size={16} />}\n                                            {finding.impact === 'Medium' && <Info size={16} />}\n                                          </div>\n                                          <span className={`impact-label ${finding.impact?.toLowerCase()}`}>\n                                            {finding.impact}\n                                          </span>\n                                        </div>\n                                        <h5>{finding.title}</h5>\n                                        <p>{finding.description}</p>\n                                        {finding.action && (\n                                          <div className=\"quick-action\">\n                                            <strong>Action:</strong> {finding.action}\n                                          </div>\n                                        )}\n                                      </motion.div>\n                                    ))}\n                                  </div>\n                                </div>\n\n\n\n                                {/* Collapsible Detailed Analysis */}\n                                <div className=\"detailed-sections\">\n                                  {/* Detailed Findings */}\n                                  {article.detailedFindings && (\n                                    <div className=\"collapsible-section\">\n                                      <button\n                                        className=\"section-toggle\"\n                                        onClick={() => toggleSection(`${message.id}-${articleIndex}`, 'detailed-findings')}\n                                      >\n                                        <span>Detailed Analysis</span>\n                                        {expandedSections[`${message.id}-${articleIndex}-detailed-findings`] ?\n                                          <ChevronUp size={16} /> : <ChevronDown size={16} />\n                                        }\n                                      </button>\n\n                                      {expandedSections[`${message.id}-${articleIndex}-detailed-findings`] && (\n                                        <motion.div\n                                          className=\"section-content\"\n                                          initial={{ opacity: 0, height: 0 }}\n                                          animate={{ opacity: 1, height: 'auto' }}\n                                          exit={{ opacity: 0, height: 0 }}\n                                        >\n                                          <div className=\"detailed-findings\">\n                                            {(article.detailedFindings || []).map((finding, index) => (\n                                      <motion.div\n                                        key={index}\n                                        className={`finding-card ${finding.isBorogueSpecific ? 'borouge-recommendation' : ''}`}\n                                        data-type={finding.type}\n                                        initial={{ opacity: 0, x: -20 }}\n                                        animate={{ opacity: 1, x: 0 }}\n                                        transition={{ delay: index * 0.1 }}\n                                      >\n                                        <div className=\"finding-header\">\n                                          <div\n                                            className=\"finding-icon\"\n                                            style={{\n                                              background: finding.type === 'regulatory' ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)' :\n                                                         finding.type === 'financial' ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)' :\n                                                         finding.type === 'competitive' ? 'linear-gradient(135deg, #0066cc 0%, #3b82f6 100%)' :\n                                                         finding.type === 'market' ? 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)' :\n                                                         finding.type === 'technology' ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)' :\n                                                         finding.type === 'environmental' ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)' :\n                                                         finding.type === 'social' ? 'linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)' :\n                                                         finding.type === 'governance' ? 'linear-gradient(135deg, #4338ca 0%, #6366f1 100%)' :\n                                                         'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)'\n                                            }}\n                                          >\n                                            {finding.type === 'regulatory' && <AlertTriangle size={20} />}\n                                            {finding.type === 'financial' && <TrendingUp size={20} />}\n                                            {finding.type === 'competitive' && <Users size={20} />}\n                                            {finding.type === 'market' && <TrendingUp size={20} />}\n                                            {finding.type === 'technology' && <Info size={20} />}\n                                            {finding.type === 'environmental' && <AlertTriangle size={20} />}\n                                            {finding.type === 'social' && <Users size={20} />}\n                                            {finding.type === 'governance' && <Info size={20} />}\n                                          </div>\n                                          <div className=\"finding-title\">{finding.title}</div>\n                                          <div className=\"finding-badges\">\n                                            <div className={`impact-badge ${finding.impact?.toLowerCase()}`}>\n                                              {finding.impact} Impact\n                                            </div>\n                                            {finding.urgency && (\n                                              <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>\n                                                {finding.urgency}\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                        <p className=\"finding-description\">{finding.description}</p>\n                                        {finding.details && (\n                                          <div className=\"finding-details\">\n                                            <p>{finding.details}</p>\n                                          </div>\n                                        )}\n                                        {finding.timeline && (\n                                          <div className=\"finding-timeline\">\n                                            <strong>Timeline:</strong> {finding.timeline}\n                                          </div>\n                                        )}\n                                        {finding.confidence && (\n                                          <div className=\"confidence-bar\">\n                                            <div className=\"confidence-label\">Confidence: {finding.confidence}%</div>\n                                            <div className=\"confidence-progress\">\n                                              <motion.div\n                                                className=\"confidence-fill\"\n                                                initial={{ width: 0 }}\n                                                animate={{ width: `${finding.confidence}%` }}\n                                                transition={{ duration: 1, delay: 0.5 }}\n                                              />\n                                            </div>\n                                          </div>\n                                        )}\n                                      </motion.div>\n                                    ))}\n                                  </div>\n                                </motion.div>\n                              )}\n                            </div>\n                          )}\n\n                                  {/* Sources Section */}\n                                  <div className=\"collapsible-section\">\n                                    <button\n                                      className=\"section-toggle\"\n                                      onClick={() => toggleSection(`${message.id}-${articleIndex}`, 'sources')}\n                                    >\n                                      <span>Sources & References ({article.sources?.length || 0})</span>\n                                      {expandedSections[`${message.id}-${articleIndex}-sources`] ?\n                                        <ChevronUp size={16} /> : <ChevronDown size={16} />\n                                      }\n                                    </button>\n\n                                    {expandedSections[`${message.id}-${articleIndex}-sources`] && (\n                                      <motion.div\n                                        className=\"section-content\"\n                                        initial={{ opacity: 0, height: 0 }}\n                                        animate={{ opacity: 1, height: 'auto' }}\n                                        exit={{ opacity: 0, height: 0 }}\n                                      >\n                                        <div className=\"sources-section\">\n                                          <div className=\"sources-grid\">\n                                            {article.sources?.map((source, index) => (\n                                              <div key={index} className=\"source-card\">\n                                                <div className=\"source-header\">\n                                                  <ExternalLink size={14} />\n                                                  <span className=\"source-title\">{source.title}</span>\n                                                  {source.confidence && (\n                                                    <span className={`source-confidence ${source.confidence.toLowerCase()}`}>\n                                                      {source.confidence}\n                                                    </span>\n                                                  )}\n                                                </div>\n                                                <div className=\"source-meta\">\n                                                  <span className=\"source-url\">{source.url}</span>\n                                                  <span className=\"source-date\">{source.date}</span>\n                                                  <span className=\"source-type\">{source.type}</span>\n                                                </div>\n                                              </div>\n                                            ))}\n                                          </div>\n                                        </div>\n                                      </motion.div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"intelligence-report simplified\">\n                          <div className=\"report-header\">\n                            <div className=\"report-title-section\">\n                              <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>\n                              <div className=\"report-actions\">\n                                <button\n                                  className=\"copy-btn secondary\"\n                                  onClick={() => copyMessage(message.content)}\n                                  title=\"Copy report\"\n                                >\n                                  <Copy size={14} />\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                          {/* Single article content would go here - keeping existing structure */}\n                        </div>\n                      )\n                    ) : (\n                      <div className=\"simple-response\">{message.content}</div>\n                    )}\n                  </div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            className=\"loading-message\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"loading-content\">\n              <Loader2 className=\"loading-spinner\" size={20} />\n              <span>Analyzing ESG data and regulations...</span>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"message-input-container\">\n        <div className=\"message-input-box\">\n          <textarea\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask a follow-up question about ESG intelligence...\"\n            className=\"message-input\"\n            rows=\"1\"\n          />\n          <motion.button\n            className=\"send-btn\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || isLoading}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Send size={18} />\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ConversationView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,cAAc;AACrB,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMmC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMkC,iBAAiB,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAEtC,MAAMmC,aAAa,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC5CL,mBAAmB,CAACM,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE,GAAG,CAACC,IAAI,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAP,cAAc,CAACQ,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED5C,SAAS,CAAC,MAAM;IACdwC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd1B,SAAS,CAAC,MAAM;IACd,IAAIuB,YAAY,EAAE;MAChB;MACA,MAAMsB,WAAW,GAAG;QAClBC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEzB,YAAY;QACrB0B,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDvB,WAAW,CAAC,CAACkB,WAAW,CAAC,CAAC;MAC1Bd,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAoB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAAC9B,YAAY,CAAC;UAC3C0B,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDvB,WAAW,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CrB,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACAoB,UAAU,CAAC,MAAM;UACf,IAAIhB,iBAAiB,CAACO,OAAO,EAAE;YAC7BP,iBAAiB,CAACO,OAAO,CAACC,cAAc,CAAC;cACvCC,QAAQ,EAAE,QAAQ;cAClBU,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAM8B,oBAAoB,GAAIG,KAAK,IAAK;IACtC,MAAMC,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;;IAEtC;IACA,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MACrI,OAAOC,2BAA2B,CAAC,eAAe,EAAEJ,KAAK,CAAC;IAC5D,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACxG,OAAOC,2BAA2B,CAAC,MAAM,EAAEJ,KAAK,CAAC;IACnD,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7E,OAAOC,2BAA2B,CAAC,mBAAmB,EAAEJ,KAAK,CAAC;IAChE,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3E,OAAOC,2BAA2B,CAAC,kBAAkB,EAAEJ,KAAK,CAAC;IAC/D,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1G,OAAOC,2BAA2B,CAAC,aAAa,EAAEJ,KAAK,CAAC;IAC1D,CAAC,MAAM,IAAIC,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxE,OAAOC,2BAA2B,CAAC,eAAe,EAAEJ,KAAK,CAAC;IAC5D,CAAC,MAAM;MACL,OAAOI,2BAA2B,CAAC,SAAS,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAMI,2BAA2B,GAAGA,CAACC,QAAQ,EAAEC,aAAa,KAAK;IAC/D,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEpC;IACA;IACA;IACA;IACA;;IAEA,MAAMC,gBAAgB,GAAGA,CAACC,gBAAgB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,KAAK;MACnF,IAAI,CAACN,gBAAgB,CAACO,GAAG,CAACH,WAAW,CAAC,EAAE;QACtCL,QAAQ,CAACS,IAAI,CAAC;UACZ,GAAGL,gBAAgB,CAAC,CAAC;UACrBE,QAAQ;UACRC,aAAa;UACbG,SAAS,EAAEL,WAAW,CAAC;QACzB,CAAC,CAAC;QACFJ,gBAAgB,CAACU,GAAG,CAACN,WAAW,CAAC;MACnC;IACF,CAAC;IAED,QAAQP,QAAQ;MACd,KAAK,eAAe;QAClBK,gBAAgB,CAACS,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGT,gBAAgB,CAACU,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACxEV,gBAAgB,CAACW,wBAAwB,EAAE,aAAa,EAAE,CAAC,EAAE,8BAA8B,CAAC;QAC5FX,gBAAgB,CAACY,6BAA6B,EAAE,kBAAkB,EAAE,CAAC,EAAE,4BAA4B,CAAC;QACpG;MACF,KAAK,MAAM;QACTZ,gBAAgB,CAACU,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,gCAAgC,CAAC;QACjFV,gBAAgB,CAACS,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACzFT,gBAAgB,CAACa,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,8BAA8B,CAAC;QAChG;MACF,KAAK,mBAAmB;QACtBb,gBAAgB,CAACS,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGT,gBAAgB,CAACc,8BAA8B,EAAE,mBAAmB,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACjGd,gBAAgB,CAACU,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,8BAA8B,CAAC;QAC/EV,gBAAgB,CAACY,6BAA6B,EAAE,kBAAkB,EAAE,CAAC,EAAE,4BAA4B,CAAC;QACpGZ,gBAAgB,CAACa,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACzF;MACF,KAAK,kBAAkB;QACrBb,gBAAgB,CAACY,6BAA6B,EAAE,kBAAkB,EAAE,CAAC,EAAE,gCAAgC,CAAC;QACxGZ,gBAAgB,CAACS,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACzFT,gBAAgB,CAACW,wBAAwB,EAAE,aAAa,EAAE,CAAC,EAAE,8BAA8B,CAAC;QAC5FX,gBAAgB,CAACa,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,4BAA4B,CAAC;QAC9F;MACF,KAAK,aAAa;QAChBb,gBAAgB,CAACS,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGT,gBAAgB,CAACW,wBAAwB,EAAE,aAAa,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACrFX,gBAAgB,CAACa,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,8BAA8B,CAAC;QAChG;MACF,KAAK,eAAe;QAClBb,gBAAgB,CAACa,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGb,gBAAgB,CAACY,6BAA6B,EAAE,kBAAkB,EAAE,CAAC,EAAE,uBAAuB,CAAC;QAC/FZ,gBAAgB,CAACc,8BAA8B,EAAE,mBAAmB,EAAE,CAAC,EAAE,8BAA8B,CAAC;QACxG;MACF;QACEd,gBAAgB,CAACS,0BAA0B,EAAE,eAAe,EAAE,CAAC,EAAE,gCAAgC,CAAC;QAClGT,gBAAgB,CAACU,kBAAkB,EAAE,MAAM,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACxEV,gBAAgB,CAACW,wBAAwB,EAAE,aAAa,EAAE,CAAC,EAAE,8BAA8B,CAAC;IAChG;;IAEA;IACAd,QAAQ,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACb,QAAQ,GAAGc,CAAC,CAACd,QAAQ,CAAC;;IAEhD;IACAe,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEvB,aAAa,CAAC;IAC3DsB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEtB,QAAQ,CAACuB,GAAG,CAACJ,CAAC,KAAK;MAAEpC,EAAE,EAAEoC,CAAC,CAACT,SAAS;MAAE1B,IAAI,EAAEmC,CAAC,CAACK,UAAU;MAAElB,QAAQ,EAAEa,CAAC,CAACb;IAAS,CAAC,CAAC,CAAC,CAAC;IAE5G,OAAO;MACLmB,YAAY,EAAE,eAAe;MAC7B1B,aAAa,EAAEA,aAAa;MAC5B2B,aAAa,EAAE1B,QAAQ,CAAC2B,MAAM;MAC9B3B,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC;EAED,MAAMY,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLY,UAAU,EAAE,iCAAiC;MAC7CI,OAAO,EAAE,qDAAqD;MAC9DC,MAAM,EAAE,uEAAuE;MAC/EC,OAAO,EAAE,kCAAkC;MAC3CC,WAAW,EAAE,8DAA8D;MAC3EC,WAAW,EAAE,CACX;QACEhD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,yCAAyC;QAChDJ,MAAM,EAAE,UAAU;QAClBK,WAAW,EAAE,uEAAuE;QACpFC,MAAM,EAAE;MACV,CAAC,EACD;QACEnD,IAAI,EAAE,WAAW;QACjBiD,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,iDAAiD;QAC9DC,MAAM,EAAE;MACV,CAAC,EACD;QACEnD,IAAI,EAAE,aAAa;QACnBiD,KAAK,EAAE,0BAA0B;QACjCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,mDAAmD;QAChEC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,gBAAgB,EAAE,CAChB;QACEpD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,uDAAuD;QAC9DJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,mJAAmJ;QAChKG,OAAO,EAAE,8MAA8M;QACvNC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,iDAAiD;QAC3DC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACExD,IAAI,EAAE,WAAW;QACjBiD,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,qHAAqH;QAClIG,OAAO,EAAE,0LAA0L;QACnMC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,8BAA8B;QACxCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACExD,IAAI,EAAE,aAAa;QACnBiD,KAAK,EAAE,6BAA6B;QACpCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,6GAA6G;QAC1HG,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,+BAA+B;QACzCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACExD,IAAI,EAAE,QAAQ;QACdiD,KAAK,EAAE,gDAAgD;QACvDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,kNAAkN;QAC3NC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,8CAA8C;QACxDC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACExD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,gDAAgD;QACvDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,wHAAwH;QACrIG,OAAO,EAAE,sNAAsN;QAC/NC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,oCAAoC;QAC9CC,iBAAiB,EAAE;MACrB,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,yBAAyB;QACxCC,kBAAkB,EAAE,YAAY;QAChCC,qBAAqB,EAAE,yBAAyB;QAChDC,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;QACxFC,MAAM,EAAE,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,8BAA8B,CAAC;QACxGC,GAAG,EAAE,CAAC,qBAAqB,EAAE,+BAA+B;MAC9D,CAAC;MACDC,SAAS,EAAE,CACT;QACE5C,QAAQ,EAAE,UAAU;QACpB6B,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,cAAc;QACxBY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,UAAU;QACpBY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,iCAAiC;QACzCI,QAAQ,EAAE,WAAW;QACrBY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDkB,kBAAkB,EAAE,CAClB;QACE9C,QAAQ,EAAE,UAAU;QACpB6B,MAAM,EAAE,+CAA+C;QACvDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,mDAAmD;QAC3DI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,QAAQ;QAClB6B,MAAM,EAAE,kCAAkC;QAC1CI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDmB,uBAAuB,EAAE,CACvB;QACEC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,qEAAqE;QAC/EC,SAAS,EAAE,uDAAuD;QAClEC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,6DAA6D;QACvEC,SAAS,EAAE,iDAAiD;QAC5DC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE,4DAA4D;QACtEC,SAAS,EAAE,qDAAqD;QAChEC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,2CAA2C;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,YAAY;QAAEsD,UAAU,EAAE;MAAW,CAAC,EAChJ;QAAEL,KAAK,EAAE,qDAAqD;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,UAAU;QAAEsD,UAAU,EAAE;MAAO,CAAC,EAChJ;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,WAAW;QAAEsD,UAAU,EAAE;MAAO,CAAC,EAChI;QAAEL,KAAK,EAAE,kCAAkC;QAAE0B,GAAG,EAAE,oBAAoB;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,UAAU;QAAEsD,UAAU,EAAE;MAAO,CAAC,EAClI;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,YAAY;QAAEsD,UAAU,EAAE;MAAS,CAAC,EACtI;QAAEL,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,aAAa;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,QAAQ;QAAEsD,UAAU,EAAE;MAAO,CAAC;IAElI,CAAC;EACH,CAAC;EAED,MAAMzB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACLW,UAAU,EAAE,2DAA2D;MACvEqC,gBAAgB,EAAE,qRAAqR;MACvSC,WAAW,EAAE,CACX;QACE9E,IAAI,EAAE,WAAW;QACjBiD,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,6IAA6I;QACtJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,aAAa;QACnBiD,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uGAAuG;QACpHG,OAAO,EAAE,0JAA0J;QACnKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,gFAAgF;QAC7FG,OAAO,EAAE,kJAAkJ;QAC3JC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,8CAA8C;QACrDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0GAA0G;QACvHG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,QAAQ;QACdiD,KAAK,EAAE,wCAAwC;QAC/CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDE,YAAY,EAAE;QACZC,aAAa,EAAE,2BAA2B;QAC1CC,kBAAkB,EAAE,4BAA4B;QAChDC,qBAAqB,EAAE,kCAAkC;QACzDC,iBAAiB,EAAE;MACrB,CAAC;MACDkB,wBAAwB,EAAE,CACxB;QACEzD,QAAQ,EAAE,UAAU;QACpB6B,MAAM,EAAE,kDAAkD;QAC1DI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,MAAM;QAClBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,yCAAyC;QACjDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,0CAA0C;QAClDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDwB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,6BAA6B;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,YAAY;QAAEsD,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,+CAA+C;QAAE0B,GAAG,EAAE,+BAA+B;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,UAAU;QAAEsD,UAAU,EAAE;MAAW,CAAC,EAC9J;QAAEL,KAAK,EAAE,8CAA8C;QAAE0B,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,UAAU;QAAEsD,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMrB,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,OAAO;MACLO,UAAU,EAAE,uCAAuC;MACnDqC,gBAAgB,EAAE,oWAAoW;MACtXC,WAAW,EAAE,CACX;QACE9E,IAAI,EAAE,eAAe;QACrBiD,KAAK,EAAE,sCAAsC;QAC7CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,uKAAuK;QAChLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,QAAQ;QACdiD,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uHAAuH;QACpIG,OAAO,EAAE,sKAAsK;QAC/KC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,4CAA4C;QACnDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,4GAA4G;QACzHG,OAAO,EAAE,6JAA6J;QACtKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,WAAW;QACjBiD,KAAK,EAAE,iDAAiD;QACxDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,+JAA+J;QACxKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEvD,IAAI,EAAE,YAAY;QAClBiD,KAAK,EAAE,iCAAiC;QACxCJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,gJAAgJ;QACzJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDwB,wBAAwB,EAAE,CACxB;QACEzD,QAAQ,EAAE,UAAU;QACpB6B,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,8CAA8C;QACtDI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACE5B,QAAQ,EAAE,MAAM;QAChB6B,MAAM,EAAE,2CAA2C;QACnDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDwB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,qCAAqC;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,QAAQ;QAAEsD,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,WAAW;QAAEsD,UAAU,EAAE;MAAO,CAAC,EAC3H;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,YAAY;QAAEsD,UAAU,EAAE;MAAS,CAAC;IAEtI,CAAC;EACH,CAAC;EAED,MAAMvB,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,OAAO;MACLS,UAAU,EAAE,sCAAsC;MAClDqC,gBAAgB,EAAE,sQAAsQ;MACxRC,WAAW,EAAE,CACX;QACE9E,IAAI,EAAE,QAAQ;QACdiD,KAAK,EAAE,qCAAqC;QAC5CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0EAA0E;QACvFG,OAAO,EAAE,wIAAwI;QACjJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,oDAAoD;QAAE0B,GAAG,EAAE,8BAA8B;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,UAAU;QAAEsD,UAAU,EAAE;MAAO,CAAC;IAElK,CAAC;EACH,CAAC;EAED,MAAMxB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,OAAO;MACLU,UAAU,EAAE,mCAAmC;MAC/CqC,gBAAgB,EAAE,kPAAkP;MACpQC,WAAW,EAAE,CACX;QACE9E,IAAI,EAAE,aAAa;QACnBiD,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,wHAAwH;QACrIG,OAAO,EAAE,wHAAwH;QACjIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,WAAW;QAAEsD,UAAU,EAAE;MAAO,CAAC;IAEhI,CAAC;EACH,CAAC;EAED,MAAMtB,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLQ,UAAU,EAAE,kCAAkC;MAC9CqC,gBAAgB,EAAE,gPAAgP;MAClQC,WAAW,EAAE,CACX;QACE9E,IAAI,EAAE,QAAQ;QACdiD,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,0HAA0H;QACnIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAE5E,IAAI,EAAE,YAAY;QAAEsD,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAInG,UAAU,CAACoG,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMnF,WAAW,GAAG;QAClBC,EAAE,EAAEpB,QAAQ,CAACgE,MAAM,GAAG,CAAC;QACvB3C,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEpB,UAAU;QACnBqB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDvB,WAAW,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,WAAW,CAAC,CAAC;MAC3ChB,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAoB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAEpB,QAAQ,CAACgE,MAAM,GAAG,CAAC;UACvB3C,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAACzB,UAAU,CAAC;UACzCqB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDvB,WAAW,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CrB,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACAoB,UAAU,CAAC,MAAM;UACf,IAAIhB,iBAAiB,CAACO,OAAO,EAAE;YAC7BP,iBAAiB,CAACO,OAAO,CAACC,cAAc,CAAC;cACvCC,QAAQ,EAAE,QAAQ;cAClBU,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAM0E,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBN,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAlD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMkD,WAAW,GAAIvF,OAAO,IAAK;IAC/BwF,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,OAAO1F,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG2F,IAAI,CAACC,SAAS,CAAC5F,OAAO,CAAC,CAAC;EAChG,CAAC;EAED,oBACE3B,OAAA,CAACnB,MAAM,CAAC2I,GAAG;IACTC,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAC7BG,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9BjI,OAAA;MAAKyH,SAAS,EAAC,qBAAqB;MAAAQ,QAAA,gBAClCjI,OAAA,CAACnB,MAAM,CAACqJ,MAAM;QACZT,SAAS,EAAC,UAAU;QACpBU,OAAO,EAAEhI,MAAO;QAChBiI,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAJ,QAAA,gBAE1BjI,OAAA,CAACjB,SAAS;UAACwJ,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhB3I,OAAA;QAAKyH,SAAS,EAAC,sBAAsB;QAAAQ,QAAA,gBACnCjI,OAAA,CAACnB,MAAM,CAACqJ,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAElB,WAAY;UACrBmB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1BjI,OAAA,CAACf,QAAQ;YAACsJ,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB3I,OAAA,CAACnB,MAAM,CAACqJ,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1BjI,OAAA,CAACR,MAAM;YAAC+I,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3I,OAAA;MAAKyH,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,gBACjCjI,OAAA,CAAClB,eAAe;QAAAmJ,QAAA,EACb5H,QAAQ,CAAC4D,GAAG,CAAC,CAAC2E,OAAO,EAAEC,YAAY,kBAClC7I,OAAA,CAACnB,MAAM,CAAC2I,GAAG;UAETC,SAAS,EAAE,WAAWmB,OAAO,CAAClH,IAAI,EAAG;UACrCgG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAG,CAAE;UAC/BjB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAE,CAAE;UAC9BhB,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7Bf,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAC,QAAA,EAE7BW,OAAO,CAAClH,IAAI,KAAK,MAAM,gBACtB1B,OAAA;YAAKyH,SAAS,EAAC,cAAc;YAAAQ,QAAA,gBAC3BjI,OAAA;cAAKyH,SAAS,EAAC,iBAAiB;cAAAQ,QAAA,EAAEW,OAAO,CAACjH;YAAO;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD3I,OAAA;cAAKyH,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BW,OAAO,CAAChH,SAAS,CAACmH,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN3I,OAAA;YAAKyH,SAAS,EAAC,YAAY;YAAAQ,QAAA,gBACzBjI,OAAA;cAAKyH,SAAS,EAAC,aAAa;cAAAQ,QAAA,EACzB,OAAOW,OAAO,CAACjH,OAAO,KAAK,QAAQ,GAClCiH,OAAO,CAACjH,OAAO,CAACwC,YAAY,KAAK,eAAe,gBAC9CnE,OAAA;gBAAKyH,SAAS,EAAC,wBAAwB;gBAAAQ,QAAA,gBACrCjI,OAAA;kBACEyH,SAAS,EAAC,iBAAiB;kBAC3ByB,GAAG,EAAEL,YAAY,KAAKxI,QAAQ,CAACgE,MAAM,GAAG,CAAC,GAAGvD,iBAAiB,GAAG,IAAK;kBAAAmH,QAAA,gBAErEjI,OAAA;oBAAAiI,QAAA,GAAI,yBAAuB,EAACW,OAAO,CAACjH,OAAO,CAACc,aAAa;kBAAA;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/D3I,OAAA;oBAAKyH,SAAS,EAAC,kBAAkB;oBAAAQ,QAAA,gBAC/BjI,OAAA;sBAAMyH,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,GAAEW,OAAO,CAACjH,OAAO,CAACyC,aAAa,EAAC,iBAAe;oBAAA;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtF3I,OAAA;sBAAMyH,SAAS,EAAC,eAAe;sBAAAQ,QAAA,EAAC;oBAAyC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELC,OAAO,CAACjH,OAAO,CAACe,QAAQ,CAACuB,GAAG,CAAC,CAACkF,OAAO,EAAEC,YAAY;kBAAA,IAAAC,oBAAA,EAAAC,gBAAA,EAAAC,iBAAA;kBAAA,oBAClDvJ,OAAA;oBAA6CyH,SAAS,EAAC,mBAAmB;oBAAAQ,QAAA,gBACxEjI,OAAA;sBAAKyH,SAAS,EAAC,yBAAyB;sBAAAQ,QAAA,eACtCjI,OAAA;wBAAKyH,SAAS,EAAC,sBAAsB;wBAAAQ,QAAA,gBACnCjI,OAAA;0BAAMyH,SAAS,EAAC,iBAAiB;0BAAAQ,QAAA,GAAC,GAAC,EAACmB,YAAY,GAAG,CAAC;wBAAA;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC5D3I,OAAA;0BAAMyH,SAAS,EAAC,gBAAgB;0BAAAQ,QAAA,EAAEkB,OAAO,CAAClG;wBAAa;0BAAAuF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN3I,OAAA;sBAAKyH,SAAS,EAAC,gCAAgC;sBAAAQ,QAAA,gBAC7CjI,OAAA;wBAAKyH,SAAS,EAAC,eAAe;wBAAAQ,QAAA,eAC5BjI,OAAA;0BAAKyH,SAAS,EAAC,sBAAsB;0BAAAQ,QAAA,gBACnCjI,OAAA;4BAAAiI,QAAA,EAAKkB,OAAO,CAACjF,UAAU,IAAI;0BAAyB;4BAAAsE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1D3I,OAAA;4BAAKyH,SAAS,EAAC,gBAAgB;4BAAAQ,QAAA,eAC7BjI,OAAA;8BACEyH,SAAS,EAAC,oBAAoB;8BAC9BU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAACiC,OAAO,CAAE;8BACpCxE,KAAK,EAAC,cAAc;8BAAAsD,QAAA,eAEpBjI,OAAA,CAACT,IAAI;gCAACgJ,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACZ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAGL,CAACQ,OAAO,CAAC7E,OAAO,IAAI6E,OAAO,CAAC5C,gBAAgB,kBAC3CvG,OAAA;wBAAKyH,SAAS,EAAC,0BAA0B;wBAAAQ,QAAA,GACtCkB,OAAO,CAAC7E,OAAO,iBACdtE,OAAA;0BAAKyH,SAAS,EAAC,mBAAmB;0BAAAQ,QAAA,gBAChCjI,OAAA;4BAAKyH,SAAS,EAAC,cAAc;4BAAAQ,QAAA,eAC3BjI,OAAA,CAACZ,aAAa;8BAACmJ,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACN3I,OAAA;4BAAKyH,SAAS,EAAC,iBAAiB;4BAAAQ,QAAA,gBAC9BjI,OAAA;8BAAAiI,QAAA,EAAI;4BAAkB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC3B3I,OAAA;8BAAAiI,QAAA,EAAIkB,OAAO,CAAC7E;4BAAO;8BAAAkE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,EACvBQ,OAAO,CAAC5E,MAAM,IAAI4E,OAAO,CAAC3E,OAAO,iBAChCxE,OAAA;8BAAKyH,SAAS,EAAC,kBAAkB;8BAAAQ,QAAA,gBAC/BjI,OAAA;gCAAMyH,SAAS,EAAC,aAAa;gCAAAQ,QAAA,EAAEkB,OAAO,CAAC5E;8BAAM;gCAAAiE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eACrD3I,OAAA;gCAAMyH,SAAS,EAAC,cAAc;gCAAAQ,QAAA,EAAEkB,OAAO,CAAC3E;8BAAO;gCAAAgE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAEAQ,OAAO,CAAC1E,WAAW,iBAClBzE,OAAA;0BAAKyH,SAAS,EAAC,uBAAuB;0BAAAQ,QAAA,gBACpCjI,OAAA;4BAAKyH,SAAS,EAAC,kBAAkB;4BAAAQ,QAAA,eAC/BjI,OAAA,CAACL,MAAM;8BAAC4I,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACN3I,OAAA;4BAAKyH,SAAS,EAAC,qBAAqB;4BAAAQ,QAAA,gBAClCjI,OAAA;8BAAAiI,QAAA,EAAI;4BAAkB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC3B3I,OAAA;8BAAAiI,QAAA,EAAIkB,OAAO,CAAC1E;4BAAW;8BAAA+D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAEAQ,OAAO,CAAC5C,gBAAgB,IAAI,CAAC4C,OAAO,CAAC7E,OAAO,iBAC3CtE,OAAA;0BAAKyH,SAAS,EAAC,mBAAmB;0BAAAQ,QAAA,gBAChCjI,OAAA;4BAAAiI,QAAA,EAAI;0BAAiB;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1B3I,OAAA;4BAAAiI,QAAA,EAAIkB,OAAO,CAAC5C;0BAAgB;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CACN,eAGD3I,OAAA;wBAAKyH,SAAS,EAAC,cAAc;wBAAAQ,QAAA,gBAC3BjI,OAAA;0BAAAiI,QAAA,EAAI;wBAAiB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1B3I,OAAA;0BAAKyH,SAAS,EAAC,eAAe;0BAAAQ,QAAA,EAC3B,CAACkB,OAAO,CAACzE,WAAW,MAAA2E,oBAAA,GAAIF,OAAO,CAAC3C,WAAW,cAAA6C,oBAAA,uBAAnBA,oBAAA,CAAqBG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,EAAEvF,GAAG,CAAC,CAACwF,OAAO,EAAEC,KAAK;4BAAA,IAAAC,eAAA,EAAAC,gBAAA;4BAAA,oBAClF5J,OAAA,CAACnB,MAAM,CAAC2I,GAAG;8BAETC,SAAS,EAAC,cAAc;8BACxBC,OAAO,EAAE;gCAAEC,OAAO,EAAE,CAAC;gCAAEmB,CAAC,EAAE;8BAAG,CAAE;8BAC/BjB,OAAO,EAAE;gCAAEF,OAAO,EAAE,CAAC;gCAAEmB,CAAC,EAAE;8BAAE,CAAE;8BAC9Bf,UAAU,EAAE;gCAAE8B,KAAK,EAAEH,KAAK,GAAG;8BAAI,CAAE;8BAAAzB,QAAA,gBAEnCjI,OAAA;gCAAKyH,SAAS,EAAC,gBAAgB;gCAAAQ,QAAA,gBAC7BjI,OAAA;kCAAKyH,SAAS,EAAE,qBAAAkC,eAAA,GAAoBF,OAAO,CAAClF,MAAM,cAAAoF,eAAA,uBAAdA,eAAA,CAAgBtH,WAAW,CAAC,CAAC,EAAG;kCAAA4F,QAAA,GACjEwB,OAAO,CAAClF,MAAM,KAAK,UAAU,iBAAIvE,OAAA,CAACZ,aAAa;oCAACmJ,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EAC5Dc,OAAO,CAAClF,MAAM,KAAK,MAAM,iBAAIvE,OAAA,CAACb,UAAU;oCAACoJ,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EACrDc,OAAO,CAAClF,MAAM,KAAK,QAAQ,iBAAIvE,OAAA,CAACX,IAAI;oCAACkJ,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC/C,CAAC,eACN3I,OAAA;kCAAMyH,SAAS,EAAE,iBAAAmC,gBAAA,GAAgBH,OAAO,CAAClF,MAAM,cAAAqF,gBAAA,uBAAdA,gBAAA,CAAgBvH,WAAW,CAAC,CAAC,EAAG;kCAAA4F,QAAA,EAC9DwB,OAAO,CAAClF;gCAAM;kCAAAiE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACX,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC,eACN3I,OAAA;gCAAAiI,QAAA,EAAKwB,OAAO,CAAC9E;8BAAK;gCAAA6D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eACxB3I,OAAA;gCAAAiI,QAAA,EAAIwB,OAAO,CAAC7E;8BAAW;gCAAA4D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,EAC3Bc,OAAO,CAAC5E,MAAM,iBACb7E,OAAA;gCAAKyH,SAAS,EAAC,cAAc;gCAAAQ,QAAA,gBAC3BjI,OAAA;kCAAAiI,QAAA,EAAQ;gCAAO;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAACc,OAAO,CAAC5E,MAAM;8BAAA;gCAAA2D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CACN;4BAAA,GAtBIe,KAAK;8BAAAlB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAuBA,CAAC;0BAAA,CACd;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAKN3I,OAAA;wBAAKyH,SAAS,EAAC,mBAAmB;wBAAAQ,QAAA,GAE/BkB,OAAO,CAACrE,gBAAgB,iBACvB9E,OAAA;0BAAKyH,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,gBAClCjI,OAAA;4BACEyH,SAAS,EAAC,gBAAgB;4BAC1BU,OAAO,EAAEA,CAAA,KAAMpH,aAAa,CAAC,GAAG6H,OAAO,CAACnH,EAAE,IAAI2H,YAAY,EAAE,EAAE,mBAAmB,CAAE;4BAAAnB,QAAA,gBAEnFjI,OAAA;8BAAAiI,QAAA,EAAM;4BAAiB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EAC7BhI,gBAAgB,CAAC,GAAGiI,OAAO,CAACnH,EAAE,IAAI2H,YAAY,oBAAoB,CAAC,gBAClEpJ,OAAA,CAACN,SAAS;8BAAC6I,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAG3I,OAAA,CAACP,WAAW;8BAAC8I,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAE/C,CAAC,EAERhI,gBAAgB,CAAC,GAAGiI,OAAO,CAACnH,EAAE,IAAI2H,YAAY,oBAAoB,CAAC,iBAClEpJ,OAAA,CAACnB,MAAM,CAAC2I,GAAG;4BACTC,SAAS,EAAC,iBAAiB;4BAC3BC,OAAO,EAAE;8BAAEC,OAAO,EAAE,CAAC;8BAAEmC,MAAM,EAAE;4BAAE,CAAE;4BACnCjC,OAAO,EAAE;8BAAEF,OAAO,EAAE,CAAC;8BAAEmC,MAAM,EAAE;4BAAO,CAAE;4BACxChC,IAAI,EAAE;8BAAEH,OAAO,EAAE,CAAC;8BAAEmC,MAAM,EAAE;4BAAE,CAAE;4BAAA7B,QAAA,eAEhCjI,OAAA;8BAAKyH,SAAS,EAAC,mBAAmB;8BAAAQ,QAAA,EAC/B,CAACkB,OAAO,CAACrE,gBAAgB,IAAI,EAAE,EAAEb,GAAG,CAAC,CAACwF,OAAO,EAAEC,KAAK;gCAAA,IAAAK,gBAAA;gCAAA,oBAC3D/J,OAAA,CAACnB,MAAM,CAAC2I,GAAG;kCAETC,SAAS,EAAE,gBAAgBgC,OAAO,CAACvE,iBAAiB,GAAG,wBAAwB,GAAG,EAAE,EAAG;kCACvF,aAAWuE,OAAO,CAAC/H,IAAK;kCACxBgG,OAAO,EAAE;oCAAEC,OAAO,EAAE,CAAC;oCAAEC,CAAC,EAAE,CAAC;kCAAG,CAAE;kCAChCC,OAAO,EAAE;oCAAEF,OAAO,EAAE,CAAC;oCAAEC,CAAC,EAAE;kCAAE,CAAE;kCAC9BG,UAAU,EAAE;oCAAE8B,KAAK,EAAEH,KAAK,GAAG;kCAAI,CAAE;kCAAAzB,QAAA,gBAEnCjI,OAAA;oCAAKyH,SAAS,EAAC,gBAAgB;oCAAAQ,QAAA,gBAC7BjI,OAAA;sCACEyH,SAAS,EAAC,cAAc;sCACxBuC,KAAK,EAAE;wCACLC,UAAU,EAAER,OAAO,CAAC/H,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACpF+H,OAAO,CAAC/H,IAAI,KAAK,WAAW,GAAG,mDAAmD,GAClF+H,OAAO,CAAC/H,IAAI,KAAK,aAAa,GAAG,mDAAmD,GACpF+H,OAAO,CAAC/H,IAAI,KAAK,QAAQ,GAAG,mDAAmD,GAC/E+H,OAAO,CAAC/H,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACnF+H,OAAO,CAAC/H,IAAI,KAAK,eAAe,GAAG,mDAAmD,GACtF+H,OAAO,CAAC/H,IAAI,KAAK,QAAQ,GAAG,mDAAmD,GAC/E+H,OAAO,CAAC/H,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACnF;sCACb,CAAE;sCAAAuG,QAAA,GAEDwB,OAAO,CAAC/H,IAAI,KAAK,YAAY,iBAAI1B,OAAA,CAACZ,aAAa;wCAACmJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EAC5Dc,OAAO,CAAC/H,IAAI,KAAK,WAAW,iBAAI1B,OAAA,CAACb,UAAU;wCAACoJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACxDc,OAAO,CAAC/H,IAAI,KAAK,aAAa,iBAAI1B,OAAA,CAACF,KAAK;wCAACyI,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACrDc,OAAO,CAAC/H,IAAI,KAAK,QAAQ,iBAAI1B,OAAA,CAACb,UAAU;wCAACoJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACrDc,OAAO,CAAC/H,IAAI,KAAK,YAAY,iBAAI1B,OAAA,CAACX,IAAI;wCAACkJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EACnDc,OAAO,CAAC/H,IAAI,KAAK,eAAe,iBAAI1B,OAAA,CAACZ,aAAa;wCAACmJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EAC/Dc,OAAO,CAAC/H,IAAI,KAAK,QAAQ,iBAAI1B,OAAA,CAACF,KAAK;wCAACyI,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC,EAChDc,OAAO,CAAC/H,IAAI,KAAK,YAAY,iBAAI1B,OAAA,CAACX,IAAI;wCAACkJ,IAAI,EAAE;sCAAG;wCAAAC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAE,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACjD,CAAC,eACN3I,OAAA;sCAAKyH,SAAS,EAAC,eAAe;sCAAAQ,QAAA,EAAEwB,OAAO,CAAC9E;oCAAK;sCAAA6D,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAM,CAAC,eACpD3I,OAAA;sCAAKyH,SAAS,EAAC,gBAAgB;sCAAAQ,QAAA,gBAC7BjI,OAAA;wCAAKyH,SAAS,EAAE,iBAAAsC,gBAAA,GAAgBN,OAAO,CAAClF,MAAM,cAAAwF,gBAAA,uBAAdA,gBAAA,CAAgB1H,WAAW,CAAC,CAAC,EAAG;wCAAA4F,QAAA,GAC7DwB,OAAO,CAAClF,MAAM,EAAC,SAClB;sCAAA;wCAAAiE,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAK,CAAC,EACLc,OAAO,CAACjF,OAAO,iBACdxE,OAAA;wCAAKyH,SAAS,EAAE,iBAAiBgC,OAAO,CAACjF,OAAO,CAACnC,WAAW,CAAC,CAAC,EAAG;wCAAA4F,QAAA,EAC9DwB,OAAO,CAACjF;sCAAO;wCAAAgE,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACb,CACN;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACE,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CAAC,eACN3I,OAAA;oCAAGyH,SAAS,EAAC,qBAAqB;oCAAAQ,QAAA,EAAEwB,OAAO,CAAC7E;kCAAW;oCAAA4D,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAI,CAAC,EAC3Dc,OAAO,CAAC1E,OAAO,iBACd/E,OAAA;oCAAKyH,SAAS,EAAC,iBAAiB;oCAAAQ,QAAA,eAC9BjI,OAAA;sCAAAiI,QAAA,EAAIwB,OAAO,CAAC1E;oCAAO;sCAAAyD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAI;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrB,CACN,EACAc,OAAO,CAACxE,QAAQ,iBACfjF,OAAA;oCAAKyH,SAAS,EAAC,kBAAkB;oCAAAQ,QAAA,gBAC/BjI,OAAA;sCAAAiI,QAAA,EAAQ;oCAAS;sCAAAO,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ,CAAC,KAAC,EAACc,OAAO,CAACxE,QAAQ;kCAAA;oCAAAuD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACzC,CACN,EACAc,OAAO,CAACzE,UAAU,iBACjBhF,OAAA;oCAAKyH,SAAS,EAAC,gBAAgB;oCAAAQ,QAAA,gBAC7BjI,OAAA;sCAAKyH,SAAS,EAAC,kBAAkB;sCAAAQ,QAAA,GAAC,cAAY,EAACwB,OAAO,CAACzE,UAAU,EAAC,GAAC;oCAAA;sCAAAwD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAK,CAAC,eACzE3I,OAAA;sCAAKyH,SAAS,EAAC,qBAAqB;sCAAAQ,QAAA,eAClCjI,OAAA,CAACnB,MAAM,CAAC2I,GAAG;wCACTC,SAAS,EAAC,iBAAiB;wCAC3BC,OAAO,EAAE;0CAAEwC,KAAK,EAAE;wCAAE,CAAE;wCACtBrC,OAAO,EAAE;0CAAEqC,KAAK,EAAE,GAAGT,OAAO,CAACzE,UAAU;wCAAI,CAAE;wCAC7C+C,UAAU,EAAE;0CAAEC,QAAQ,EAAE,CAAC;0CAAE6B,KAAK,EAAE;wCAAI;sCAAE;wCAAArB,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACzC;oCAAC;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACC,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CACN;gCAAA,GAlEIe,KAAK;kCAAAlB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAmEA,CAAC;8BAAA,CACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACI,CACb;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN,eAGO3I,OAAA;0BAAKyH,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,gBAClCjI,OAAA;4BACEyH,SAAS,EAAC,gBAAgB;4BAC1BU,OAAO,EAAEA,CAAA,KAAMpH,aAAa,CAAC,GAAG6H,OAAO,CAACnH,EAAE,IAAI2H,YAAY,EAAE,EAAE,SAAS,CAAE;4BAAAnB,QAAA,gBAEzEjI,OAAA;8BAAAiI,QAAA,GAAM,wBAAsB,EAAC,EAAAqB,gBAAA,GAAAH,OAAO,CAAC/C,OAAO,cAAAkD,gBAAA,uBAAfA,gBAAA,CAAiBjF,MAAM,KAAI,CAAC,EAAC,GAAC;4BAAA;8BAAAmE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EACjEhI,gBAAgB,CAAC,GAAGiI,OAAO,CAACnH,EAAE,IAAI2H,YAAY,UAAU,CAAC,gBACxDpJ,OAAA,CAACN,SAAS;8BAAC6I,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAG3I,OAAA,CAACP,WAAW;8BAAC8I,IAAI,EAAE;4BAAG;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAE/C,CAAC,EAERhI,gBAAgB,CAAC,GAAGiI,OAAO,CAACnH,EAAE,IAAI2H,YAAY,UAAU,CAAC,iBACxDpJ,OAAA,CAACnB,MAAM,CAAC2I,GAAG;4BACTC,SAAS,EAAC,iBAAiB;4BAC3BC,OAAO,EAAE;8BAAEC,OAAO,EAAE,CAAC;8BAAEmC,MAAM,EAAE;4BAAE,CAAE;4BACnCjC,OAAO,EAAE;8BAAEF,OAAO,EAAE,CAAC;8BAAEmC,MAAM,EAAE;4BAAO,CAAE;4BACxChC,IAAI,EAAE;8BAAEH,OAAO,EAAE,CAAC;8BAAEmC,MAAM,EAAE;4BAAE,CAAE;4BAAA7B,QAAA,eAEhCjI,OAAA;8BAAKyH,SAAS,EAAC,iBAAiB;8BAAAQ,QAAA,eAC9BjI,OAAA;gCAAKyH,SAAS,EAAC,cAAc;gCAAAQ,QAAA,GAAAsB,iBAAA,GAC1BJ,OAAO,CAAC/C,OAAO,cAAAmD,iBAAA,uBAAfA,iBAAA,CAAiBtF,GAAG,CAAC,CAACkG,MAAM,EAAET,KAAK,kBAClC1J,OAAA;kCAAiByH,SAAS,EAAC,aAAa;kCAAAQ,QAAA,gBACtCjI,OAAA;oCAAKyH,SAAS,EAAC,eAAe;oCAAAQ,QAAA,gBAC5BjI,OAAA,CAACd,YAAY;sCAACqJ,IAAI,EAAE;oCAAG;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC,eAC1B3I,OAAA;sCAAMyH,SAAS,EAAC,cAAc;sCAAAQ,QAAA,EAAEkC,MAAM,CAACxF;oCAAK;sCAAA6D,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,EACnDwB,MAAM,CAACnF,UAAU,iBAChBhF,OAAA;sCAAMyH,SAAS,EAAE,qBAAqB0C,MAAM,CAACnF,UAAU,CAAC3C,WAAW,CAAC,CAAC,EAAG;sCAAA4F,QAAA,EACrEkC,MAAM,CAACnF;oCAAU;sCAAAwD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACd,CACP;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACE,CAAC,eACN3I,OAAA;oCAAKyH,SAAS,EAAC,aAAa;oCAAAQ,QAAA,gBAC1BjI,OAAA;sCAAMyH,SAAS,EAAC,YAAY;sCAAAQ,QAAA,EAAEkC,MAAM,CAAC9D;oCAAG;sCAAAmC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,eAChD3I,OAAA;sCAAMyH,SAAS,EAAC,aAAa;sCAAAQ,QAAA,EAAEkC,MAAM,CAAC7D;oCAAI;sCAAAkC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,eAClD3I,OAAA;sCAAMyH,SAAS,EAAC,aAAa;sCAAAQ,QAAA,EAAEkC,MAAM,CAACzI;oCAAI;sCAAA8G,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC/C,CAAC;gCAAA,GAdEe,KAAK;kCAAAlB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAeV,CACN;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACI,CACb;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAvPEQ,OAAO,CAAC/F,SAAS,IAAIgG,YAAY;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwPtC,CAAC;gBAAA,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN3I,OAAA;gBAAKyH,SAAS,EAAC,gCAAgC;gBAAAQ,QAAA,eAC7CjI,OAAA;kBAAKyH,SAAS,EAAC,eAAe;kBAAAQ,QAAA,eAC5BjI,OAAA;oBAAKyH,SAAS,EAAC,sBAAsB;oBAAAQ,QAAA,gBACnCjI,OAAA;sBAAAiI,QAAA,EAAKW,OAAO,CAACjH,OAAO,CAACuC,UAAU,IAAI;oBAAyB;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClE3I,OAAA;sBAAKyH,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,eAC7BjI,OAAA;wBACEyH,SAAS,EAAC,oBAAoB;wBAC9BU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC0B,OAAO,CAACjH,OAAO,CAAE;wBAC5CgD,KAAK,EAAC,aAAa;wBAAAsD,QAAA,eAEnBjI,OAAA,CAACT,IAAI;0BAACgJ,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CACN,gBAED3I,OAAA;gBAAKyH,SAAS,EAAC,iBAAiB;gBAAAQ,QAAA,EAAEW,OAAO,CAACjH;cAAO;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACxD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN3I,OAAA;cAAKyH,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BW,OAAO,CAAChH,SAAS,CAACmH,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN,GAtTIC,OAAO,CAACnH,EAAE;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuTL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEjBlI,SAAS,iBACRT,OAAA,CAACnB,MAAM,CAAC2I,GAAG;QACTC,SAAS,EAAC,iBAAiB;QAC3BC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAG,CAAE;QAC/BjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAAAb,QAAA,eAE9BjI,OAAA;UAAKyH,SAAS,EAAC,iBAAiB;UAAAQ,QAAA,gBAC9BjI,OAAA,CAACV,OAAO;YAACmI,SAAS,EAAC,iBAAiB;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD3I,OAAA;YAAAiI,QAAA,EAAM;UAAqC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAED3I,OAAA;QAAKkJ,GAAG,EAAErI;MAAe;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGN3I,OAAA;MAAKyH,SAAS,EAAC,yBAAyB;MAAAQ,QAAA,eACtCjI,OAAA;QAAKyH,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChCjI,OAAA;UACEoK,KAAK,EAAE7J,UAAW;UAClB8J,QAAQ,EAAGxD,CAAC,IAAKrG,aAAa,CAACqG,CAAC,CAACyD,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAE3D,cAAe;UAC3B4D,WAAW,EAAC,oDAAoD;UAChE/C,SAAS,EAAC,eAAe;UACzBgD,IAAI,EAAC;QAAG;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF3I,OAAA,CAACnB,MAAM,CAACqJ,MAAM;UACZT,SAAS,EAAC,UAAU;UACpBU,OAAO,EAAEzB,iBAAkB;UAC3BgE,QAAQ,EAAE,CAACnK,UAAU,CAACoG,IAAI,CAAC,CAAC,IAAIlG,SAAU;UAC1C2H,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,eAE1BjI,OAAA,CAAChB,IAAI;YAACuJ,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACvI,EAAA,CAphCIH,gBAAgB;AAAA0K,EAAA,GAAhB1K,gBAAgB;AAshCtB,eAAeA,gBAAgB;AAAC,IAAA0K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}