{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowLeft, Send, Download, ExternalLink, TrendingUp, AlertTriangle, Info, Loader2, Copy, Share2, ChevronDown, ChevronUp, Target, DollarSign, Clock, Users } from 'lucide-react';\nimport './ConversationView.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationView = ({\n  initialQuery,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n  const generateMockResponse = query => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = query => {\n      const lowerQuery = query.toLowerCase();\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n    return getReportByQuery(query);\n  };\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [{\n        type: \"regulatory\",\n        title: \"Mandatory Recycled Content Requirements\",\n        impact: \"Critical\",\n        description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n        action: \"Secure recycling partnerships immediately\"\n      }, {\n        type: \"financial\",\n        title: \"Investment Requirements\",\n        impact: \"High\",\n        description: \"$800M-1.2B needed for compliance infrastructure\",\n        action: \"Establish dedicated compliance budget\"\n      }, {\n        type: \"competitive\",\n        title: \"SABIC Competitive Threat\",\n        impact: \"High\",\n        description: \"Risk losing 15-20% EU market share to competitors\",\n        action: \"Accelerate sustainable product development\"\n      }],\n      detailedFindings: [{\n        type: \"regulatory\",\n        title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n        details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n        confidence: 98,\n        timeline: \"Implementation: Jan 2025, Full compliance: 2030\"\n      }, {\n        type: \"financial\",\n        title: \"Compliance Investment Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n        details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2027\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Landscape Shift\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n        details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n        confidence: 92,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [{\n        priority: \"Critical\",\n        action: \"Form EU Compliance Task Force\",\n        timeline: \"Next 30 days\",\n        investment: \"$5M\",\n        description: \"Immediate action team to coordinate regulatory response\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Recycling Partnerships\",\n        timeline: \"6 months\",\n        investment: \"$200-300M\",\n        description: \"Lock in technology partnerships before competitors\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line\",\n        timeline: \"12 months\",\n        investment: \"$150M\",\n        description: \"Develop premium recycled content products\"\n      }],\n      allRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish EU Regulatory Compliance Task Force\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$5M\",\n        description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Chemical Recycling Technology Partnerships\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$200-300M\",\n        description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line Development\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$150M\",\n        description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n      }, {\n        priority: \"Medium\",\n        action: \"Establish European Recycling Hub\",\n        timeline: \"24 months (Q4 2025)\",\n        investment: \"$400-500M\",\n        description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n      }],\n      competitiveBenchmarking: [{\n        company: \"SABIC\",\n        strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n        advantage: \"First-mover in chemical recycling, strong EU presence\",\n        weakness: \"Higher cost base, limited feedstock security\"\n      }, {\n        company: \"Dow\",\n        strategy: \"Advanced recycling partnerships, circular design principles\",\n        advantage: \"Technology leadership, established partnerships\",\n        weakness: \"Focus on specialty applications, limited commodity exposure\"\n      }, {\n        company: \"LyondellBasell\",\n        strategy: \"Molecular recycling technology, circular economy solutions\",\n        advantage: \"Integrated technology development, scale advantages\",\n        weakness: \"Limited EU manufacturing footprint\"\n      }],\n      sources: [{\n        title: \"EU Packaging & Packaging Waste Regulation\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2024-01-15\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Environment Agency Circular Economy Report\",\n        url: \"eea.europa.eu\",\n        date: \"2024-02-01\",\n        type: \"research\",\n        confidence: \"High\"\n      }, {\n        title: \"SABIC Circular Economy Strategy Update\",\n        url: \"sabic.com\",\n        date: \"2024-01-30\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"Plastics Europe Market Data 2024\",\n        url: \"plasticseurope.org\",\n        date: \"2024-02-15\",\n        type: \"industry\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey Circular Economy in Chemicals\",\n        url: \"mckinsey.com\",\n        date: \"2024-01-20\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }, {\n        title: \"Wood Mackenzie Petrochemicals Outlook\",\n        url: \"woodmac.com\",\n        date: \"2024-02-10\",\n        type: \"market\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [{\n        type: \"financial\",\n        title: \"Direct CBAM Cost Impact\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n        details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n        confidence: 92,\n        timeline: \"Implementation: January 2026\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Advantage Opportunity\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n        details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n        confidence: 88,\n        timeline: \"Advantage period: 2026-2035\"\n      }, {\n        type: \"technology\",\n        title: \"Carbon Reduction Investment Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n        details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2030\"\n      }, {\n        type: \"regulatory\",\n        title: \"CBAM Reporting and Verification Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n        details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n        confidence: 95,\n        timeline: \"Reporting starts: October 2023\"\n      }, {\n        type: \"market\",\n        title: \"Premium Low-Carbon Product Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n        details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n        confidence: 78,\n        timeline: \"Market development: 2025-2030\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Implement Comprehensive Carbon Accounting System\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$10M\",\n        description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n      }, {\n        priority: \"High\",\n        action: \"Accelerate Renewable Energy Integration\",\n        timeline: \"18 months (Q3 2025)\",\n        investment: \"$200M\",\n        description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n      }, {\n        priority: \"High\",\n        action: \"Develop Low-Carbon Product Certification\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$5M\",\n        description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n      }],\n      sources: [{\n        title: \"EU CBAM Regulation 2023/956\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2023-05-17\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Commission CBAM Implementation Guide\",\n        url: \"taxation-customs.ec.europa.eu\",\n        date: \"2024-01-10\",\n        type: \"guidance\",\n        confidence: \"Official\"\n      }, {\n        title: \"IEA Petrochemicals Carbon Intensity Database\",\n        url: \"iea.org\",\n        date: \"2024-02-05\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [{\n        type: \"environmental\",\n        title: \"Decarbonization Pathway Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n        details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n        confidence: 90,\n        timeline: \"Transformation period: 2024-2050\"\n      }, {\n        type: \"social\",\n        title: \"UAE Emiratization and Skills Development\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n        details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n        confidence: 85,\n        timeline: \"Achievement target: 2030\"\n      }, {\n        type: \"governance\",\n        title: \"ESG Reporting and Transparency Enhancement\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n        details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n        confidence: 88,\n        timeline: \"Full compliance: 2025\"\n      }, {\n        type: \"financial\",\n        title: \"Sustainable Finance and Green Bonds Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n        details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n        confidence: 82,\n        timeline: \"Financing window: 2024-2027\"\n      }, {\n        type: \"technology\",\n        title: \"Digital ESG Management Platform\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n        details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n        confidence: 75,\n        timeline: \"Implementation: 2024-2025\"\n      }],\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish Chief Sustainability Officer Role\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$2M\",\n        description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Comprehensive Decarbonization Program\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$500M\",\n        description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n      }, {\n        priority: \"High\",\n        action: \"Implement Advanced Emiratization Strategy\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$100M\",\n        description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n      }],\n      sources: [{\n        title: \"UAE Vision 2071 Strategic Framework\",\n        url: \"government.ae\",\n        date: \"2023-12-01\",\n        type: \"policy\",\n        confidence: \"Official\"\n      }, {\n        title: \"ADNOC Sustainability Strategy 2030\",\n        url: \"adnoc.ae\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey ESG in Chemicals Industry\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }]\n    };\n  };\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Recycled Polyethylene Market Growth\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n        details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n        confidence: 88,\n        timeline: \"Market expansion: 2024-2030\"\n      }],\n      sources: [{\n        title: \"Ellen MacArthur Foundation Circular Economy Report\",\n        url: \"ellenmacarthurfoundation.org\",\n        date: \"2024-01-10\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [{\n        type: \"competitive\",\n        title: \"SABIC Circular Economy Leadership\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n        details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n        confidence: 95,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      sources: [{\n        title: \"SABIC Sustainability Strategy 2030\",\n        url: \"sabic.com\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Sustainable Packaging Demand Surge\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n        details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n        confidence: 90,\n        timeline: \"Market shift: 2024-2027\"\n      }],\n      sources: [{\n        title: \"McKinsey Sustainable Packaging Report\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n  const copyMessage = content => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"conversation-view\",\n    initial: {\n      opacity: 0,\n      x: 20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    exit: {\n      opacity: 0,\n      x: -20\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversation-header\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"back-btn\",\n        onClick: onBack,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this), \"Back to Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-actions\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          onClick: exportToPDF,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Share2, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), \"Share\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `message ${message.type}`,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-response\",\n              children: typeof message.content === 'object' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"intelligence-report\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"report-title-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: message.content.reportType || 'ESG Intelligence Report'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"report-meta\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"report-timestamp\",\n                        children: [\"Generated: \", new Date().toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 611,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"report-confidence\",\n                        children: \"High Confidence Analysis\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 612,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"copy-btn\",\n                    onClick: () => copyMessage(message.content),\n                    children: /*#__PURE__*/_jsxDEV(Copy, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 619,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 25\n                }, this), message.content.executiveSummary && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"executive-summary\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Executive Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: message.content.executiveSummary\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 27\n                }, this), message.content.marketImpact && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"market-impact-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Market Impact Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"impact-metrics\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Revenue at Risk\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value risk\",\n                        children: message.content.marketImpact.revenueAtRisk\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Investment Required\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value investment\",\n                        children: message.content.marketImpact.investmentRequired\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Timeline\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value timeline\",\n                        children: message.content.marketImpact.timelineForCompliance\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 644,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Market Opportunity\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 647,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value opportunity\",\n                        children: message.content.marketImpact.marketOpportunity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 648,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"key-findings\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Key Findings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 27\n                  }, this), message.content.keyFindings.map((finding, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"finding-card\",\n                    initial: {\n                      opacity: 0,\n                      x: -20\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: index * 0.1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"finding-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-icon\",\n                        children: [finding.type === 'regulatory' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 666,\n                          columnNumber: 69\n                        }, this), finding.type === 'financial' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 667,\n                          columnNumber: 68\n                        }, this), finding.type === 'competitive' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 70\n                        }, this), finding.type === 'market' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 669,\n                          columnNumber: 65\n                        }, this), finding.type === 'technology' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 670,\n                          columnNumber: 69\n                        }, this), finding.type === 'environmental' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 671,\n                          columnNumber: 72\n                        }, this), finding.type === 'social' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 672,\n                          columnNumber: 65\n                        }, this), finding.type === 'governance' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 673,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-title\",\n                        children: finding.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-badges\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `impact-badge ${finding.impact.toLowerCase()}`,\n                          children: [finding.impact, \" Impact\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 677,\n                          columnNumber: 35\n                        }, this), finding.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `urgency-badge ${finding.urgency.toLowerCase()}`,\n                          children: finding.urgency\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 681,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"finding-description\",\n                      children: finding.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 31\n                    }, this), finding.details && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"finding-details\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: finding.details\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 690,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 33\n                    }, this), finding.timeline && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"finding-timeline\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Timeline:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 35\n                      }, this), \" \", finding.timeline]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 694,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"confidence-bar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"confidence-label\",\n                        children: [\"Confidence: \", finding.confidence, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 699,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"confidence-progress\",\n                        children: /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"confidence-fill\",\n                          initial: {\n                            width: 0\n                          },\n                          animate: {\n                            width: `${finding.confidence}%`\n                          },\n                          transition: {\n                            duration: 1,\n                            delay: 0.5\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 701,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 700,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 29\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 25\n                }, this), message.content.strategicRecommendations && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"strategic-recommendations\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Strategic Recommendations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 29\n                  }, this), message.content.strategicRecommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"recommendation-card\",\n                    initial: {\n                      opacity: 0,\n                      y: 10\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.3 + index * 0.1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"recommendation-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `priority-badge ${rec.priority.toLowerCase()}`,\n                        children: [rec.priority, \" Priority\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"recommendation-investment\",\n                        children: rec.investment\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"recommendation-action\",\n                      children: rec.action\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"recommendation-description\",\n                      children: rec.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"recommendation-timeline\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Timeline:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 35\n                      }, this), \" \", rec.timeline]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 33\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 31\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 27\n                }, this), message.content.competitiveBenchmarking && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"competitive-benchmarking\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Competitive Benchmarking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"competitors-grid\",\n                    children: message.content.competitiveBenchmarking.map((comp, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"competitor-card\",\n                      initial: {\n                        opacity: 0,\n                        scale: 0.95\n                      },\n                      animate: {\n                        opacity: 1,\n                        scale: 1\n                      },\n                      transition: {\n                        delay: 0.4 + index * 0.1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"competitor-name\",\n                        children: comp.company\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 752,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"competitor-strategy\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Strategy:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 37\n                        }, this), \" \", comp.strategy]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 753,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"competitor-analysis\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"advantage\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Advantage:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 758,\n                            columnNumber: 39\n                          }, this), \" \", comp.advantage]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 757,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"weakness\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Weakness:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 761,\n                            columnNumber: 39\n                          }, this), \" \", comp.weakness]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 760,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 756,\n                        columnNumber: 35\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 27\n                }, this), message.content.riskAssessment && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"risk-assessment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Risk Assessment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"risk-categories\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"risk-category high-risk\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        children: \"High Risk\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 775,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        children: message.content.riskAssessment.high.map((risk, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: risk\n                        }, index, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 778,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"risk-category medium-risk\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        children: \"Medium Risk\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        children: message.content.riskAssessment.medium.map((risk, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: risk\n                        }, index, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 786,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 784,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"risk-category low-risk\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        children: \"Low Risk\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        children: message.content.riskAssessment.low.map((risk, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: risk\n                        }, index, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 794,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 792,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sources-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Sources & References\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"sources-grid\",\n                    children: message.content.sources.map((source, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"source-card\",\n                      initial: {\n                        opacity: 0,\n                        y: 10\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.3 + index * 0.1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"source-header\",\n                        children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 814,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-title\",\n                          children: source.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 815,\n                          columnNumber: 35\n                        }, this), source.confidence && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `source-confidence ${source.confidence.toLowerCase()}`,\n                          children: source.confidence\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 817,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"source-meta\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-url\",\n                          children: source.url\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 823,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-date\",\n                          children: source.date\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 824,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-type\",\n                          children: source.type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 825,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 822,\n                        columnNumber: 33\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 25\n                }, this), message.content.actionItems && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-items\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Quick Action Items\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: message.content.actionItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.li, {\n                      initial: {\n                        opacity: 0,\n                        x: -10\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0\n                      },\n                      transition: {\n                        delay: 0.5 + index * 0.1\n                      },\n                      children: item\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"simple-response\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-message\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(Loader2, {\n            className: \"loading-spinner\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Analyzing ESG data and regulations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-input-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-input-box\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask a follow-up question about ESG intelligence...\",\n          className: \"message-input\",\n          rows: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"send-btn\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || isLoading,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 880,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 543,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationView, \"Jfa2Q+54F4smiufyXm/+bCymPuA=\");\n_c = ConversationView;\nexport default ConversationView;\nvar _c;\n$RefreshReg$(_c, \"ConversationView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "ArrowLeft", "Send", "Download", "ExternalLink", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Loader2", "Copy", "Share2", "ChevronDown", "ChevronUp", "Target", "DollarSign", "Clock", "Users", "jsxDEV", "_jsxDEV", "ConversationView", "initialQuery", "onBack", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "isLoading", "setIsLoading", "expandedSections", "setExpandedSections", "messagesEndRef", "toggleSection", "messageId", "section", "prev", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "userMessage", "id", "type", "content", "timestamp", "Date", "setTimeout", "aiResponse", "generateMockResponse", "query", "getReportByQuery", "lowerQuery", "toLowerCase", "includes", "generateEURegulationReport", "generateCBAMReport", "generateCircularEconomyReport", "generateCompetitorReport", "generateMarketTrendsReport", "generateComprehensiveESGReport", "reportType", "problem", "impact", "urgency", "opportunity", "topFindings", "title", "description", "action", "detailedFindings", "details", "confidence", "timeline", "marketImpact", "revenueAtRisk", "investmentRequired", "timelineForCompliance", "marketOpportunity", "riskAssessment", "high", "medium", "low", "nextSteps", "priority", "investment", "allRecommendations", "competitiveBenchmarking", "company", "strategy", "advantage", "weakness", "sources", "url", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyFindings", "strategicRecommendations", "handleSendMessage", "trim", "length", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "exportToPDF", "console", "log", "copyMessage", "navigator", "clipboard", "writeText", "JSON", "stringify", "div", "className", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "children", "button", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "message", "y", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "finding", "index", "delay", "width", "rec", "comp", "risk", "source", "actionItems", "item", "li", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ArrowLeft,\n  Send,\n  Download,\n  ExternalLink,\n  TrendingUp,\n  AlertTriangle,\n  Info,\n  Loader2,\n  Copy,\n  Share2,\n  ChevronDown,\n  ChevronUp,\n  Target,\n  DollarSign,\n  Clock,\n  Users\n} from 'lucide-react';\nimport './ConversationView.css';\n\nconst ConversationView = ({ initialQuery, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n\n  const generateMockResponse = (query) => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = (query) => {\n      const lowerQuery = query.toLowerCase();\n\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n\n    return getReportByQuery(query);\n  };\n\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [\n        {\n          type: \"regulatory\",\n          title: \"Mandatory Recycled Content Requirements\",\n          impact: \"Critical\",\n          description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n          action: \"Secure recycling partnerships immediately\"\n        },\n        {\n          type: \"financial\",\n          title: \"Investment Requirements\",\n          impact: \"High\",\n          description: \"$800M-1.2B needed for compliance infrastructure\",\n          action: \"Establish dedicated compliance budget\"\n        },\n        {\n          type: \"competitive\",\n          title: \"SABIC Competitive Threat\",\n          impact: \"High\",\n          description: \"Risk losing 15-20% EU market share to competitors\",\n          action: \"Accelerate sustainable product development\"\n        }\n      ],\n      detailedFindings: [\n        {\n          type: \"regulatory\",\n          title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n          details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n          confidence: 98,\n          timeline: \"Implementation: Jan 2025, Full compliance: 2030\"\n        },\n        {\n          type: \"financial\",\n          title: \"Compliance Investment Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n          details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2027\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Landscape Shift\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n          details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n          confidence: 92,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [\n        {\n          priority: \"Critical\",\n          action: \"Form EU Compliance Task Force\",\n          timeline: \"Next 30 days\",\n          investment: \"$5M\",\n          description: \"Immediate action team to coordinate regulatory response\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Recycling Partnerships\",\n          timeline: \"6 months\",\n          investment: \"$200-300M\",\n          description: \"Lock in technology partnerships before competitors\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line\",\n          timeline: \"12 months\",\n          investment: \"$150M\",\n          description: \"Develop premium recycled content products\"\n        }\n      ],\n      allRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish EU Regulatory Compliance Task Force\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$5M\",\n          description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Chemical Recycling Technology Partnerships\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$200-300M\",\n          description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line Development\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$150M\",\n          description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n        },\n        {\n          priority: \"Medium\",\n          action: \"Establish European Recycling Hub\",\n          timeline: \"24 months (Q4 2025)\",\n          investment: \"$400-500M\",\n          description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n        }\n      ],\n      competitiveBenchmarking: [\n        {\n          company: \"SABIC\",\n          strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n          advantage: \"First-mover in chemical recycling, strong EU presence\",\n          weakness: \"Higher cost base, limited feedstock security\"\n        },\n        {\n          company: \"Dow\",\n          strategy: \"Advanced recycling partnerships, circular design principles\",\n          advantage: \"Technology leadership, established partnerships\",\n          weakness: \"Focus on specialty applications, limited commodity exposure\"\n        },\n        {\n          company: \"LyondellBasell\",\n          strategy: \"Molecular recycling technology, circular economy solutions\",\n          advantage: \"Integrated technology development, scale advantages\",\n          weakness: \"Limited EU manufacturing footprint\"\n        }\n      ],\n      sources: [\n        { title: \"EU Packaging & Packaging Waste Regulation\", url: \"eur-lex.europa.eu\", date: \"2024-01-15\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Environment Agency Circular Economy Report\", url: \"eea.europa.eu\", date: \"2024-02-01\", type: \"research\", confidence: \"High\" },\n        { title: \"SABIC Circular Economy Strategy Update\", url: \"sabic.com\", date: \"2024-01-30\", type: \"corporate\", confidence: \"High\" },\n        { title: \"Plastics Europe Market Data 2024\", url: \"plasticseurope.org\", date: \"2024-02-15\", type: \"industry\", confidence: \"High\" },\n        { title: \"McKinsey Circular Economy in Chemicals\", url: \"mckinsey.com\", date: \"2024-01-20\", type: \"consulting\", confidence: \"Medium\" },\n        { title: \"Wood Mackenzie Petrochemicals Outlook\", url: \"woodmac.com\", date: \"2024-02-10\", type: \"market\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [\n        {\n          type: \"financial\",\n          title: \"Direct CBAM Cost Impact\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n          details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n          confidence: 92,\n          timeline: \"Implementation: January 2026\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Advantage Opportunity\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n          details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n          confidence: 88,\n          timeline: \"Advantage period: 2026-2035\"\n        },\n        {\n          type: \"technology\",\n          title: \"Carbon Reduction Investment Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n          details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2030\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"CBAM Reporting and Verification Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n          details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n          confidence: 95,\n          timeline: \"Reporting starts: October 2023\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Low-Carbon Product Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n          details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n          confidence: 78,\n          timeline: \"Market development: 2025-2030\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Implement Comprehensive Carbon Accounting System\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$10M\",\n          description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Accelerate Renewable Energy Integration\",\n          timeline: \"18 months (Q3 2025)\",\n          investment: \"$200M\",\n          description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Develop Low-Carbon Product Certification\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$5M\",\n          description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n        }\n      ],\n      sources: [\n        { title: \"EU CBAM Regulation 2023/956\", url: \"eur-lex.europa.eu\", date: \"2023-05-17\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Commission CBAM Implementation Guide\", url: \"taxation-customs.ec.europa.eu\", date: \"2024-01-10\", type: \"guidance\", confidence: \"Official\" },\n        { title: \"IEA Petrochemicals Carbon Intensity Database\", url: \"iea.org\", date: \"2024-02-05\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [\n        {\n          type: \"environmental\",\n          title: \"Decarbonization Pathway Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n          details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n          confidence: 90,\n          timeline: \"Transformation period: 2024-2050\"\n        },\n        {\n          type: \"social\",\n          title: \"UAE Emiratization and Skills Development\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n          details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n          confidence: 85,\n          timeline: \"Achievement target: 2030\"\n        },\n        {\n          type: \"governance\",\n          title: \"ESG Reporting and Transparency Enhancement\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n          details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n          confidence: 88,\n          timeline: \"Full compliance: 2025\"\n        },\n        {\n          type: \"financial\",\n          title: \"Sustainable Finance and Green Bonds Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n          details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n          confidence: 82,\n          timeline: \"Financing window: 2024-2027\"\n        },\n        {\n          type: \"technology\",\n          title: \"Digital ESG Management Platform\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n          details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n          confidence: 75,\n          timeline: \"Implementation: 2024-2025\"\n        }\n      ],\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish Chief Sustainability Officer Role\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$2M\",\n          description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Comprehensive Decarbonization Program\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$500M\",\n          description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Implement Advanced Emiratization Strategy\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$100M\",\n          description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n        }\n      ],\n      sources: [\n        { title: \"UAE Vision 2071 Strategic Framework\", url: \"government.ae\", date: \"2023-12-01\", type: \"policy\", confidence: \"Official\" },\n        { title: \"ADNOC Sustainability Strategy 2030\", url: \"adnoc.ae\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" },\n        { title: \"McKinsey ESG in Chemicals Industry\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"Medium\" }\n      ]\n    };\n  };\n\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Recycled Polyethylene Market Growth\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n          details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n          confidence: 88,\n          timeline: \"Market expansion: 2024-2030\"\n        }\n      ],\n      sources: [\n        { title: \"Ellen MacArthur Foundation Circular Economy Report\", url: \"ellenmacarthurfoundation.org\", date: \"2024-01-10\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [\n        {\n          type: \"competitive\",\n          title: \"SABIC Circular Economy Leadership\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n          details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n          confidence: 95,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      sources: [\n        { title: \"SABIC Sustainability Strategy 2030\", url: \"sabic.com\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Sustainable Packaging Demand Surge\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n          details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n          confidence: 90,\n          timeline: \"Market shift: 2024-2027\"\n        }\n      ],\n      sources: [\n        { title: \"McKinsey Sustainable Packaging Report\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n\n  const copyMessage = (content) => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n\n  return (\n    <motion.div\n      className=\"conversation-view\"\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      exit={{ opacity: 0, x: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"conversation-header\">\n        <motion.button\n          className=\"back-btn\"\n          onClick={onBack}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <ArrowLeft size={20} />\n          Back to Search\n        </motion.button>\n\n        <div className=\"conversation-actions\">\n          <motion.button\n            className=\"action-btn\"\n            onClick={exportToPDF}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Download size={18} />\n            Export\n          </motion.button>\n          <motion.button\n            className=\"action-btn\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Share2 size={18} />\n            Share\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"messages-container\">\n        <AnimatePresence>\n          {messages.map((message) => (\n            <motion.div\n              key={message.id}\n              className={`message ${message.type}`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {message.type === 'user' ? (\n                <div className=\"user-message\">\n                  <div className=\"message-content\">{message.content}</div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"ai-message\">\n                  <div className=\"ai-response\">\n                    {typeof message.content === 'object' ? (\n                      <div className=\"intelligence-report\">\n                        <div className=\"report-header\">\n                          <div className=\"report-title-section\">\n                            <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>\n                            <div className=\"report-meta\">\n                              <span className=\"report-timestamp\">Generated: {new Date().toLocaleDateString()}</span>\n                              <span className=\"report-confidence\">High Confidence Analysis</span>\n                            </div>\n                          </div>\n                          <button\n                            className=\"copy-btn\"\n                            onClick={() => copyMessage(message.content)}\n                          >\n                            <Copy size={16} />\n                          </button>\n                        </div>\n\n                        {message.content.executiveSummary && (\n                          <div className=\"executive-summary\">\n                            <h4>Executive Summary</h4>\n                            <p>{message.content.executiveSummary}</p>\n                          </div>\n                        )}\n\n                        {message.content.marketImpact && (\n                          <div className=\"market-impact-section\">\n                            <h4>Market Impact Analysis</h4>\n                            <div className=\"impact-metrics\">\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Revenue at Risk</span>\n                                <span className=\"metric-value risk\">{message.content.marketImpact.revenueAtRisk}</span>\n                              </div>\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Investment Required</span>\n                                <span className=\"metric-value investment\">{message.content.marketImpact.investmentRequired}</span>\n                              </div>\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Timeline</span>\n                                <span className=\"metric-value timeline\">{message.content.marketImpact.timelineForCompliance}</span>\n                              </div>\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Market Opportunity</span>\n                                <span className=\"metric-value opportunity\">{message.content.marketImpact.marketOpportunity}</span>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        <div className=\"key-findings\">\n                          <h4>Key Findings</h4>\n                          {message.content.keyFindings.map((finding, index) => (\n                            <motion.div\n                              key={index}\n                              className=\"finding-card\"\n                              initial={{ opacity: 0, x: -20 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              transition={{ delay: index * 0.1 }}\n                            >\n                              <div className=\"finding-header\">\n                                <div className=\"finding-icon\">\n                                  {finding.type === 'regulatory' && <AlertTriangle size={16} />}\n                                  {finding.type === 'financial' && <TrendingUp size={16} />}\n                                  {finding.type === 'competitive' && <Info size={16} />}\n                                  {finding.type === 'market' && <TrendingUp size={16} />}\n                                  {finding.type === 'technology' && <Info size={16} />}\n                                  {finding.type === 'environmental' && <AlertTriangle size={16} />}\n                                  {finding.type === 'social' && <Info size={16} />}\n                                  {finding.type === 'governance' && <Info size={16} />}\n                                </div>\n                                <div className=\"finding-title\">{finding.title}</div>\n                                <div className=\"finding-badges\">\n                                  <div className={`impact-badge ${finding.impact.toLowerCase()}`}>\n                                    {finding.impact} Impact\n                                  </div>\n                                  {finding.urgency && (\n                                    <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>\n                                      {finding.urgency}\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n                              <p className=\"finding-description\">{finding.description}</p>\n                              {finding.details && (\n                                <div className=\"finding-details\">\n                                  <p>{finding.details}</p>\n                                </div>\n                              )}\n                              {finding.timeline && (\n                                <div className=\"finding-timeline\">\n                                  <strong>Timeline:</strong> {finding.timeline}\n                                </div>\n                              )}\n                              <div className=\"confidence-bar\">\n                                <div className=\"confidence-label\">Confidence: {finding.confidence}%</div>\n                                <div className=\"confidence-progress\">\n                                  <motion.div\n                                    className=\"confidence-fill\"\n                                    initial={{ width: 0 }}\n                                    animate={{ width: `${finding.confidence}%` }}\n                                    transition={{ duration: 1, delay: 0.5 }}\n                                  />\n                                </div>\n                              </div>\n                            </motion.div>\n                          ))}\n                        </div>\n\n                        {message.content.strategicRecommendations && (\n                          <div className=\"strategic-recommendations\">\n                            <h4>Strategic Recommendations</h4>\n                            {message.content.strategicRecommendations.map((rec, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"recommendation-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: 0.3 + index * 0.1 }}\n                              >\n                                <div className=\"recommendation-header\">\n                                  <div className={`priority-badge ${rec.priority.toLowerCase()}`}>\n                                    {rec.priority} Priority\n                                  </div>\n                                  <div className=\"recommendation-investment\">{rec.investment}</div>\n                                </div>\n                                <h5 className=\"recommendation-action\">{rec.action}</h5>\n                                <p className=\"recommendation-description\">{rec.description}</p>\n                                <div className=\"recommendation-timeline\">\n                                  <strong>Timeline:</strong> {rec.timeline}\n                                </div>\n                              </motion.div>\n                            ))}\n                          </div>\n                        )}\n\n                        {message.content.competitiveBenchmarking && (\n                          <div className=\"competitive-benchmarking\">\n                            <h4>Competitive Benchmarking</h4>\n                            <div className=\"competitors-grid\">\n                              {message.content.competitiveBenchmarking.map((comp, index) => (\n                                <motion.div\n                                  key={index}\n                                  className=\"competitor-card\"\n                                  initial={{ opacity: 0, scale: 0.95 }}\n                                  animate={{ opacity: 1, scale: 1 }}\n                                  transition={{ delay: 0.4 + index * 0.1 }}\n                                >\n                                  <h6 className=\"competitor-name\">{comp.company}</h6>\n                                  <div className=\"competitor-strategy\">\n                                    <strong>Strategy:</strong> {comp.strategy}\n                                  </div>\n                                  <div className=\"competitor-analysis\">\n                                    <div className=\"advantage\">\n                                      <strong>Advantage:</strong> {comp.advantage}\n                                    </div>\n                                    <div className=\"weakness\">\n                                      <strong>Weakness:</strong> {comp.weakness}\n                                    </div>\n                                  </div>\n                                </motion.div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n\n                        {message.content.riskAssessment && (\n                          <div className=\"risk-assessment\">\n                            <h4>Risk Assessment</h4>\n                            <div className=\"risk-categories\">\n                              <div className=\"risk-category high-risk\">\n                                <h6>High Risk</h6>\n                                <ul>\n                                  {message.content.riskAssessment.high.map((risk, index) => (\n                                    <li key={index}>{risk}</li>\n                                  ))}\n                                </ul>\n                              </div>\n                              <div className=\"risk-category medium-risk\">\n                                <h6>Medium Risk</h6>\n                                <ul>\n                                  {message.content.riskAssessment.medium.map((risk, index) => (\n                                    <li key={index}>{risk}</li>\n                                  ))}\n                                </ul>\n                              </div>\n                              <div className=\"risk-category low-risk\">\n                                <h6>Low Risk</h6>\n                                <ul>\n                                  {message.content.riskAssessment.low.map((risk, index) => (\n                                    <li key={index}>{risk}</li>\n                                  ))}\n                                </ul>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        <div className=\"sources-section\">\n                          <h4>Sources & References</h4>\n                          <div className=\"sources-grid\">\n                            {message.content.sources.map((source, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"source-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: 0.3 + index * 0.1 }}\n                              >\n                                <div className=\"source-header\">\n                                  <ExternalLink size={14} />\n                                  <span className=\"source-title\">{source.title}</span>\n                                  {source.confidence && (\n                                    <span className={`source-confidence ${source.confidence.toLowerCase()}`}>\n                                      {source.confidence}\n                                    </span>\n                                  )}\n                                </div>\n                                <div className=\"source-meta\">\n                                  <span className=\"source-url\">{source.url}</span>\n                                  <span className=\"source-date\">{source.date}</span>\n                                  <span className=\"source-type\">{source.type}</span>\n                                </div>\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        {message.content.actionItems && (\n                          <div className=\"action-items\">\n                            <h4>Quick Action Items</h4>\n                            <ul>\n                              {message.content.actionItems.map((item, index) => (\n                                <motion.li\n                                  key={index}\n                                  initial={{ opacity: 0, x: -10 }}\n                                  animate={{ opacity: 1, x: 0 }}\n                                  transition={{ delay: 0.5 + index * 0.1 }}\n                                >\n                                  {item}\n                                </motion.li>\n                              ))}\n                            </ul>\n                          </div>\n                        )}\n                      </div>\n                    ) : (\n                      <div className=\"simple-response\">{message.content}</div>\n                    )}\n                  </div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            className=\"loading-message\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"loading-content\">\n              <Loader2 className=\"loading-spinner\" size={20} />\n              <span>Analyzing ESG data and regulations...</span>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"message-input-container\">\n        <div className=\"message-input-box\">\n          <textarea\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask a follow-up question about ESG intelligence...\"\n            className=\"message-input\"\n            rows=\"1\"\n          />\n          <motion.button\n            className=\"send-btn\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || isLoading}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Send size={18} />\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ConversationView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,cAAc;AACrB,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMmC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMkC,aAAa,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC5CJ,mBAAmB,CAACK,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE,GAAG,CAACC,IAAI,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAN,cAAc,CAACO,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED3C,SAAS,CAAC,MAAM;IACduC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;EAEd1B,SAAS,CAAC,MAAM;IACd,IAAIuB,YAAY,EAAE;MAChB;MACA,MAAMqB,WAAW,GAAG;QAClBC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAExB,YAAY;QACrByB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDtB,WAAW,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC1Bb,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAmB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAAC7B,YAAY,CAAC;UAC3CyB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CpB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAElB,MAAM6B,oBAAoB,GAAIC,KAAK,IAAK;IACtC;IACA,MAAMC,gBAAgB,GAAID,KAAK,IAAK;MAClC,MAAME,UAAU,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;MAEtC,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpG,OAAOC,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIH,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvE,OAAOE,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIJ,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC9E,OAAOG,6BAA6B,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIL,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC5E,OAAOI,wBAAwB,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIN,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxE,OAAOK,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOC,8BAA8B,CAAC,CAAC;MACzC;IACF,CAAC;IAED,OAAOT,gBAAgB,CAACD,KAAK,CAAC;EAChC,CAAC;EAED,MAAMK,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLM,UAAU,EAAE,iCAAiC;MAC7CC,OAAO,EAAE,qDAAqD;MAC9DC,MAAM,EAAE,uEAAuE;MAC/EC,OAAO,EAAE,kCAAkC;MAC3CC,WAAW,EAAE,8DAA8D;MAC3EC,WAAW,EAAE,CACX;QACEvB,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,yCAAyC;QAChDJ,MAAM,EAAE,UAAU;QAClBK,WAAW,EAAE,uEAAuE;QACpFC,MAAM,EAAE;MACV,CAAC,EACD;QACE1B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,iDAAiD;QAC9DC,MAAM,EAAE;MACV,CAAC,EACD;QACE1B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,0BAA0B;QACjCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,mDAAmD;QAChEC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,gBAAgB,EAAE,CAChB;QACE3B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,uDAAuD;QAC9DJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,mJAAmJ;QAChKG,OAAO,EAAE,8MAA8M;QACvNC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,qHAAqH;QAClIG,OAAO,EAAE,0LAA0L;QACnMC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,6BAA6B;QACpCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,6GAA6G;QAC1HG,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,yBAAyB;QACxCC,kBAAkB,EAAE,YAAY;QAChCC,qBAAqB,EAAE,yBAAyB;QAChDC,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;QACxFC,MAAM,EAAE,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,8BAA8B,CAAC;QACxGC,GAAG,EAAE,CAAC,qBAAqB,EAAE,+BAA+B;MAC9D,CAAC;MACDC,SAAS,EAAE,CACT;QACEC,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,cAAc;QACxBY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,UAAU;QACpBY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,iCAAiC;QACzCI,QAAQ,EAAE,WAAW;QACrBY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDkB,kBAAkB,EAAE,CAClB;QACEF,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,+CAA+C;QACvDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,mDAAmD;QAC3DI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,QAAQ;QAClBf,MAAM,EAAE,kCAAkC;QAC1CI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDmB,uBAAuB,EAAE,CACvB;QACEC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,qEAAqE;QAC/EC,SAAS,EAAE,uDAAuD;QAClEC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,6DAA6D;QACvEC,SAAS,EAAE,iDAAiD;QAC5DC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE,4DAA4D;QACtEC,SAAS,EAAE,qDAAqD;QAChEC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,2CAA2C;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAChJ;QAAEL,KAAK,EAAE,qDAAqD;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAChJ;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAChI;QAAEL,KAAK,EAAE,kCAAkC;QAAE0B,GAAG,EAAE,oBAAoB;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAClI;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAS,CAAC,EACtI;QAAEL,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,aAAa;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,QAAQ;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAElI,CAAC;EACH,CAAC;EAED,MAAMhB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACLK,UAAU,EAAE,2DAA2D;MACvEkC,gBAAgB,EAAE,qRAAqR;MACvSC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,6IAA6I;QACtJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uGAAuG;QACpHG,OAAO,EAAE,0JAA0J;QACnKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,gFAAgF;QAC7FG,OAAO,EAAE,kJAAkJ;QAC3JC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,8CAA8C;QACrDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0GAA0G;QACvHG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,wCAAwC;QAC/CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,2BAA2B;QAC1CC,kBAAkB,EAAE,4BAA4B;QAChDC,qBAAqB,EAAE,kCAAkC;QACzDC,iBAAiB,EAAE;MACrB,CAAC;MACDmB,wBAAwB,EAAE,CACxB;QACEb,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,kDAAkD;QAC1DI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,MAAM;QAClBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,yCAAyC;QACjDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,0CAA0C;QAClDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDwB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,6BAA6B;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,+CAA+C;QAAE0B,GAAG,EAAE,+BAA+B;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAC9J;QAAEL,KAAK,EAAE,8CAA8C;QAAE0B,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMZ,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,OAAO;MACLC,UAAU,EAAE,uCAAuC;MACnDkC,gBAAgB,EAAE,oWAAoW;MACtXC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,eAAe;QACrBwB,KAAK,EAAE,sCAAsC;QAC7CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,uKAAuK;QAChLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uHAAuH;QACpIG,OAAO,EAAE,sKAAsK;QAC/KC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,4CAA4C;QACnDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,4GAA4G;QACzHG,OAAO,EAAE,6JAA6J;QACtKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,iDAAiD;QACxDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,+JAA+J;QACxKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,iCAAiC;QACxCJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,gJAAgJ;QACzJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDwB,wBAAwB,EAAE,CACxB;QACEb,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,8CAA8C;QACtDI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,2CAA2C;QACnDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDwB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,qCAAqC;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,QAAQ;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAC3H;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAS,CAAC;IAEtI,CAAC;EACH,CAAC;EAED,MAAMf,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,OAAO;MACLI,UAAU,EAAE,sCAAsC;MAClDkC,gBAAgB,EAAE,sQAAsQ;MACxRC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,qCAAqC;QAC5CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0EAA0E;QACvFG,OAAO,EAAE,wIAAwI;QACjJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,oDAAoD;QAAE0B,GAAG,EAAE,8BAA8B;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAElK,CAAC;EACH,CAAC;EAED,MAAMd,wBAAwB,GAAGA,CAAA,KAAM;IACrC,OAAO;MACLG,UAAU,EAAE,mCAAmC;MAC/CkC,gBAAgB,EAAE,kPAAkP;MACpQC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,wHAAwH;QACrIG,OAAO,EAAE,wHAAwH;QACjIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEhI,CAAC;EACH,CAAC;EAED,MAAMb,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLE,UAAU,EAAE,kCAAkC;MAC9CkC,gBAAgB,EAAE,gPAAgP;MAClQC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,0HAA0H;QACnIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIzE,UAAU,CAAC0E,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM1D,WAAW,GAAG;QAClBC,EAAE,EAAEnB,QAAQ,CAAC6E,MAAM,GAAG,CAAC;QACvBzD,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEnB,UAAU;QACnBoB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,WAAW,CAAC,CAAC;MAC3Cf,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAmB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAEnB,QAAQ,CAAC6E,MAAM,GAAG,CAAC;UACvBzD,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAACxB,UAAU,CAAC;UACzCoB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CpB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMyE,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,WAAW,GAAIjE,OAAO,IAAK;IAC/BkE,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,OAAOpE,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGqE,IAAI,CAACC,SAAS,CAACtE,OAAO,CAAC,CAAC;EAChG,CAAC;EAED,oBACE1B,OAAA,CAACnB,MAAM,CAACoH,GAAG;IACTC,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAC7BG,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9B1G,OAAA;MAAKkG,SAAS,EAAC,qBAAqB;MAAAQ,QAAA,gBAClC1G,OAAA,CAACnB,MAAM,CAAC8H,MAAM;QACZT,SAAS,EAAC,UAAU;QACpBU,OAAO,EAAEzG,MAAO;QAChB0G,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAJ,QAAA,gBAE1B1G,OAAA,CAACjB,SAAS;UAACiI,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBpH,OAAA;QAAKkG,SAAS,EAAC,sBAAsB;QAAAQ,QAAA,gBACnC1G,OAAA,CAACnB,MAAM,CAAC8H,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAEpB,WAAY;UACrBqB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B1G,OAAA,CAACf,QAAQ;YAAC+H,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChBpH,OAAA,CAACnB,MAAM,CAAC8H,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B1G,OAAA,CAACR,MAAM;YAACwH,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpH,OAAA;MAAKkG,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,gBACjC1G,OAAA,CAAClB,eAAe;QAAA4H,QAAA,EACbrG,QAAQ,CAACgH,GAAG,CAAEC,OAAO,iBACpBtH,OAAA,CAACnB,MAAM,CAACoH,GAAG;UAETC,SAAS,EAAE,WAAWoB,OAAO,CAAC7F,IAAI,EAAG;UACrC0E,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAG,CAAE;UAC/BjB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAE,CAAE;UAC9BhB,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7Bf,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAC,QAAA,EAE7BY,OAAO,CAAC7F,IAAI,KAAK,MAAM,gBACtBzB,OAAA;YAAKkG,SAAS,EAAC,cAAc;YAAAQ,QAAA,gBAC3B1G,OAAA;cAAKkG,SAAS,EAAC,iBAAiB;cAAAQ,QAAA,EAAEY,OAAO,CAAC5F;YAAO;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDpH,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BY,OAAO,CAAC3F,SAAS,CAAC6F,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENpH,OAAA;YAAKkG,SAAS,EAAC,YAAY;YAAAQ,QAAA,gBACzB1G,OAAA;cAAKkG,SAAS,EAAC,aAAa;cAAAQ,QAAA,EACzB,OAAOY,OAAO,CAAC5F,OAAO,KAAK,QAAQ,gBAClC1B,OAAA;gBAAKkG,SAAS,EAAC,qBAAqB;gBAAAQ,QAAA,gBAClC1G,OAAA;kBAAKkG,SAAS,EAAC,eAAe;kBAAAQ,QAAA,gBAC5B1G,OAAA;oBAAKkG,SAAS,EAAC,sBAAsB;oBAAAQ,QAAA,gBACnC1G,OAAA;sBAAA0G,QAAA,EAAKY,OAAO,CAAC5F,OAAO,CAACiB,UAAU,IAAI;oBAAyB;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClEpH,OAAA;sBAAKkG,SAAS,EAAC,aAAa;sBAAAQ,QAAA,gBAC1B1G,OAAA;wBAAMkG,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,GAAC,aAAW,EAAC,IAAI9E,IAAI,CAAC,CAAC,CAAC+F,kBAAkB,CAAC,CAAC;sBAAA;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFpH,OAAA;wBAAMkG,SAAS,EAAC,mBAAmB;wBAAAQ,QAAA,EAAC;sBAAwB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNpH,OAAA;oBACEkG,SAAS,EAAC,UAAU;oBACpBU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC2B,OAAO,CAAC5F,OAAO,CAAE;oBAAAgF,QAAA,eAE5C1G,OAAA,CAACT,IAAI;sBAACyH,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAELE,OAAO,CAAC5F,OAAO,CAACmD,gBAAgB,iBAC/B7E,OAAA;kBAAKkG,SAAS,EAAC,mBAAmB;kBAAAQ,QAAA,gBAChC1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAiB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BpH,OAAA;oBAAA0G,QAAA,EAAIY,OAAO,CAAC5F,OAAO,CAACmD;kBAAgB;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACN,EAEAE,OAAO,CAAC5F,OAAO,CAAC8B,YAAY,iBAC3BxD,OAAA;kBAAKkG,SAAS,EAAC,uBAAuB;kBAAAQ,QAAA,gBACpC1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAsB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/BpH,OAAA;oBAAKkG,SAAS,EAAC,gBAAgB;oBAAAQ,QAAA,gBAC7B1G,OAAA;sBAAKkG,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B1G,OAAA;wBAAMkG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAe;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrDpH,OAAA;wBAAMkG,SAAS,EAAC,mBAAmB;wBAAAQ,QAAA,EAAEY,OAAO,CAAC5F,OAAO,CAAC8B,YAAY,CAACC;sBAAa;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACNpH,OAAA;sBAAKkG,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B1G,OAAA;wBAAMkG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAmB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzDpH,OAAA;wBAAMkG,SAAS,EAAC,yBAAyB;wBAAAQ,QAAA,EAAEY,OAAO,CAAC5F,OAAO,CAAC8B,YAAY,CAACE;sBAAkB;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC,eACNpH,OAAA;sBAAKkG,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B1G,OAAA;wBAAMkG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAQ;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9CpH,OAAA;wBAAMkG,SAAS,EAAC,uBAAuB;wBAAAQ,QAAA,EAAEY,OAAO,CAAC5F,OAAO,CAAC8B,YAAY,CAACG;sBAAqB;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACNpH,OAAA;sBAAKkG,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B1G,OAAA;wBAAMkG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAkB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxDpH,OAAA;wBAAMkG,SAAS,EAAC,0BAA0B;wBAAAQ,QAAA,EAAEY,OAAO,CAAC5F,OAAO,CAAC8B,YAAY,CAACI;sBAAiB;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDpH,OAAA;kBAAKkG,SAAS,EAAC,cAAc;kBAAAQ,QAAA,gBAC3B1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAY;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACpBE,OAAO,CAAC5F,OAAO,CAACoD,WAAW,CAACuC,GAAG,CAAC,CAACO,OAAO,EAAEC,KAAK,kBAC9C7H,OAAA,CAACnB,MAAM,CAACoH,GAAG;oBAETC,SAAS,EAAC,cAAc;oBACxBC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BG,UAAU,EAAE;sBAAEsB,KAAK,EAAED,KAAK,GAAG;oBAAI,CAAE;oBAAAnB,QAAA,gBAEnC1G,OAAA;sBAAKkG,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,gBAC7B1G,OAAA;wBAAKkG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,GAC1BkB,OAAO,CAACnG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACZ,aAAa;0BAAC4H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC5DQ,OAAO,CAACnG,IAAI,KAAK,WAAW,iBAAIzB,OAAA,CAACb,UAAU;0BAAC6H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACxDQ,OAAO,CAACnG,IAAI,KAAK,aAAa,iBAAIzB,OAAA,CAACX,IAAI;0BAAC2H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpDQ,OAAO,CAACnG,IAAI,KAAK,QAAQ,iBAAIzB,OAAA,CAACb,UAAU;0BAAC6H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrDQ,OAAO,CAACnG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACX,IAAI;0BAAC2H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACnDQ,OAAO,CAACnG,IAAI,KAAK,eAAe,iBAAIzB,OAAA,CAACZ,aAAa;0BAAC4H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC/DQ,OAAO,CAACnG,IAAI,KAAK,QAAQ,iBAAIzB,OAAA,CAACX,IAAI;0BAAC2H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC/CQ,OAAO,CAACnG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACX,IAAI;0BAAC2H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,eAAe;wBAAAQ,QAAA,EAAEkB,OAAO,CAAC3E;sBAAK;wBAAAgE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpDpH,OAAA;wBAAKkG,SAAS,EAAC,gBAAgB;wBAAAQ,QAAA,gBAC7B1G,OAAA;0BAAKkG,SAAS,EAAE,gBAAgB0B,OAAO,CAAC/E,MAAM,CAACV,WAAW,CAAC,CAAC,EAAG;0BAAAuE,QAAA,GAC5DkB,OAAO,CAAC/E,MAAM,EAAC,SAClB;wBAAA;0BAAAoE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EACLQ,OAAO,CAAC9E,OAAO,iBACd9C,OAAA;0BAAKkG,SAAS,EAAE,iBAAiB0B,OAAO,CAAC9E,OAAO,CAACX,WAAW,CAAC,CAAC,EAAG;0BAAAuE,QAAA,EAC9DkB,OAAO,CAAC9E;wBAAO;0BAAAmE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpH,OAAA;sBAAGkG,SAAS,EAAC,qBAAqB;sBAAAQ,QAAA,EAAEkB,OAAO,CAAC1E;oBAAW;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC3DQ,OAAO,CAACvE,OAAO,iBACdrD,OAAA;sBAAKkG,SAAS,EAAC,iBAAiB;sBAAAQ,QAAA,eAC9B1G,OAAA;wBAAA0G,QAAA,EAAIkB,OAAO,CAACvE;sBAAO;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CACN,EACAQ,OAAO,CAACrE,QAAQ,iBACfvD,OAAA;sBAAKkG,SAAS,EAAC,kBAAkB;sBAAAQ,QAAA,gBAC/B1G,OAAA;wBAAA0G,QAAA,EAAQ;sBAAS;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACQ,OAAO,CAACrE,QAAQ;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CACN,eACDpH,OAAA;sBAAKkG,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,gBAC7B1G,OAAA;wBAAKkG,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,GAAC,cAAY,EAACkB,OAAO,CAACtE,UAAU,EAAC,GAAC;sBAAA;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzEpH,OAAA;wBAAKkG,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,eAClC1G,OAAA,CAACnB,MAAM,CAACoH,GAAG;0BACTC,SAAS,EAAC,iBAAiB;0BAC3BC,OAAO,EAAE;4BAAE4B,KAAK,EAAE;0BAAE,CAAE;0BACtBzB,OAAO,EAAE;4BAAEyB,KAAK,EAAE,GAAGH,OAAO,CAACtE,UAAU;0BAAI,CAAE;0BAC7CkD,UAAU,EAAE;4BAAEC,QAAQ,EAAE,CAAC;4BAAEqB,KAAK,EAAE;0BAAI;wBAAE;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAlDDS,KAAK;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmDA,CACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAELE,OAAO,CAAC5F,OAAO,CAACqD,wBAAwB,iBACvC/E,OAAA;kBAAKkG,SAAS,EAAC,2BAA2B;kBAAAQ,QAAA,gBACxC1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAyB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACjCE,OAAO,CAAC5F,OAAO,CAACqD,wBAAwB,CAACsC,GAAG,CAAC,CAACW,GAAG,EAAEH,KAAK,kBACvD7H,OAAA,CAACnB,MAAM,CAACoH,GAAG;oBAETC,SAAS,EAAC,qBAAqB;oBAC/BC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEmB,CAAC,EAAE;oBAAG,CAAE;oBAC/BjB,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEmB,CAAC,EAAE;oBAAE,CAAE;oBAC9Bf,UAAU,EAAE;sBAAEsB,KAAK,EAAE,GAAG,GAAGD,KAAK,GAAG;oBAAI,CAAE;oBAAAnB,QAAA,gBAEzC1G,OAAA;sBAAKkG,SAAS,EAAC,uBAAuB;sBAAAQ,QAAA,gBACpC1G,OAAA;wBAAKkG,SAAS,EAAE,kBAAkB8B,GAAG,CAAC9D,QAAQ,CAAC/B,WAAW,CAAC,CAAC,EAAG;wBAAAuE,QAAA,GAC5DsB,GAAG,CAAC9D,QAAQ,EAAC,WAChB;sBAAA;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,2BAA2B;wBAAAQ,QAAA,EAAEsB,GAAG,CAAC7D;sBAAU;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNpH,OAAA;sBAAIkG,SAAS,EAAC,uBAAuB;sBAAAQ,QAAA,EAAEsB,GAAG,CAAC7E;oBAAM;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDpH,OAAA;sBAAGkG,SAAS,EAAC,4BAA4B;sBAAAQ,QAAA,EAAEsB,GAAG,CAAC9E;oBAAW;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/DpH,OAAA;sBAAKkG,SAAS,EAAC,yBAAyB;sBAAAQ,QAAA,gBACtC1G,OAAA;wBAAA0G,QAAA,EAAQ;sBAAS;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACY,GAAG,CAACzE,QAAQ;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA,GAhBDS,KAAK;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiBA,CACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,EAEAE,OAAO,CAAC5F,OAAO,CAAC2C,uBAAuB,iBACtCrE,OAAA;kBAAKkG,SAAS,EAAC,0BAA0B;kBAAAQ,QAAA,gBACvC1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAwB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjCpH,OAAA;oBAAKkG,SAAS,EAAC,kBAAkB;oBAAAQ,QAAA,EAC9BY,OAAO,CAAC5F,OAAO,CAAC2C,uBAAuB,CAACgD,GAAG,CAAC,CAACY,IAAI,EAAEJ,KAAK,kBACvD7H,OAAA,CAACnB,MAAM,CAACoH,GAAG;sBAETC,SAAS,EAAC,iBAAiB;sBAC3BC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEU,KAAK,EAAE;sBAAK,CAAE;sBACrCR,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEU,KAAK,EAAE;sBAAE,CAAE;sBAClCN,UAAU,EAAE;wBAAEsB,KAAK,EAAE,GAAG,GAAGD,KAAK,GAAG;sBAAI,CAAE;sBAAAnB,QAAA,gBAEzC1G,OAAA;wBAAIkG,SAAS,EAAC,iBAAiB;wBAAAQ,QAAA,EAAEuB,IAAI,CAAC3D;sBAAO;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnDpH,OAAA;wBAAKkG,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,gBAClC1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACa,IAAI,CAAC1D,QAAQ;sBAAA;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,gBAClC1G,OAAA;0BAAKkG,SAAS,EAAC,WAAW;0BAAAQ,QAAA,gBACxB1G,OAAA;4BAAA0G,QAAA,EAAQ;0BAAU;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACa,IAAI,CAACzD,SAAS;wBAAA;0BAAAyC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eACNpH,OAAA;0BAAKkG,SAAS,EAAC,UAAU;0BAAAQ,QAAA,gBACvB1G,OAAA;4BAAA0G,QAAA,EAAQ;0BAAS;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACa,IAAI,CAACxD,QAAQ;wBAAA;0BAAAwC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,GAjBDS,KAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkBA,CACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAE,OAAO,CAAC5F,OAAO,CAACmC,cAAc,iBAC7B7D,OAAA;kBAAKkG,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,gBAC9B1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAe;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBpH,OAAA;oBAAKkG,SAAS,EAAC,iBAAiB;oBAAAQ,QAAA,gBAC9B1G,OAAA;sBAAKkG,SAAS,EAAC,yBAAyB;sBAAAQ,QAAA,gBACtC1G,OAAA;wBAAA0G,QAAA,EAAI;sBAAS;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBpH,OAAA;wBAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACmC,cAAc,CAACC,IAAI,CAACuD,GAAG,CAAC,CAACa,IAAI,EAAEL,KAAK,kBACnD7H,OAAA;0BAAA0G,QAAA,EAAiBwB;wBAAI,GAAZL,KAAK;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAC3B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACNpH,OAAA;sBAAKkG,SAAS,EAAC,2BAA2B;sBAAAQ,QAAA,gBACxC1G,OAAA;wBAAA0G,QAAA,EAAI;sBAAW;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpBpH,OAAA;wBAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACmC,cAAc,CAACE,MAAM,CAACsD,GAAG,CAAC,CAACa,IAAI,EAAEL,KAAK,kBACrD7H,OAAA;0BAAA0G,QAAA,EAAiBwB;wBAAI,GAAZL,KAAK;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAC3B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACNpH,OAAA;sBAAKkG,SAAS,EAAC,wBAAwB;sBAAAQ,QAAA,gBACrC1G,OAAA;wBAAA0G,QAAA,EAAI;sBAAQ;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjBpH,OAAA;wBAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACmC,cAAc,CAACG,GAAG,CAACqD,GAAG,CAAC,CAACa,IAAI,EAAEL,KAAK,kBAClD7H,OAAA;0BAAA0G,QAAA,EAAiBwB;wBAAI,GAAZL,KAAK;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAC3B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDpH,OAAA;kBAAKkG,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,gBAC9B1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAoB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7BpH,OAAA;oBAAKkG,SAAS,EAAC,cAAc;oBAAAQ,QAAA,EAC1BY,OAAO,CAAC5F,OAAO,CAACgD,OAAO,CAAC2C,GAAG,CAAC,CAACc,MAAM,EAAEN,KAAK,kBACzC7H,OAAA,CAACnB,MAAM,CAACoH,GAAG;sBAETC,SAAS,EAAC,aAAa;sBACvBC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEmB,CAAC,EAAE;sBAAG,CAAE;sBAC/BjB,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEmB,CAAC,EAAE;sBAAE,CAAE;sBAC9Bf,UAAU,EAAE;wBAAEsB,KAAK,EAAE,GAAG,GAAGD,KAAK,GAAG;sBAAI,CAAE;sBAAAnB,QAAA,gBAEzC1G,OAAA;wBAAKkG,SAAS,EAAC,eAAe;wBAAAQ,QAAA,gBAC5B1G,OAAA,CAACd,YAAY;0BAAC8H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1BpH,OAAA;0BAAMkG,SAAS,EAAC,cAAc;0BAAAQ,QAAA,EAAEyB,MAAM,CAAClF;wBAAK;0BAAAgE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACnDe,MAAM,CAAC7E,UAAU,iBAChBtD,OAAA;0BAAMkG,SAAS,EAAE,qBAAqBiC,MAAM,CAAC7E,UAAU,CAACnB,WAAW,CAAC,CAAC,EAAG;0BAAAuE,QAAA,EACrEyB,MAAM,CAAC7E;wBAAU;0BAAA2D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,aAAa;wBAAAQ,QAAA,gBAC1B1G,OAAA;0BAAMkG,SAAS,EAAC,YAAY;0BAAAQ,QAAA,EAAEyB,MAAM,CAACxD;wBAAG;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAChDpH,OAAA;0BAAMkG,SAAS,EAAC,aAAa;0BAAAQ,QAAA,EAAEyB,MAAM,CAACvD;wBAAI;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAClDpH,OAAA;0BAAMkG,SAAS,EAAC,aAAa;0BAAAQ,QAAA,EAAEyB,MAAM,CAAC1G;wBAAI;0BAAAwF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC;oBAAA,GAnBDS,KAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAoBA,CACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELE,OAAO,CAAC5F,OAAO,CAAC0G,WAAW,iBAC1BpI,OAAA;kBAAKkG,SAAS,EAAC,cAAc;kBAAAQ,QAAA,gBAC3B1G,OAAA;oBAAA0G,QAAA,EAAI;kBAAkB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3BpH,OAAA;oBAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAAC0G,WAAW,CAACf,GAAG,CAAC,CAACgB,IAAI,EAAER,KAAK,kBAC3C7H,OAAA,CAACnB,MAAM,CAACyJ,EAAE;sBAERnC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE,CAAC;sBAAG,CAAE;sBAChCC,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAE,CAAE;sBAC9BG,UAAU,EAAE;wBAAEsB,KAAK,EAAE,GAAG,GAAGD,KAAK,GAAG;sBAAI,CAAE;sBAAAnB,QAAA,EAExC2B;oBAAI,GALAR,KAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMD,CACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAENpH,OAAA;gBAAKkG,SAAS,EAAC,iBAAiB;gBAAAQ,QAAA,EAAEY,OAAO,CAAC5F;cAAO;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACxD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNpH,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BY,OAAO,CAAC3F,SAAS,CAAC6F,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN,GA9QIE,OAAO,CAAC9F,EAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+QL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEjB3G,SAAS,iBACRT,OAAA,CAACnB,MAAM,CAACoH,GAAG;QACTC,SAAS,EAAC,iBAAiB;QAC3BC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAG,CAAE;QAC/BjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAAAb,QAAA,eAE9B1G,OAAA;UAAKkG,SAAS,EAAC,iBAAiB;UAAAQ,QAAA,gBAC9B1G,OAAA,CAACV,OAAO;YAAC4G,SAAS,EAAC,iBAAiB;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDpH,OAAA;YAAA0G,QAAA,EAAM;UAAqC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDpH,OAAA;QAAKuI,GAAG,EAAE1H;MAAe;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNpH,OAAA;MAAKkG,SAAS,EAAC,yBAAyB;MAAAQ,QAAA,eACtC1G,OAAA;QAAKkG,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChC1G,OAAA;UACEwI,KAAK,EAAEjI,UAAW;UAClBkI,QAAQ,EAAGrD,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACsD,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAExD,cAAe;UAC3ByD,WAAW,EAAC,oDAAoD;UAChE1C,SAAS,EAAC,eAAe;UACzB2C,IAAI,EAAC;QAAG;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpH,OAAA,CAACnB,MAAM,CAAC8H,MAAM;UACZT,SAAS,EAAC,UAAU;UACpBU,OAAO,EAAE5B,iBAAkB;UAC3B8D,QAAQ,EAAE,CAACvI,UAAU,CAAC0E,IAAI,CAAC,CAAC,IAAIxE,SAAU;UAC1CoG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,eAE1B1G,OAAA,CAAChB,IAAI;YAACgI,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAChH,EAAA,CAh3BIH,gBAAgB;AAAA8I,EAAA,GAAhB9I,gBAAgB;AAk3BtB,eAAeA,gBAAgB;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}