{"ast": null, "code": "import { warnOnce } from 'motion-utils';\nfunction createDOMMotionComponentProxy(componentFactory) {\n  if (typeof Proxy === \"undefined\") {\n    return componentFactory;\n  }\n  /**\n   * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n   * Rather than generating them anew every render.\n   */\n  const componentCache = new Map();\n  const deprecatedFactoryFunction = (...args) => {\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(false, \"motion() is deprecated. Use motion.create() instead.\");\n    }\n    return componentFactory(...args);\n  };\n  return new Proxy(deprecatedFactoryFunction, {\n    /**\n     * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n     * The prop name is passed through as `key` and we can use that to generate a `motion`\n     * DOM component with that name.\n     */\n    get: (_target, key) => {\n      if (key === \"create\") return componentFactory;\n      /**\n       * If this element doesn't exist in the component cache, create it and cache.\n       */\n      if (!componentCache.has(key)) {\n        componentCache.set(key, componentFactory(key));\n      }\n      return componentCache.get(key);\n    }\n  });\n}\nexport { createDOMMotionComponentProxy };", "map": {"version": 3, "names": ["warnOnce", "createDOMMotionComponentProxy", "componentFactory", "Proxy", "componentCache", "Map", "deprecatedFactoryFunction", "args", "process", "env", "NODE_ENV", "get", "_target", "key", "has", "set"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs"], "sourcesContent": ["import { warnOnce } from 'motion-utils';\n\nfunction createDOMMotionComponentProxy(componentFactory) {\n    if (typeof Proxy === \"undefined\") {\n        return componentFactory;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    const deprecatedFactoryFunction = (...args) => {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, \"motion() is deprecated. Use motion.create() instead.\");\n        }\n        return componentFactory(...args);\n    };\n    return new Proxy(deprecatedFactoryFunction, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            if (key === \"create\")\n                return componentFactory;\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, componentFactory(key));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\nexport { createDOMMotionComponentProxy };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,SAASC,6BAA6BA,CAACC,gBAAgB,EAAE;EACrD,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;IAC9B,OAAOD,gBAAgB;EAC3B;EACA;AACJ;AACA;AACA;EACI,MAAME,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC,MAAMC,yBAAyB,GAAGA,CAAC,GAAGC,IAAI,KAAK;IAC3C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvCV,QAAQ,CAAC,KAAK,EAAE,sDAAsD,CAAC;IAC3E;IACA,OAAOE,gBAAgB,CAAC,GAAGK,IAAI,CAAC;EACpC,CAAC;EACD,OAAO,IAAIJ,KAAK,CAACG,yBAAyB,EAAE;IACxC;AACR;AACA;AACA;AACA;IACQK,GAAG,EAAEA,CAACC,OAAO,EAAEC,GAAG,KAAK;MACnB,IAAIA,GAAG,KAAK,QAAQ,EAChB,OAAOX,gBAAgB;MAC3B;AACZ;AACA;MACY,IAAI,CAACE,cAAc,CAACU,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BT,cAAc,CAACW,GAAG,CAACF,GAAG,EAAEX,gBAAgB,CAACW,GAAG,CAAC,CAAC;MAClD;MACA,OAAOT,cAAc,CAACO,GAAG,CAACE,GAAG,CAAC;IAClC;EACJ,CAAC,CAAC;AACN;AAEA,SAASZ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}