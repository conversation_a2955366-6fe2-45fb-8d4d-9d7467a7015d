{"ast": null, "code": "import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nfunction canSetAsProperty(element, name) {\n  if (!(name in element)) return false;\n  const descriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(element), name) || Object.getOwnPropertyDescriptor(element, name);\n  // Check if it has a setter\n  return descriptor && typeof descriptor.set === \"function\";\n}\nconst addAttrValue = (element, state, key, value) => {\n  const isProp = canSetAsProperty(element, key);\n  const name = isProp ? key : key.startsWith(\"data\") || key.startsWith(\"aria\") ? camelToDash(key) : key;\n  /**\n   * Set attribute directly via property if available\n   */\n  const render = isProp ? () => {\n    element[name] = state.latest[key];\n  } : () => {\n    const v = state.latest[key];\n    if (v === null || v === undefined) {\n      element.removeAttribute(name);\n    } else {\n      element.setAttribute(name, String(v));\n    }\n  };\n  return state.set(key, value, render);\n};\nconst attrEffect = /*@__PURE__*/createSelectorEffect(/*@__PURE__*/createEffect(addAttrValue));\nexport { addAttrValue, attrEffect };", "map": {"version": 3, "names": ["camelToDash", "createSelectorEffect", "createEffect", "canSetAsProperty", "element", "name", "descriptor", "Object", "getOwnPropertyDescriptor", "getPrototypeOf", "set", "addAttrValue", "state", "key", "value", "isProp", "startsWith", "render", "latest", "v", "undefined", "removeAttribute", "setAttribute", "String", "attrEffect"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/effects/attr/index.mjs"], "sourcesContent": ["import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\n\nfunction canSetAsProperty(element, name) {\n    if (!(name in element))\n        return false;\n    const descriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(element), name) ||\n        Object.getOwnPropertyDescriptor(element, name);\n    // Check if it has a setter\n    return descriptor && typeof descriptor.set === \"function\";\n}\nconst addAttrValue = (element, state, key, value) => {\n    const isProp = canSetAsProperty(element, key);\n    const name = isProp\n        ? key\n        : key.startsWith(\"data\") || key.startsWith(\"aria\")\n            ? camelToDash(key)\n            : key;\n    /**\n     * Set attribute directly via property if available\n     */\n    const render = isProp\n        ? () => {\n            element[name] = state.latest[key];\n        }\n        : () => {\n            const v = state.latest[key];\n            if (v === null || v === undefined) {\n                element.removeAttribute(name);\n            }\n            else {\n                element.setAttribute(name, String(v));\n            }\n        };\n    return state.set(key, value, render);\n};\nconst attrEffect = /*@__PURE__*/ createSelectorEffect(\n/*@__PURE__*/ createEffect(addAttrValue));\n\nexport { addAttrValue, attrEffect };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,0CAA0C;AACtE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,YAAY,QAAQ,4BAA4B;AAEzD,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACrC,IAAI,EAAEA,IAAI,IAAID,OAAO,CAAC,EAClB,OAAO,KAAK;EAChB,MAAME,UAAU,GAAGC,MAAM,CAACC,wBAAwB,CAACD,MAAM,CAACE,cAAc,CAACL,OAAO,CAAC,EAAEC,IAAI,CAAC,IACpFE,MAAM,CAACC,wBAAwB,CAACJ,OAAO,EAAEC,IAAI,CAAC;EAClD;EACA,OAAOC,UAAU,IAAI,OAAOA,UAAU,CAACI,GAAG,KAAK,UAAU;AAC7D;AACA,MAAMC,YAAY,GAAGA,CAACP,OAAO,EAAEQ,KAAK,EAAEC,GAAG,EAAEC,KAAK,KAAK;EACjD,MAAMC,MAAM,GAAGZ,gBAAgB,CAACC,OAAO,EAAES,GAAG,CAAC;EAC7C,MAAMR,IAAI,GAAGU,MAAM,GACbF,GAAG,GACHA,GAAG,CAACG,UAAU,CAAC,MAAM,CAAC,IAAIH,GAAG,CAACG,UAAU,CAAC,MAAM,CAAC,GAC5ChB,WAAW,CAACa,GAAG,CAAC,GAChBA,GAAG;EACb;AACJ;AACA;EACI,MAAMI,MAAM,GAAGF,MAAM,GACf,MAAM;IACJX,OAAO,CAACC,IAAI,CAAC,GAAGO,KAAK,CAACM,MAAM,CAACL,GAAG,CAAC;EACrC,CAAC,GACC,MAAM;IACJ,MAAMM,CAAC,GAAGP,KAAK,CAACM,MAAM,CAACL,GAAG,CAAC;IAC3B,IAAIM,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS,EAAE;MAC/BhB,OAAO,CAACiB,eAAe,CAAChB,IAAI,CAAC;IACjC,CAAC,MACI;MACDD,OAAO,CAACkB,YAAY,CAACjB,IAAI,EAAEkB,MAAM,CAACJ,CAAC,CAAC,CAAC;IACzC;EACJ,CAAC;EACL,OAAOP,KAAK,CAACF,GAAG,CAACG,GAAG,EAAEC,KAAK,EAAEG,MAAM,CAAC;AACxC,CAAC;AACD,MAAMO,UAAU,GAAG,aAAcvB,oBAAoB,CACrD,aAAcC,YAAY,CAACS,YAAY,CAAC,CAAC;AAEzC,SAASA,YAAY,EAAEa,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}