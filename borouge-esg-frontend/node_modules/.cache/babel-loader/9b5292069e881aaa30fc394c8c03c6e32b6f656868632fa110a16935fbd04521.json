{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}], [\"path\", {\n  d: \"M7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16\",\n  key: \"qmtpty\"\n}], [\"path\", {\n  d: \"M9.5 4h5L17 7h3a2 2 0 0 1 2 2v7.5\",\n  key: \"1ufyfc\"\n}], [\"path\", {\n  d: \"M14.121 15.121A3 3 0 1 1 9.88 10.88\",\n  key: \"11zox6\"\n}]];\nconst CameraOff = createLucideIcon(\"camera-off\", __iconNode);\nexport { __iconNode, CameraOff as default };", "map": {"version": 3, "names": ["__iconNode", "x1", "x2", "y1", "y2", "key", "d", "CameraOff", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/camera-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n  ['path', { d: 'M7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16', key: 'qmtpty' }],\n  ['path', { d: 'M9.5 4h5L17 7h3a2 2 0 0 1 2 2v7.5', key: '1ufyfc' }],\n  ['path', { d: 'M14.121 15.121A3 3 0 1 1 9.88 10.88', key: '11zox6' }],\n];\n\n/**\n * @component @name CameraOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMiIgeDI9IjIyIiB5MT0iMiIgeTI9IjIyIiAvPgogIDxwYXRoIGQ9Ik03IDdINGEyIDIgMCAwIDAtMiAydjlhMiAyIDAgMCAwIDIgMmgxNiIgLz4KICA8cGF0aCBkPSJNOS41IDRoNUwxNyA3aDNhMiAyIDAgMCAxIDIgMnY3LjUiIC8+CiAgPHBhdGggZD0iTTE0LjEyMSAxNS4xMjFBMyAzIDAgMSAxIDkuODggMTAuODgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/camera-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CameraOff = createLucideIcon('camera-off', __iconNode);\n\nexport default CameraOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAEC,CAAA,EAAG,mCAAqC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAEC,CAAA,EAAG,qCAAuC;EAAAD,GAAA,EAAK;AAAA,CAAU,EACtE;AAaM,MAAAE,SAAA,GAAYC,gBAAiB,eAAcR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}