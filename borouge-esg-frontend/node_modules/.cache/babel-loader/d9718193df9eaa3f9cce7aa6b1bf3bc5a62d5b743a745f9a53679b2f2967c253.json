{"ast": null, "code": "import { activeAnimations } from './animation-count.mjs';\nimport { statsBuffer } from './buffer.mjs';\nimport { frame, cancelFrame, frameData } from '../frameloop/frame.mjs';\nfunction record() {\n  const {\n    value\n  } = statsBuffer;\n  if (value === null) {\n    cancelFrame(record);\n    return;\n  }\n  value.frameloop.rate.push(frameData.delta);\n  value.animations.mainThread.push(activeAnimations.mainThread);\n  value.animations.waapi.push(activeAnimations.waapi);\n  value.animations.layout.push(activeAnimations.layout);\n}\nfunction mean(values) {\n  return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n  if (values.length === 0) {\n    return {\n      min: 0,\n      max: 0,\n      avg: 0\n    };\n  }\n  return {\n    min: Math.min(...values),\n    max: Math.max(...values),\n    avg: calcAverage(values)\n  };\n}\nconst msToFps = ms => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n  statsBuffer.value = null;\n  statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n  const {\n    value\n  } = statsBuffer;\n  if (!value) {\n    throw new Error(\"Stats are not being measured\");\n  }\n  clearStatsBuffer();\n  cancelFrame(record);\n  const summary = {\n    frameloop: {\n      setup: summarise(value.frameloop.setup),\n      rate: summarise(value.frameloop.rate),\n      read: summarise(value.frameloop.read),\n      resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n      preUpdate: summarise(value.frameloop.preUpdate),\n      update: summarise(value.frameloop.update),\n      preRender: summarise(value.frameloop.preRender),\n      render: summarise(value.frameloop.render),\n      postRender: summarise(value.frameloop.postRender)\n    },\n    animations: {\n      mainThread: summarise(value.animations.mainThread),\n      waapi: summarise(value.animations.waapi),\n      layout: summarise(value.animations.layout)\n    },\n    layoutProjection: {\n      nodes: summarise(value.layoutProjection.nodes),\n      calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n      calculatedProjections: summarise(value.layoutProjection.calculatedProjections)\n    }\n  };\n  /**\n   * Convert the rate to FPS\n   */\n  const {\n    rate\n  } = summary.frameloop;\n  rate.min = msToFps(rate.min);\n  rate.max = msToFps(rate.max);\n  rate.avg = msToFps(rate.avg);\n  [rate.min, rate.max] = [rate.max, rate.min];\n  return summary;\n}\nfunction recordStats() {\n  if (statsBuffer.value) {\n    clearStatsBuffer();\n    throw new Error(\"Stats are already being measured\");\n  }\n  const newStatsBuffer = statsBuffer;\n  newStatsBuffer.value = {\n    frameloop: {\n      setup: [],\n      rate: [],\n      read: [],\n      resolveKeyframes: [],\n      preUpdate: [],\n      update: [],\n      preRender: [],\n      render: [],\n      postRender: []\n    },\n    animations: {\n      mainThread: [],\n      waapi: [],\n      layout: []\n    },\n    layoutProjection: {\n      nodes: [],\n      calculatedTargetDeltas: [],\n      calculatedProjections: []\n    }\n  };\n  newStatsBuffer.addProjectionMetrics = metrics => {\n    const {\n      layoutProjection\n    } = newStatsBuffer.value;\n    layoutProjection.nodes.push(metrics.nodes);\n    layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n    layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n  };\n  frame.postRender(record, true);\n  return reportStats;\n}\nexport { recordStats };", "map": {"version": 3, "names": ["activeAnimations", "statsBuffer", "frame", "cancelFrame", "frameData", "record", "value", "frameloop", "rate", "push", "delta", "animations", "mainThread", "waapi", "layout", "mean", "values", "reduce", "acc", "length", "summarise", "calcAverage", "min", "max", "avg", "Math", "msToFps", "ms", "round", "clearStatsBuffer", "addProjectionMetrics", "reportStats", "Error", "summary", "setup", "read", "resolveKeyframes", "preUpdate", "update", "preRender", "render", "postRender", "layoutProjection", "nodes", "calculatedTargetDeltas", "calculatedProjections", "recordStats", "newStatsBuffer", "metrics"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/stats/index.mjs"], "sourcesContent": ["import { activeAnimations } from './animation-count.mjs';\nimport { statsBuffer } from './buffer.mjs';\nimport { frame, cancelFrame, frameData } from '../frameloop/frame.mjs';\n\nfunction record() {\n    const { value } = statsBuffer;\n    if (value === null) {\n        cancelFrame(record);\n        return;\n    }\n    value.frameloop.rate.push(frameData.delta);\n    value.animations.mainThread.push(activeAnimations.mainThread);\n    value.animations.waapi.push(activeAnimations.waapi);\n    value.animations.layout.push(activeAnimations.layout);\n}\nfunction mean(values) {\n    return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n    if (values.length === 0) {\n        return {\n            min: 0,\n            max: 0,\n            avg: 0,\n        };\n    }\n    return {\n        min: Math.min(...values),\n        max: Math.max(...values),\n        avg: calcAverage(values),\n    };\n}\nconst msToFps = (ms) => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n    statsBuffer.value = null;\n    statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n    const { value } = statsBuffer;\n    if (!value) {\n        throw new Error(\"Stats are not being measured\");\n    }\n    clearStatsBuffer();\n    cancelFrame(record);\n    const summary = {\n        frameloop: {\n            setup: summarise(value.frameloop.setup),\n            rate: summarise(value.frameloop.rate),\n            read: summarise(value.frameloop.read),\n            resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n            preUpdate: summarise(value.frameloop.preUpdate),\n            update: summarise(value.frameloop.update),\n            preRender: summarise(value.frameloop.preRender),\n            render: summarise(value.frameloop.render),\n            postRender: summarise(value.frameloop.postRender),\n        },\n        animations: {\n            mainThread: summarise(value.animations.mainThread),\n            waapi: summarise(value.animations.waapi),\n            layout: summarise(value.animations.layout),\n        },\n        layoutProjection: {\n            nodes: summarise(value.layoutProjection.nodes),\n            calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n            calculatedProjections: summarise(value.layoutProjection.calculatedProjections),\n        },\n    };\n    /**\n     * Convert the rate to FPS\n     */\n    const { rate } = summary.frameloop;\n    rate.min = msToFps(rate.min);\n    rate.max = msToFps(rate.max);\n    rate.avg = msToFps(rate.avg);\n    [rate.min, rate.max] = [rate.max, rate.min];\n    return summary;\n}\nfunction recordStats() {\n    if (statsBuffer.value) {\n        clearStatsBuffer();\n        throw new Error(\"Stats are already being measured\");\n    }\n    const newStatsBuffer = statsBuffer;\n    newStatsBuffer.value = {\n        frameloop: {\n            setup: [],\n            rate: [],\n            read: [],\n            resolveKeyframes: [],\n            preUpdate: [],\n            update: [],\n            preRender: [],\n            render: [],\n            postRender: [],\n        },\n        animations: {\n            mainThread: [],\n            waapi: [],\n            layout: [],\n        },\n        layoutProjection: {\n            nodes: [],\n            calculatedTargetDeltas: [],\n            calculatedProjections: [],\n        },\n    };\n    newStatsBuffer.addProjectionMetrics = (metrics) => {\n        const { layoutProjection } = newStatsBuffer.value;\n        layoutProjection.nodes.push(metrics.nodes);\n        layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n        layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n    };\n    frame.postRender(record, true);\n    return reportStats;\n}\n\nexport { recordStats };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,wBAAwB;AAEtE,SAASC,MAAMA,CAAA,EAAG;EACd,MAAM;IAAEC;EAAM,CAAC,GAAGL,WAAW;EAC7B,IAAIK,KAAK,KAAK,IAAI,EAAE;IAChBH,WAAW,CAACE,MAAM,CAAC;IACnB;EACJ;EACAC,KAAK,CAACC,SAAS,CAACC,IAAI,CAACC,IAAI,CAACL,SAAS,CAACM,KAAK,CAAC;EAC1CJ,KAAK,CAACK,UAAU,CAACC,UAAU,CAACH,IAAI,CAACT,gBAAgB,CAACY,UAAU,CAAC;EAC7DN,KAAK,CAACK,UAAU,CAACE,KAAK,CAACJ,IAAI,CAACT,gBAAgB,CAACa,KAAK,CAAC;EACnDP,KAAK,CAACK,UAAU,CAACG,MAAM,CAACL,IAAI,CAACT,gBAAgB,CAACc,MAAM,CAAC;AACzD;AACA,SAASC,IAAIA,CAACC,MAAM,EAAE;EAClB,OAAOA,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEZ,KAAK,KAAKY,GAAG,GAAGZ,KAAK,EAAE,CAAC,CAAC,GAAGU,MAAM,CAACG,MAAM;AACxE;AACA,SAASC,SAASA,CAACJ,MAAM,EAAEK,WAAW,GAAGN,IAAI,EAAE;EAC3C,IAAIC,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO;MACHG,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE;IACT,CAAC;EACL;EACA,OAAO;IACHF,GAAG,EAAEG,IAAI,CAACH,GAAG,CAAC,GAAGN,MAAM,CAAC;IACxBO,GAAG,EAAEE,IAAI,CAACF,GAAG,CAAC,GAAGP,MAAM,CAAC;IACxBQ,GAAG,EAAEH,WAAW,CAACL,MAAM;EAC3B,CAAC;AACL;AACA,MAAMU,OAAO,GAAIC,EAAE,IAAKF,IAAI,CAACG,KAAK,CAAC,IAAI,GAAGD,EAAE,CAAC;AAC7C,SAASE,gBAAgBA,CAAA,EAAG;EACxB5B,WAAW,CAACK,KAAK,GAAG,IAAI;EACxBL,WAAW,CAAC6B,oBAAoB,GAAG,IAAI;AAC3C;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,MAAM;IAAEzB;EAAM,CAAC,GAAGL,WAAW;EAC7B,IAAI,CAACK,KAAK,EAAE;IACR,MAAM,IAAI0B,KAAK,CAAC,8BAA8B,CAAC;EACnD;EACAH,gBAAgB,CAAC,CAAC;EAClB1B,WAAW,CAACE,MAAM,CAAC;EACnB,MAAM4B,OAAO,GAAG;IACZ1B,SAAS,EAAE;MACP2B,KAAK,EAAEd,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC2B,KAAK,CAAC;MACvC1B,IAAI,EAAEY,SAAS,CAACd,KAAK,CAACC,SAAS,CAACC,IAAI,CAAC;MACrC2B,IAAI,EAAEf,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC4B,IAAI,CAAC;MACrCC,gBAAgB,EAAEhB,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC6B,gBAAgB,CAAC;MAC7DC,SAAS,EAAEjB,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC8B,SAAS,CAAC;MAC/CC,MAAM,EAAElB,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC+B,MAAM,CAAC;MACzCC,SAAS,EAAEnB,SAAS,CAACd,KAAK,CAACC,SAAS,CAACgC,SAAS,CAAC;MAC/CC,MAAM,EAAEpB,SAAS,CAACd,KAAK,CAACC,SAAS,CAACiC,MAAM,CAAC;MACzCC,UAAU,EAAErB,SAAS,CAACd,KAAK,CAACC,SAAS,CAACkC,UAAU;IACpD,CAAC;IACD9B,UAAU,EAAE;MACRC,UAAU,EAAEQ,SAAS,CAACd,KAAK,CAACK,UAAU,CAACC,UAAU,CAAC;MAClDC,KAAK,EAAEO,SAAS,CAACd,KAAK,CAACK,UAAU,CAACE,KAAK,CAAC;MACxCC,MAAM,EAAEM,SAAS,CAACd,KAAK,CAACK,UAAU,CAACG,MAAM;IAC7C,CAAC;IACD4B,gBAAgB,EAAE;MACdC,KAAK,EAAEvB,SAAS,CAACd,KAAK,CAACoC,gBAAgB,CAACC,KAAK,CAAC;MAC9CC,sBAAsB,EAAExB,SAAS,CAACd,KAAK,CAACoC,gBAAgB,CAACE,sBAAsB,CAAC;MAChFC,qBAAqB,EAAEzB,SAAS,CAACd,KAAK,CAACoC,gBAAgB,CAACG,qBAAqB;IACjF;EACJ,CAAC;EACD;AACJ;AACA;EACI,MAAM;IAAErC;EAAK,CAAC,GAAGyB,OAAO,CAAC1B,SAAS;EAClCC,IAAI,CAACc,GAAG,GAAGI,OAAO,CAAClB,IAAI,CAACc,GAAG,CAAC;EAC5Bd,IAAI,CAACe,GAAG,GAAGG,OAAO,CAAClB,IAAI,CAACe,GAAG,CAAC;EAC5Bf,IAAI,CAACgB,GAAG,GAAGE,OAAO,CAAClB,IAAI,CAACgB,GAAG,CAAC;EAC5B,CAAChB,IAAI,CAACc,GAAG,EAAEd,IAAI,CAACe,GAAG,CAAC,GAAG,CAACf,IAAI,CAACe,GAAG,EAAEf,IAAI,CAACc,GAAG,CAAC;EAC3C,OAAOW,OAAO;AAClB;AACA,SAASa,WAAWA,CAAA,EAAG;EACnB,IAAI7C,WAAW,CAACK,KAAK,EAAE;IACnBuB,gBAAgB,CAAC,CAAC;IAClB,MAAM,IAAIG,KAAK,CAAC,kCAAkC,CAAC;EACvD;EACA,MAAMe,cAAc,GAAG9C,WAAW;EAClC8C,cAAc,CAACzC,KAAK,GAAG;IACnBC,SAAS,EAAE;MACP2B,KAAK,EAAE,EAAE;MACT1B,IAAI,EAAE,EAAE;MACR2B,IAAI,EAAE,EAAE;MACRC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE;IAChB,CAAC;IACD9B,UAAU,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACZ,CAAC;IACD4B,gBAAgB,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,sBAAsB,EAAE,EAAE;MAC1BC,qBAAqB,EAAE;IAC3B;EACJ,CAAC;EACDE,cAAc,CAACjB,oBAAoB,GAAIkB,OAAO,IAAK;IAC/C,MAAM;MAAEN;IAAiB,CAAC,GAAGK,cAAc,CAACzC,KAAK;IACjDoC,gBAAgB,CAACC,KAAK,CAAClC,IAAI,CAACuC,OAAO,CAACL,KAAK,CAAC;IAC1CD,gBAAgB,CAACE,sBAAsB,CAACnC,IAAI,CAACuC,OAAO,CAACJ,sBAAsB,CAAC;IAC5EF,gBAAgB,CAACG,qBAAqB,CAACpC,IAAI,CAACuC,OAAO,CAACH,qBAAqB,CAAC;EAC9E,CAAC;EACD3C,KAAK,CAACuC,UAAU,CAACpC,MAAM,EAAE,IAAI,CAAC;EAC9B,OAAO0B,WAAW;AACtB;AAEA,SAASe,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}