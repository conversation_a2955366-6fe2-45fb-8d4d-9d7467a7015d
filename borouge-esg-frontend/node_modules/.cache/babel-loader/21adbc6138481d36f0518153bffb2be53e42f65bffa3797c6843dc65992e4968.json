{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\nconst LayoutGroupContext = createContext({});\nexport { LayoutGroupContext };", "map": {"version": 3, "names": ["createContext", "LayoutGroupContext"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst LayoutGroupContext = createContext({});\n\nexport { LayoutGroupContext };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,kBAAkB,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC;AAE5C,SAASC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}