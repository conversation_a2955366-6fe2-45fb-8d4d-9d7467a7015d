{"ast": null, "code": "import { isCSSVar } from './is-css-var.mjs';\nfunction setStyle(element, name, value) {\n  isCSSVar(name) ? element.style.setProperty(name, value) : element.style[name] = value;\n}\nexport { setStyle };", "map": {"version": 3, "names": ["isCSSVar", "setStyle", "element", "name", "value", "style", "setProperty"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/render/dom/style-set.mjs"], "sourcesContent": ["import { isCSSVar } from './is-css-var.mjs';\n\nfunction setStyle(element, name, value) {\n    isCSSVar(name)\n        ? element.style.setProperty(name, value)\n        : (element.style[name] = value);\n}\n\nexport { setStyle };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAE3C,SAASC,QAAQA,CAACC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACpCJ,QAAQ,CAACG,IAAI,CAAC,GACRD,OAAO,CAACG,KAAK,CAACC,WAAW,CAACH,IAAI,EAAEC,KAAK,CAAC,GACrCF,OAAO,CAACG,KAAK,CAACF,IAAI,CAAC,GAAGC,KAAM;AACvC;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}