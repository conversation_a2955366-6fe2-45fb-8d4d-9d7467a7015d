{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"19\",\n  r: \"3\",\n  key: \"1kj8tv\"\n}], [\"path\", {\n  d: \"M9 19h8.5c.4 0 .9-.1 1.3-.2\",\n  key: \"1effex\"\n}], [\"path\", {\n  d: \"M5.2 5.2A3.5 3.53 0 0 0 6.5 12H12\",\n  key: \"k9y2ds\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M21 15.3a3.5 3.5 0 0 0-3.3-3.3\",\n  key: \"11nlu2\"\n}], [\"path\", {\n  d: \"M15 5h-4.3\",\n  key: \"6537je\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"gq8acd\"\n}]];\nconst RouteOff = createLucideIcon(\"route-off\", __iconNode);\nexport { __iconNode, RouteOff as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "RouteOff", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/route-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '6', cy: '19', r: '3', key: '1kj8tv' }],\n  ['path', { d: 'M9 19h8.5c.4 0 .9-.1 1.3-.2', key: '1effex' }],\n  ['path', { d: 'M5.2 5.2A3.5 3.53 0 0 0 6.5 12H12', key: 'k9y2ds' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M21 15.3a3.5 3.5 0 0 0-3.3-3.3', key: '11nlu2' }],\n  ['path', { d: 'M15 5h-4.3', key: '6537je' }],\n  ['circle', { cx: '18', cy: '5', r: '3', key: 'gq8acd' }],\n];\n\n/**\n * @component @name RouteOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTkiIHI9IjMiIC8+CiAgPHBhdGggZD0iTTkgMTloOC41Yy40IDAgLjktLjEgMS4zLS4yIiAvPgogIDxwYXRoIGQ9Ik01LjIgNS4yQTMuNSAzLjUzIDAgMCAwIDYuNSAxMkgxMiIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgogIDxwYXRoIGQ9Ik0yMSAxNS4zYTMuNSAzLjUgMCAwIDAtMy4zLTMuMyIgLz4KICA8cGF0aCBkPSJNMTUgNWgtNC4zIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iNSIgcj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/route-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RouteOff = createLucideIcon('route-off', __iconNode);\n\nexport default RouteOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6BAA+B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,mCAAqC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,EACzD;AAaM,MAAAE,QAAA,GAAWC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}