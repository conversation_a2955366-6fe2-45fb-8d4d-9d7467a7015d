{"ast": null, "code": "function isGenerator(type) {\n  return typeof type === \"function\" && \"applyToOptions\" in type;\n}\nexport { isGenerator };", "map": {"version": 3, "names": ["isGenerator", "type"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs"], "sourcesContent": ["function isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\nexport { isGenerator };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACvB,OAAO,OAAOA,IAAI,KAAK,UAAU,IAAI,gBAAgB,IAAIA,IAAI;AACjE;AAEA,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}