{"ast": null, "code": "import { transformPropOrder, getValueAsType, numberValueTypes } from 'motion-dom';\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\",\n  transformPerspective: \"perspective\"\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n  // The transform string we're going to build into.\n  let transformString = \"\";\n  let transformIsDefault = true;\n  /**\n   * Loop over all possible transforms in order, adding the ones that\n   * are present to the transform string.\n   */\n  for (let i = 0; i < numTransforms; i++) {\n    const key = transformPropOrder[i];\n    const value = latestValues[key];\n    if (value === undefined) continue;\n    let valueIsDefault = true;\n    if (typeof value === \"number\") {\n      valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n    } else {\n      valueIsDefault = parseFloat(value) === 0;\n    }\n    if (!valueIsDefault || transformTemplate) {\n      const valueAsType = getValueAsType(value, numberValueTypes[key]);\n      if (!valueIsDefault) {\n        transformIsDefault = false;\n        const transformName = translateAlias[key] || key;\n        transformString += `${transformName}(${valueAsType}) `;\n      }\n      if (transformTemplate) {\n        transform[key] = valueAsType;\n      }\n    }\n  }\n  transformString = transformString.trim();\n  // If we have a custom `transform` template, pass our transform values and\n  // generated transformString to that before returning\n  if (transformTemplate) {\n    transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n  } else if (transformIsDefault) {\n    transformString = \"none\";\n  }\n  return transformString;\n}\nexport { buildTransform };", "map": {"version": 3, "names": ["transformPropOrder", "getValueAsType", "numberValueTypes", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "z", "transformPerspective", "numTransforms", "length", "buildTransform", "latestValues", "transform", "transformTemplate", "transformString", "transformIsDefault", "i", "key", "value", "undefined", "valueIsDefault", "startsWith", "parseFloat", "valueAsType", "transformName", "trim"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs"], "sourcesContent": ["import { transformPropOrder, getValueAsType, numberValueTypes } from 'motion-dom';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = transformPropOrder[i];\n        const value = latestValues[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault || transformTemplate) {\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (!valueIsDefault) {\n                transformIsDefault = false;\n                const transformName = translateAlias[key] || key;\n                transformString += `${transformName}(${valueAsType}) `;\n            }\n            if (transformTemplate) {\n                transform[key] = valueAsType;\n            }\n        }\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AAEjF,MAAMC,cAAc,GAAG;EACnBC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,aAAa,GAAGR,kBAAkB,CAACS,MAAM;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAE;EAChE;EACA,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,kBAAkB,GAAG,IAAI;EAC7B;AACJ;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,aAAa,EAAEQ,CAAC,EAAE,EAAE;IACpC,MAAMC,GAAG,GAAGjB,kBAAkB,CAACgB,CAAC,CAAC;IACjC,MAAME,KAAK,GAAGP,YAAY,CAACM,GAAG,CAAC;IAC/B,IAAIC,KAAK,KAAKC,SAAS,EACnB;IACJ,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAC3BE,cAAc,GAAGF,KAAK,MAAMD,GAAG,CAACI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MACI;MACDD,cAAc,GAAGE,UAAU,CAACJ,KAAK,CAAC,KAAK,CAAC;IAC5C;IACA,IAAI,CAACE,cAAc,IAAIP,iBAAiB,EAAE;MACtC,MAAMU,WAAW,GAAGtB,cAAc,CAACiB,KAAK,EAAEhB,gBAAgB,CAACe,GAAG,CAAC,CAAC;MAChE,IAAI,CAACG,cAAc,EAAE;QACjBL,kBAAkB,GAAG,KAAK;QAC1B,MAAMS,aAAa,GAAGrB,cAAc,CAACc,GAAG,CAAC,IAAIA,GAAG;QAChDH,eAAe,IAAI,GAAGU,aAAa,IAAID,WAAW,IAAI;MAC1D;MACA,IAAIV,iBAAiB,EAAE;QACnBD,SAAS,CAACK,GAAG,CAAC,GAAGM,WAAW;MAChC;IACJ;EACJ;EACAT,eAAe,GAAGA,eAAe,CAACW,IAAI,CAAC,CAAC;EACxC;EACA;EACA,IAAIZ,iBAAiB,EAAE;IACnBC,eAAe,GAAGD,iBAAiB,CAACD,SAAS,EAAEG,kBAAkB,GAAG,EAAE,GAAGD,eAAe,CAAC;EAC7F,CAAC,MACI,IAAIC,kBAAkB,EAAE;IACzBD,eAAe,GAAG,MAAM;EAC5B;EACA,OAAOA,eAAe;AAC1B;AAEA,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}