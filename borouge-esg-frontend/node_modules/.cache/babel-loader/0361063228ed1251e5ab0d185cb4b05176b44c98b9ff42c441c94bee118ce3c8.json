{"ast": null, "code": "import { optimizedAppearDataAttribute } from './data-id.mjs';\nfunction getOptimisedAppearId(visualElement) {\n  return visualElement.props[optimizedAppearDataAttribute];\n}\nexport { getOptimisedAppearId };", "map": {"version": 3, "names": ["optimizedAppearDataAttribute", "getOptimisedAppearId", "visualElement", "props"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs"], "sourcesContent": ["import { optimizedAppearDataAttribute } from './data-id.mjs';\n\nfunction getOptimisedAppearId(visualElement) {\n    return visualElement.props[optimizedAppearDataAttribute];\n}\n\nexport { getOptimisedAppearId };\n"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,eAAe;AAE5D,SAASC,oBAAoBA,CAACC,aAAa,EAAE;EACzC,OAAOA,aAAa,CAACC,KAAK,CAACH,4BAA4B,CAAC;AAC5D;AAEA,SAASC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}