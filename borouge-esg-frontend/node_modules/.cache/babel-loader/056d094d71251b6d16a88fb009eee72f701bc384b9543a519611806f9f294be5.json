{"ast": null, "code": "import { useConstant } from '../../utils/use-constant.mjs';\nimport { WillChangeMotionValue } from './WillChangeMotionValue.mjs';\nfunction useWillChange() {\n  return useConstant(() => new WillChangeMotionValue(\"auto\"));\n}\nexport { useWillChange };", "map": {"version": 3, "names": ["useConstant", "WillChangeMotionValue", "useWillChange"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/value/use-will-change/index.mjs"], "sourcesContent": ["import { useConstant } from '../../utils/use-constant.mjs';\nimport { WillChangeMotionValue } from './WillChangeMotionValue.mjs';\n\nfunction useWillChange() {\n    return useConstant(() => new WillChangeMotionValue(\"auto\"));\n}\n\nexport { useWillChange };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,qBAAqB,QAAQ,6BAA6B;AAEnE,SAASC,aAAaA,CAAA,EAAG;EACrB,OAAOF,WAAW,CAAC,MAAM,IAAIC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AAC/D;AAEA,SAASC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}