{"ast": null, "code": "\"use client\";\n\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useRef, useMemo } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { DeprecatedLayoutGroupContext } from '../../context/DeprecatedLayoutGroupContext.mjs';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { nodeGroup } from '../../projection/node/group.mjs';\nconst shouldInheritGroup = inherit => inherit === true;\nconst shouldInheritId = inherit => shouldInheritGroup(inherit === true) || inherit === \"id\";\nconst LayoutGroup = ({\n  children,\n  id,\n  inherit = true\n}) => {\n  const layoutGroupContext = useContext(LayoutGroupContext);\n  const deprecatedLayoutGroupContext = useContext(DeprecatedLayoutGroupContext);\n  const [forceRender, key] = useForceUpdate();\n  const context = useRef(null);\n  const upstreamId = layoutGroupContext.id || deprecatedLayoutGroupContext;\n  if (context.current === null) {\n    if (shouldInheritId(inherit) && upstreamId) {\n      id = id ? upstreamId + \"-\" + id : upstreamId;\n    }\n    context.current = {\n      id,\n      group: shouldInheritGroup(inherit) ? layoutGroupContext.group || nodeGroup() : nodeGroup()\n    };\n  }\n  const memoizedContext = useMemo(() => ({\n    ...context.current,\n    forceRender\n  }), [key]);\n  return jsx(LayoutGroupContext.Provider, {\n    value: memoizedContext,\n    children: children\n  });\n};\nexport { LayoutGroup };", "map": {"version": 3, "names": ["jsx", "useContext", "useRef", "useMemo", "LayoutGroupContext", "DeprecatedLayoutGroupContext", "useForceUpdate", "nodeGroup", "shouldInheritGroup", "inherit", "shouldInheritId", "LayoutGroup", "children", "id", "layoutGroupContext", "deprecatedLayoutGroupContext", "forceRender", "key", "context", "upstreamId", "current", "group", "memoizedContext", "Provider", "value"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/components/LayoutGroup/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useRef, useMemo } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { DeprecatedLayoutGroupContext } from '../../context/DeprecatedLayoutGroupContext.mjs';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { nodeGroup } from '../../projection/node/group.mjs';\n\nconst shouldInheritGroup = (inherit) => inherit === true;\nconst shouldInheritId = (inherit) => shouldInheritGroup(inherit === true) || inherit === \"id\";\nconst LayoutGroup = ({ children, id, inherit = true }) => {\n    const layoutGroupContext = useContext(LayoutGroupContext);\n    const deprecatedLayoutGroupContext = useContext(DeprecatedLayoutGroupContext);\n    const [forceRender, key] = useForceUpdate();\n    const context = useRef(null);\n    const upstreamId = layoutGroupContext.id || deprecatedLayoutGroupContext;\n    if (context.current === null) {\n        if (shouldInheritId(inherit) && upstreamId) {\n            id = id ? upstreamId + \"-\" + id : upstreamId;\n        }\n        context.current = {\n            id,\n            group: shouldInheritGroup(inherit)\n                ? layoutGroupContext.group || nodeGroup()\n                : nodeGroup(),\n        };\n    }\n    const memoizedContext = useMemo(() => ({ ...context.current, forceRender }), [key]);\n    return (jsx(LayoutGroupContext.Provider, { value: memoizedContext, children: children }));\n};\n\nexport { LayoutGroup };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,UAAU,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AACnD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,4BAA4B,QAAQ,gDAAgD;AAC7F,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,MAAMC,kBAAkB,GAAIC,OAAO,IAAKA,OAAO,KAAK,IAAI;AACxD,MAAMC,eAAe,GAAID,OAAO,IAAKD,kBAAkB,CAACC,OAAO,KAAK,IAAI,CAAC,IAAIA,OAAO,KAAK,IAAI;AAC7F,MAAME,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,EAAE;EAAEJ,OAAO,GAAG;AAAK,CAAC,KAAK;EACtD,MAAMK,kBAAkB,GAAGb,UAAU,CAACG,kBAAkB,CAAC;EACzD,MAAMW,4BAA4B,GAAGd,UAAU,CAACI,4BAA4B,CAAC;EAC7E,MAAM,CAACW,WAAW,EAAEC,GAAG,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC3C,MAAMY,OAAO,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMiB,UAAU,GAAGL,kBAAkB,CAACD,EAAE,IAAIE,4BAA4B;EACxE,IAAIG,OAAO,CAACE,OAAO,KAAK,IAAI,EAAE;IAC1B,IAAIV,eAAe,CAACD,OAAO,CAAC,IAAIU,UAAU,EAAE;MACxCN,EAAE,GAAGA,EAAE,GAAGM,UAAU,GAAG,GAAG,GAAGN,EAAE,GAAGM,UAAU;IAChD;IACAD,OAAO,CAACE,OAAO,GAAG;MACdP,EAAE;MACFQ,KAAK,EAAEb,kBAAkB,CAACC,OAAO,CAAC,GAC5BK,kBAAkB,CAACO,KAAK,IAAId,SAAS,CAAC,CAAC,GACvCA,SAAS,CAAC;IACpB,CAAC;EACL;EACA,MAAMe,eAAe,GAAGnB,OAAO,CAAC,OAAO;IAAE,GAAGe,OAAO,CAACE,OAAO;IAAEJ;EAAY,CAAC,CAAC,EAAE,CAACC,GAAG,CAAC,CAAC;EACnF,OAAQjB,GAAG,CAACI,kBAAkB,CAACmB,QAAQ,EAAE;IAAEC,KAAK,EAAEF,eAAe;IAAEV,QAAQ,EAAEA;EAAS,CAAC,CAAC;AAC5F,CAAC;AAED,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}