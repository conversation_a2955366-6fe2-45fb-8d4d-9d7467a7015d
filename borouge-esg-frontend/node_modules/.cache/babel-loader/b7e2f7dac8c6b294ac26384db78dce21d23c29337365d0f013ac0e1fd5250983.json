{"ast": null, "code": "import { startWaapiAnimation } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { optimizedAppearDataId } from './data-id.mjs';\nimport { getOptimisedAppearId } from './get-appear-id.mjs';\nimport { handoffOptimizedAppearAnimation } from './handoff.mjs';\nimport { appearAnimationStore, appearComplete } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\n/**\n * A single time to use across all animations to manually set startTime\n * and ensure they're all in sync.\n */\nlet startFrameTime;\n/**\n * A dummy animation to detect when Chrome is ready to start\n * painting the page and hold off from triggering the real animation\n * until then. We only need one animation to detect paint ready.\n *\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1406850\n */\nlet readyAnimation;\n/**\n * Keep track of animations that were suspended vs cancelled so we\n * can easily resume them when we're done measuring layout.\n */\nconst suspendedAnimations = new Set();\nfunction resumeSuspendedAnimations() {\n  suspendedAnimations.forEach(data => {\n    data.animation.play();\n    data.animation.startTime = data.startTime;\n  });\n  suspendedAnimations.clear();\n}\nfunction startOptimizedAppearAnimation(element, name, keyframes, options, onReady) {\n  // Prevent optimised appear animations if Motion has already started animating.\n  if (window.MotionIsMounted) {\n    return;\n  }\n  const id = element.dataset[optimizedAppearDataId];\n  if (!id) return;\n  window.MotionHandoffAnimation = handoffOptimizedAppearAnimation;\n  const storeId = appearStoreId(id, name);\n  if (!readyAnimation) {\n    readyAnimation = startWaapiAnimation(element, name, [keyframes[0], keyframes[0]],\n    /**\n     * 10 secs is basically just a super-safe duration to give Chrome\n     * long enough to get the animation ready.\n     */\n    {\n      duration: 10000,\n      ease: \"linear\"\n    });\n    appearAnimationStore.set(storeId, {\n      animation: readyAnimation,\n      startTime: null\n    });\n    /**\n     * If there's no readyAnimation then there's been no instantiation\n     * of handoff animations.\n     */\n    window.MotionHandoffAnimation = handoffOptimizedAppearAnimation;\n    window.MotionHasOptimisedAnimation = (elementId, valueName) => {\n      if (!elementId) return false;\n      /**\n       * Keep a map of elementIds that have started animating. We check\n       * via ID instead of Element because of hydration errors and\n       * pre-hydration checks. We also actively record IDs as they start\n       * animating rather than simply checking for data-appear-id as\n       * this attrbute might be present but not lead to an animation, for\n       * instance if the element's appear animation is on a different\n       * breakpoint.\n       */\n      if (!valueName) {\n        return appearComplete.has(elementId);\n      }\n      const animationId = appearStoreId(elementId, valueName);\n      return Boolean(appearAnimationStore.get(animationId));\n    };\n    window.MotionHandoffMarkAsComplete = elementId => {\n      if (appearComplete.has(elementId)) {\n        appearComplete.set(elementId, true);\n      }\n    };\n    window.MotionHandoffIsComplete = elementId => {\n      return appearComplete.get(elementId) === true;\n    };\n    /**\n     * We only need to cancel transform animations as\n     * they're the ones that will interfere with the\n     * layout animation measurements.\n     */\n    window.MotionCancelOptimisedAnimation = (elementId, valueName, frame, canResume) => {\n      const animationId = appearStoreId(elementId, valueName);\n      const data = appearAnimationStore.get(animationId);\n      if (!data) return;\n      if (frame && canResume === undefined) {\n        /**\n         * Wait until the end of the subsequent frame to cancel the animation\n         * to ensure we don't remove the animation before the main thread has\n         * had a chance to resolve keyframes and render.\n         */\n        frame.postRender(() => {\n          frame.postRender(() => {\n            data.animation.cancel();\n          });\n        });\n      } else {\n        data.animation.cancel();\n      }\n      if (frame && canResume) {\n        suspendedAnimations.add(data);\n        frame.render(resumeSuspendedAnimations);\n      } else {\n        appearAnimationStore.delete(animationId);\n        /**\n         * If there are no more animations left, we can remove the cancel function.\n         * This will let us know when we can stop checking for conflicting layout animations.\n         */\n        if (!appearAnimationStore.size) {\n          window.MotionCancelOptimisedAnimation = undefined;\n        }\n      }\n    };\n    window.MotionCheckAppearSync = (visualElement, valueName, value) => {\n      const appearId = getOptimisedAppearId(visualElement);\n      if (!appearId) return;\n      const valueIsOptimised = window.MotionHasOptimisedAnimation?.(appearId, valueName);\n      const externalAnimationValue = visualElement.props.values?.[valueName];\n      if (!valueIsOptimised || !externalAnimationValue) return;\n      const removeSyncCheck = value.on(\"change\", latestValue => {\n        if (externalAnimationValue.get() !== latestValue) {\n          window.MotionCancelOptimisedAnimation?.(appearId, valueName);\n          removeSyncCheck();\n        }\n      });\n      return removeSyncCheck;\n    };\n  }\n  const startAnimation = () => {\n    readyAnimation.cancel();\n    const appearAnimation = startWaapiAnimation(element, name, keyframes, options);\n    /**\n     * Record the time of the first started animation. We call performance.now() once\n     * here and once in handoff to ensure we're getting\n     * close to a frame-locked time. This keeps all animations in sync.\n     */\n    if (startFrameTime === undefined) {\n      startFrameTime = performance.now();\n    }\n    appearAnimation.startTime = startFrameTime;\n    appearAnimationStore.set(storeId, {\n      animation: appearAnimation,\n      startTime: startFrameTime\n    });\n    if (onReady) onReady(appearAnimation);\n  };\n  appearComplete.set(id, false);\n  if (readyAnimation.ready) {\n    readyAnimation.ready.then(startAnimation).catch(noop);\n  } else {\n    startAnimation();\n  }\n}\nexport { startOptimizedAppearAnimation };", "map": {"version": 3, "names": ["startWaapiAnimation", "noop", "optimizedAppearDataId", "getOptimisedAppearId", "handoffOptimizedAppearAnimation", "appearAnimationStore", "appearComplete", "appearStoreId", "startFrameTime", "readyAnimation", "suspendedAnimations", "Set", "resumeSuspendedAnimations", "for<PERSON>ach", "data", "animation", "play", "startTime", "clear", "startOptimizedAppearAnimation", "element", "name", "keyframes", "options", "onReady", "window", "MotionIsMounted", "id", "dataset", "MotionHandoffAnimation", "storeId", "duration", "ease", "set", "MotionHasOptimisedAnimation", "elementId", "valueName", "has", "animationId", "Boolean", "get", "MotionHandoffMarkAsComplete", "MotionHandoffIsComplete", "MotionCancelOptimisedAnimation", "frame", "canResume", "undefined", "postRender", "cancel", "add", "render", "delete", "size", "MotionCheckAppearSync", "visualElement", "value", "appearId", "valueIsOptimised", "externalAnimationValue", "props", "values", "removeSyncCheck", "on", "latestValue", "startAnimation", "appearAnimation", "performance", "now", "ready", "then", "catch"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/start.mjs"], "sourcesContent": ["import { startWaapiAnimation } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { optimizedAppearDataId } from './data-id.mjs';\nimport { getOptimisedAppearId } from './get-appear-id.mjs';\nimport { handoffOptimizedAppearAnimation } from './handoff.mjs';\nimport { appearAnimationStore, appearComplete } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\n/**\n * A single time to use across all animations to manually set startTime\n * and ensure they're all in sync.\n */\nlet startFrameTime;\n/**\n * A dummy animation to detect when Chrome is ready to start\n * painting the page and hold off from triggering the real animation\n * until then. We only need one animation to detect paint ready.\n *\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1406850\n */\nlet readyAnimation;\n/**\n * Keep track of animations that were suspended vs cancelled so we\n * can easily resume them when we're done measuring layout.\n */\nconst suspendedAnimations = new Set();\nfunction resumeSuspendedAnimations() {\n    suspendedAnimations.forEach((data) => {\n        data.animation.play();\n        data.animation.startTime = data.startTime;\n    });\n    suspendedAnimations.clear();\n}\nfunction startOptimizedAppearAnimation(element, name, keyframes, options, onReady) {\n    // Prevent optimised appear animations if Motion has already started animating.\n    if (window.MotionIsMounted) {\n        return;\n    }\n    const id = element.dataset[optimizedAppearDataId];\n    if (!id)\n        return;\n    window.MotionHandoffAnimation = handoffOptimizedAppearAnimation;\n    const storeId = appearStoreId(id, name);\n    if (!readyAnimation) {\n        readyAnimation = startWaapiAnimation(element, name, [keyframes[0], keyframes[0]], \n        /**\n         * 10 secs is basically just a super-safe duration to give Chrome\n         * long enough to get the animation ready.\n         */\n        { duration: 10000, ease: \"linear\" });\n        appearAnimationStore.set(storeId, {\n            animation: readyAnimation,\n            startTime: null,\n        });\n        /**\n         * If there's no readyAnimation then there's been no instantiation\n         * of handoff animations.\n         */\n        window.MotionHandoffAnimation = handoffOptimizedAppearAnimation;\n        window.MotionHasOptimisedAnimation = (elementId, valueName) => {\n            if (!elementId)\n                return false;\n            /**\n             * Keep a map of elementIds that have started animating. We check\n             * via ID instead of Element because of hydration errors and\n             * pre-hydration checks. We also actively record IDs as they start\n             * animating rather than simply checking for data-appear-id as\n             * this attrbute might be present but not lead to an animation, for\n             * instance if the element's appear animation is on a different\n             * breakpoint.\n             */\n            if (!valueName) {\n                return appearComplete.has(elementId);\n            }\n            const animationId = appearStoreId(elementId, valueName);\n            return Boolean(appearAnimationStore.get(animationId));\n        };\n        window.MotionHandoffMarkAsComplete = (elementId) => {\n            if (appearComplete.has(elementId)) {\n                appearComplete.set(elementId, true);\n            }\n        };\n        window.MotionHandoffIsComplete = (elementId) => {\n            return appearComplete.get(elementId) === true;\n        };\n        /**\n         * We only need to cancel transform animations as\n         * they're the ones that will interfere with the\n         * layout animation measurements.\n         */\n        window.MotionCancelOptimisedAnimation = (elementId, valueName, frame, canResume) => {\n            const animationId = appearStoreId(elementId, valueName);\n            const data = appearAnimationStore.get(animationId);\n            if (!data)\n                return;\n            if (frame && canResume === undefined) {\n                /**\n                 * Wait until the end of the subsequent frame to cancel the animation\n                 * to ensure we don't remove the animation before the main thread has\n                 * had a chance to resolve keyframes and render.\n                 */\n                frame.postRender(() => {\n                    frame.postRender(() => {\n                        data.animation.cancel();\n                    });\n                });\n            }\n            else {\n                data.animation.cancel();\n            }\n            if (frame && canResume) {\n                suspendedAnimations.add(data);\n                frame.render(resumeSuspendedAnimations);\n            }\n            else {\n                appearAnimationStore.delete(animationId);\n                /**\n                 * If there are no more animations left, we can remove the cancel function.\n                 * This will let us know when we can stop checking for conflicting layout animations.\n                 */\n                if (!appearAnimationStore.size) {\n                    window.MotionCancelOptimisedAnimation = undefined;\n                }\n            }\n        };\n        window.MotionCheckAppearSync = (visualElement, valueName, value) => {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (!appearId)\n                return;\n            const valueIsOptimised = window.MotionHasOptimisedAnimation?.(appearId, valueName);\n            const externalAnimationValue = visualElement.props.values?.[valueName];\n            if (!valueIsOptimised || !externalAnimationValue)\n                return;\n            const removeSyncCheck = value.on(\"change\", (latestValue) => {\n                if (externalAnimationValue.get() !== latestValue) {\n                    window.MotionCancelOptimisedAnimation?.(appearId, valueName);\n                    removeSyncCheck();\n                }\n            });\n            return removeSyncCheck;\n        };\n    }\n    const startAnimation = () => {\n        readyAnimation.cancel();\n        const appearAnimation = startWaapiAnimation(element, name, keyframes, options);\n        /**\n         * Record the time of the first started animation. We call performance.now() once\n         * here and once in handoff to ensure we're getting\n         * close to a frame-locked time. This keeps all animations in sync.\n         */\n        if (startFrameTime === undefined) {\n            startFrameTime = performance.now();\n        }\n        appearAnimation.startTime = startFrameTime;\n        appearAnimationStore.set(storeId, {\n            animation: appearAnimation,\n            startTime: startFrameTime,\n        });\n        if (onReady)\n            onReady(appearAnimation);\n    };\n    appearComplete.set(id, false);\n    if (readyAnimation.ready) {\n        readyAnimation.ready.then(startAnimation).catch(noop);\n    }\n    else {\n        startAnimation();\n    }\n}\n\nexport { startOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,YAAY;AAChD,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,+BAA+B,QAAQ,eAAe;AAC/D,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,aAAa;AAClE,SAASC,aAAa,QAAQ,gBAAgB;;AAE9C;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrC,SAASC,yBAAyBA,CAAA,EAAG;EACjCF,mBAAmB,CAACG,OAAO,CAAEC,IAAI,IAAK;IAClCA,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,CAAC;IACrBF,IAAI,CAACC,SAAS,CAACE,SAAS,GAAGH,IAAI,CAACG,SAAS;EAC7C,CAAC,CAAC;EACFP,mBAAmB,CAACQ,KAAK,CAAC,CAAC;AAC/B;AACA,SAASC,6BAA6BA,CAACC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/E;EACA,IAAIC,MAAM,CAACC,eAAe,EAAE;IACxB;EACJ;EACA,MAAMC,EAAE,GAAGP,OAAO,CAACQ,OAAO,CAAC1B,qBAAqB,CAAC;EACjD,IAAI,CAACyB,EAAE,EACH;EACJF,MAAM,CAACI,sBAAsB,GAAGzB,+BAA+B;EAC/D,MAAM0B,OAAO,GAAGvB,aAAa,CAACoB,EAAE,EAAEN,IAAI,CAAC;EACvC,IAAI,CAACZ,cAAc,EAAE;IACjBA,cAAc,GAAGT,mBAAmB,CAACoB,OAAO,EAAEC,IAAI,EAAE,CAACC,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;IAChF;AACR;AACA;AACA;IACQ;MAAES,QAAQ,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAS,CAAC,CAAC;IACpC3B,oBAAoB,CAAC4B,GAAG,CAACH,OAAO,EAAE;MAC9Bf,SAAS,EAAEN,cAAc;MACzBQ,SAAS,EAAE;IACf,CAAC,CAAC;IACF;AACR;AACA;AACA;IACQQ,MAAM,CAACI,sBAAsB,GAAGzB,+BAA+B;IAC/DqB,MAAM,CAACS,2BAA2B,GAAG,CAACC,SAAS,EAAEC,SAAS,KAAK;MAC3D,IAAI,CAACD,SAAS,EACV,OAAO,KAAK;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,EAAE;QACZ,OAAO9B,cAAc,CAAC+B,GAAG,CAACF,SAAS,CAAC;MACxC;MACA,MAAMG,WAAW,GAAG/B,aAAa,CAAC4B,SAAS,EAAEC,SAAS,CAAC;MACvD,OAAOG,OAAO,CAAClC,oBAAoB,CAACmC,GAAG,CAACF,WAAW,CAAC,CAAC;IACzD,CAAC;IACDb,MAAM,CAACgB,2BAA2B,GAAIN,SAAS,IAAK;MAChD,IAAI7B,cAAc,CAAC+B,GAAG,CAACF,SAAS,CAAC,EAAE;QAC/B7B,cAAc,CAAC2B,GAAG,CAACE,SAAS,EAAE,IAAI,CAAC;MACvC;IACJ,CAAC;IACDV,MAAM,CAACiB,uBAAuB,GAAIP,SAAS,IAAK;MAC5C,OAAO7B,cAAc,CAACkC,GAAG,CAACL,SAAS,CAAC,KAAK,IAAI;IACjD,CAAC;IACD;AACR;AACA;AACA;AACA;IACQV,MAAM,CAACkB,8BAA8B,GAAG,CAACR,SAAS,EAAEC,SAAS,EAAEQ,KAAK,EAAEC,SAAS,KAAK;MAChF,MAAMP,WAAW,GAAG/B,aAAa,CAAC4B,SAAS,EAAEC,SAAS,CAAC;MACvD,MAAMtB,IAAI,GAAGT,oBAAoB,CAACmC,GAAG,CAACF,WAAW,CAAC;MAClD,IAAI,CAACxB,IAAI,EACL;MACJ,IAAI8B,KAAK,IAAIC,SAAS,KAAKC,SAAS,EAAE;QAClC;AAChB;AACA;AACA;AACA;QACgBF,KAAK,CAACG,UAAU,CAAC,MAAM;UACnBH,KAAK,CAACG,UAAU,CAAC,MAAM;YACnBjC,IAAI,CAACC,SAAS,CAACiC,MAAM,CAAC,CAAC;UAC3B,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACDlC,IAAI,CAACC,SAAS,CAACiC,MAAM,CAAC,CAAC;MAC3B;MACA,IAAIJ,KAAK,IAAIC,SAAS,EAAE;QACpBnC,mBAAmB,CAACuC,GAAG,CAACnC,IAAI,CAAC;QAC7B8B,KAAK,CAACM,MAAM,CAACtC,yBAAyB,CAAC;MAC3C,CAAC,MACI;QACDP,oBAAoB,CAAC8C,MAAM,CAACb,WAAW,CAAC;QACxC;AAChB;AACA;AACA;QACgB,IAAI,CAACjC,oBAAoB,CAAC+C,IAAI,EAAE;UAC5B3B,MAAM,CAACkB,8BAA8B,GAAGG,SAAS;QACrD;MACJ;IACJ,CAAC;IACDrB,MAAM,CAAC4B,qBAAqB,GAAG,CAACC,aAAa,EAAElB,SAAS,EAAEmB,KAAK,KAAK;MAChE,MAAMC,QAAQ,GAAGrD,oBAAoB,CAACmD,aAAa,CAAC;MACpD,IAAI,CAACE,QAAQ,EACT;MACJ,MAAMC,gBAAgB,GAAGhC,MAAM,CAACS,2BAA2B,GAAGsB,QAAQ,EAAEpB,SAAS,CAAC;MAClF,MAAMsB,sBAAsB,GAAGJ,aAAa,CAACK,KAAK,CAACC,MAAM,GAAGxB,SAAS,CAAC;MACtE,IAAI,CAACqB,gBAAgB,IAAI,CAACC,sBAAsB,EAC5C;MACJ,MAAMG,eAAe,GAAGN,KAAK,CAACO,EAAE,CAAC,QAAQ,EAAGC,WAAW,IAAK;QACxD,IAAIL,sBAAsB,CAAClB,GAAG,CAAC,CAAC,KAAKuB,WAAW,EAAE;UAC9CtC,MAAM,CAACkB,8BAA8B,GAAGa,QAAQ,EAAEpB,SAAS,CAAC;UAC5DyB,eAAe,CAAC,CAAC;QACrB;MACJ,CAAC,CAAC;MACF,OAAOA,eAAe;IAC1B,CAAC;EACL;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IACzBvD,cAAc,CAACuC,MAAM,CAAC,CAAC;IACvB,MAAMiB,eAAe,GAAGjE,mBAAmB,CAACoB,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;IAC9E;AACR;AACA;AACA;AACA;IACQ,IAAIf,cAAc,KAAKsC,SAAS,EAAE;MAC9BtC,cAAc,GAAG0D,WAAW,CAACC,GAAG,CAAC,CAAC;IACtC;IACAF,eAAe,CAAChD,SAAS,GAAGT,cAAc;IAC1CH,oBAAoB,CAAC4B,GAAG,CAACH,OAAO,EAAE;MAC9Bf,SAAS,EAAEkD,eAAe;MAC1BhD,SAAS,EAAET;IACf,CAAC,CAAC;IACF,IAAIgB,OAAO,EACPA,OAAO,CAACyC,eAAe,CAAC;EAChC,CAAC;EACD3D,cAAc,CAAC2B,GAAG,CAACN,EAAE,EAAE,KAAK,CAAC;EAC7B,IAAIlB,cAAc,CAAC2D,KAAK,EAAE;IACtB3D,cAAc,CAAC2D,KAAK,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,KAAK,CAACrE,IAAI,CAAC;EACzD,CAAC,MACI;IACD+D,cAAc,CAAC,CAAC;EACpB;AACJ;AAEA,SAAS7C,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}