{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 16H4a2 2 0 1 1 0-4h16a2 2 0 1 1 0 4h-4.25\",\n  key: \"5dloqd\"\n}], [\"path\", {\n  d: \"M5 12a2 2 0 0 1-2-2 9 7 0 0 1 18 0 2 2 0 0 1-2 2\",\n  key: \"1vl3my\"\n}], [\"path\", {\n  d: \"M5 16a2 2 0 0 0-2 2 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 2 2 0 0 0-2-2q0 0 0 0\",\n  key: \"1us75o\"\n}], [\"path\", {\n  d: \"m6.67 12 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2\",\n  key: \"qqzweh\"\n}]];\nconst Hamburger = createLucideIcon(\"hamburger\", __iconNode);\nexport { __iconNode, Hamburger as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON>", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/hamburger.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 16H4a2 2 0 1 1 0-4h16a2 2 0 1 1 0 4h-4.25', key: '5dloqd' }],\n  ['path', { d: 'M5 12a2 2 0 0 1-2-2 9 7 0 0 1 18 0 2 2 0 0 1-2 2', key: '1vl3my' }],\n  [\n    'path',\n    {\n      d: 'M5 16a2 2 0 0 0-2 2 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 2 2 0 0 0-2-2q0 0 0 0',\n      key: '1us75o',\n    },\n  ],\n  ['path', { d: 'm6.67 12 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2', key: 'qqzweh' }],\n];\n\n/**\n * @component @name Hamburger\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTZINGEyIDIgMCAxIDEgMC00aDE2YTIgMiAwIDEgMSAwIDRoLTQuMjUiIC8+CiAgPHBhdGggZD0iTTUgMTJhMiAyIDAgMCAxLTItMiA5IDcgMCAwIDEgMTggMCAyIDIgMCAwIDEtMiAyIiAvPgogIDxwYXRoIGQ9Ik01IDE2YTIgMiAwIDAgMC0yIDIgMyAzIDAgMCAwIDMgM2gxMmEzIDMgMCAwIDAgMy0zIDIgMiAwIDAgMC0yLTJxMCAwIDAgMCIgLz4KICA8cGF0aCBkPSJtNi42NyAxMiA2LjEzIDQuNmEyIDIgMCAwIDAgMi44LS40bDMuMTUtNC4yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/hamburger\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Hamburger = createLucideIcon('hamburger', __iconNode);\n\nexport default Hamburger;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,kDAAoD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjF,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9E;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}