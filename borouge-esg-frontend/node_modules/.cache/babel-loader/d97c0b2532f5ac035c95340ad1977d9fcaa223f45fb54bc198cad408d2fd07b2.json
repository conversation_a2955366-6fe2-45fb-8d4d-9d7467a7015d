{"ast": null, "code": "import { getMixer } from './complex.mjs';\nimport { mixNumber } from './number.mjs';\nfunction mix(from, to, p) {\n  if (typeof from === \"number\" && typeof to === \"number\" && typeof p === \"number\") {\n    return mixNumber(from, to, p);\n  }\n  const mixer = getMixer(from);\n  return mixer(from, to);\n}\nexport { mix };", "map": {"version": 3, "names": ["getMixer", "mixNumber", "mix", "from", "to", "p", "mixer"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/utils/mix/index.mjs"], "sourcesContent": ["import { getMixer } from './complex.mjs';\nimport { mixNumber } from './number.mjs';\n\nfunction mix(from, to, p) {\n    if (typeof from === \"number\" &&\n        typeof to === \"number\" &&\n        typeof p === \"number\") {\n        return mixNumber(from, to, p);\n    }\n    const mixer = getMixer(from);\n    return mixer(from, to);\n}\n\nexport { mix };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,SAAS,QAAQ,cAAc;AAExC,SAASC,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,CAAC,EAAE;EACtB,IAAI,OAAOF,IAAI,KAAK,QAAQ,IACxB,OAAOC,EAAE,KAAK,QAAQ,IACtB,OAAOC,CAAC,KAAK,QAAQ,EAAE;IACvB,OAAOJ,SAAS,CAACE,IAAI,EAAEC,EAAE,EAAEC,CAAC,CAAC;EACjC;EACA,MAAMC,KAAK,GAAGN,QAAQ,CAACG,IAAI,CAAC;EAC5B,OAAOG,KAAK,CAACH,IAAI,EAAEC,EAAE,CAAC;AAC1B;AAEA,SAASF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}