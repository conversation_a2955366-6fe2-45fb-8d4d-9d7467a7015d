{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"10\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"e45bow\"\n}], [\"path\", {\n  d: \"M10.3 15H7a4 4 0 0 0-4 4v2\",\n  key: \"3bnktk\"\n}], [\"path\", {\n  d: \"M15 15.5V14a2 2 0 0 1 4 0v1.5\",\n  key: \"12ym5i\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"13\",\n  y: \"16\",\n  rx: \".899\",\n  key: \"4p176n\"\n}]];\nconst UserLock = createLucideIcon(\"user-lock\", __iconNode);\nexport { __iconNode, UserLock as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "width", "height", "x", "y", "rx", "UserLock", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/user-lock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '10', cy: '7', r: '4', key: 'e45bow' }],\n  ['path', { d: 'M10.3 15H7a4 4 0 0 0-4 4v2', key: '3bnktk' }],\n  ['path', { d: 'M15 15.5V14a2 2 0 0 1 4 0v1.5', key: '12ym5i' }],\n  ['rect', { width: '8', height: '5', x: '13', y: '16', rx: '.899', key: '4p176n' }],\n];\n\n/**\n * @component @name UserLock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjciIHI9IjQiIC8+CiAgPHBhdGggZD0iTTEwLjMgMTVIN2E0IDQgMCAwIDAtNCA0djIiIC8+CiAgPHBhdGggZD0iTTE1IDE1LjVWMTRhMiAyIDAgMCAxIDQgMHYxLjUiIC8+CiAgPHJlY3Qgd2lkdGg9IjgiIGhlaWdodD0iNSIgeD0iMTMiIHk9IjE2IiByeD0iLjg5OSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserLock = createLucideIcon('user-lock', __iconNode);\n\nexport default UserLock;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,4BAA8B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAEC,CAAA,EAAG,+BAAiC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEE,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,MAAQ;EAAAN,GAAA,EAAK;AAAA,CAAU,EACnF;AAaM,MAAAO,QAAA,GAAWC,gBAAiB,cAAaZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}