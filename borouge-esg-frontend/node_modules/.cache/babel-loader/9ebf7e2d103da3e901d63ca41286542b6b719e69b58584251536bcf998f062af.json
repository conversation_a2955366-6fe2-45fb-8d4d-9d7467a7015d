{"ast": null, "code": "import { GroupAnimationWithThen } from 'motion-dom';\nimport { animateSequence } from './sequence.mjs';\nimport { animateSubject } from './subject.mjs';\nfunction isSequence(value) {\n  return Array.isArray(value) && value.some(Array.isArray);\n}\n/**\n * Creates an animation function that is optionally scoped\n * to a specific element.\n */\nfunction createScopedAnimate(scope) {\n  /**\n   * Implementation\n   */\n  function scopedAnimate(subjectOrSequence, optionsOrKeyframes, options) {\n    let animations = [];\n    if (isSequence(subjectOrSequence)) {\n      animations = animateSequence(subjectOrSequence, optionsOrKeyframes, scope);\n    } else {\n      animations = animateSubject(subjectOrSequence, optionsOrKeyframes, options, scope);\n    }\n    const animation = new GroupAnimationWithThen(animations);\n    if (scope) {\n      scope.animations.push(animation);\n    }\n    return animation;\n  }\n  return scopedAnimate;\n}\nconst animate = createScopedAnimate();\nexport { animate, createScopedAnimate };", "map": {"version": 3, "names": ["GroupAnimationWithThen", "animateSequence", "animateSubject", "isSequence", "value", "Array", "isArray", "some", "createScopedAnimate", "scope", "scopedAnimate", "subjectOrSequence", "optionsOrKeyframes", "options", "animations", "animation", "push", "animate"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/animation/animate/index.mjs"], "sourcesContent": ["import { GroupAnimationWithThen } from 'motion-dom';\nimport { animateSequence } from './sequence.mjs';\nimport { animateSubject } from './subject.mjs';\n\nfunction isSequence(value) {\n    return Array.isArray(value) && value.some(Array.isArray);\n}\n/**\n * Creates an animation function that is optionally scoped\n * to a specific element.\n */\nfunction createScopedAnimate(scope) {\n    /**\n     * Implementation\n     */\n    function scopedAnimate(subjectOrSequence, optionsOrKeyframes, options) {\n        let animations = [];\n        if (isSequence(subjectOrSequence)) {\n            animations = animateSequence(subjectOrSequence, optionsOrKeyframes, scope);\n        }\n        else {\n            animations = animateSubject(subjectOrSequence, optionsOrKeyframes, options, scope);\n        }\n        const animation = new GroupAnimationWithThen(animations);\n        if (scope) {\n            scope.animations.push(animation);\n        }\n        return animation;\n    }\n    return scopedAnimate;\n}\nconst animate = createScopedAnimate();\n\nexport { animate, createScopedAnimate };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,YAAY;AACnD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,cAAc,QAAQ,eAAe;AAE9C,SAASC,UAAUA,CAACC,KAAK,EAAE;EACvB,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,IAAI,CAACF,KAAK,CAACC,OAAO,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACC,KAAK,EAAE;EAChC;AACJ;AACA;EACI,SAASC,aAAaA,CAACC,iBAAiB,EAAEC,kBAAkB,EAAEC,OAAO,EAAE;IACnE,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIX,UAAU,CAACQ,iBAAiB,CAAC,EAAE;MAC/BG,UAAU,GAAGb,eAAe,CAACU,iBAAiB,EAAEC,kBAAkB,EAAEH,KAAK,CAAC;IAC9E,CAAC,MACI;MACDK,UAAU,GAAGZ,cAAc,CAACS,iBAAiB,EAAEC,kBAAkB,EAAEC,OAAO,EAAEJ,KAAK,CAAC;IACtF;IACA,MAAMM,SAAS,GAAG,IAAIf,sBAAsB,CAACc,UAAU,CAAC;IACxD,IAAIL,KAAK,EAAE;MACPA,KAAK,CAACK,UAAU,CAACE,IAAI,CAACD,SAAS,CAAC;IACpC;IACA,OAAOA,SAAS;EACpB;EACA,OAAOL,aAAa;AACxB;AACA,MAAMO,OAAO,GAAGT,mBAAmB,CAAC,CAAC;AAErC,SAASS,OAAO,EAAET,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}