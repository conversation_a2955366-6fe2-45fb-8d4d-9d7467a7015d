{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, ChevronDown, MessageSquare, Users, Bookmark, Menu, X } from 'lucide-react';\nimport ConversationView from './components/ConversationView';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [conversations, setConversations] = useState([]);\n  const [currentView, setCurrentView] = useState('search'); // 'search' or 'conversation'\n  const [activeQuery, setActiveQuery] = useState('');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // ESG-focused suggestion chips for Borouge\n  const suggestionChips = ['EU plastic regulations 2024', 'CBAM carbon border adjustment', 'Circular economy packaging', 'SABIC sustainability strategy', 'Petrochemical market trends', 'ESG reporting requirements', 'Renewable feedstock adoption', 'Carbon footprint reduction'];\n  const handleSearch = query => {\n    if (query.trim()) {\n      console.log('Searching for:', query);\n      // Add to conversations\n      const newConversation = {\n        id: Date.now(),\n        query: query,\n        timestamp: new Date()\n      };\n      setConversations([newConversation, ...conversations]);\n      setSearchQuery('');\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSearch(searchQuery);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: \"B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Borouge ESG\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"new-search-btn\",\n        children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), \"Start new search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Users, {\n              className: \"nav-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), \"All Intelligence\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-count\",\n            children: conversations.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Bookmark, {\n              className: \"nav-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), \"Saved\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-count\",\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recent-chats\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Recent Chats\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), conversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-chats\",\n          children: \"No saved chats yet.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: conversations.slice(0, 5).map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '8px 0',\n              fontSize: '14px',\n              color: '#6b7280',\n              cursor: 'pointer',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              whiteSpace: 'nowrap'\n            },\n            children: conversation.query\n          }, conversation.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"The ESG Intelligence Engine for\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subtitle\",\n          children: [\"Petrochemicals\", /*#__PURE__*/_jsxDEV(ChevronDown, {\n            className: \"dropdown-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-box\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"search-input\",\n            placeholder: \"EU plastic regulations impact on Borouge operations\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            onKeyPress: handleKeyPress\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"research-badge\",\n              children: \"INTELLIGENCE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"search-btn\",\n              onClick: () => handleSearch(searchQuery),\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"search-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"suggestions\",\n        children: suggestionChips.map((chip, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"suggestion-chip\",\n          onClick: () => handleSearch(chip),\n          children: chip\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"/SpknX6vu08HVWq1maOnMSaYUI0=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Search", "ChevronDown", "MessageSquare", "Users", "Bookmark", "<PERSON><PERSON>", "X", "ConversationView", "jsxDEV", "_jsxDEV", "App", "_s", "searchQuery", "setSearch<PERSON>uery", "conversations", "setConversations", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "activeQuery", "setActiveQuery", "sidebarOpen", "setSidebarOpen", "suggestionChips", "handleSearch", "query", "trim", "console", "log", "newConversation", "id", "Date", "now", "timestamp", "handleKeyPress", "e", "key", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "style", "display", "alignItems", "length", "slice", "map", "conversation", "padding", "fontSize", "color", "cursor", "overflow", "textOverflow", "whiteSpace", "type", "placeholder", "value", "onChange", "target", "onKeyPress", "onClick", "chip", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, ChevronDown, MessageSquare, Users, Bookmark, Menu, X } from 'lucide-react';\nimport ConversationView from './components/ConversationView';\nimport './App.css';\n\nfunction App() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [conversations, setConversations] = useState([]);\n  const [currentView, setCurrentView] = useState('search'); // 'search' or 'conversation'\n  const [activeQuery, setActiveQuery] = useState('');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // ESG-focused suggestion chips for Borouge\n  const suggestionChips = [\n    'EU plastic regulations 2024',\n    'CBAM carbon border adjustment',\n    'Circular economy packaging',\n    'SABIC sustainability strategy',\n    'Petrochemical market trends',\n    'ESG reporting requirements',\n    'Renewable feedstock adoption',\n    'Carbon footprint reduction'\n  ];\n\n  const handleSearch = (query) => {\n    if (query.trim()) {\n      console.log('Searching for:', query);\n      // Add to conversations\n      const newConversation = {\n        id: Date.now(),\n        query: query,\n        timestamp: new Date()\n      };\n      setConversations([newConversation, ...conversations]);\n      setSearchQuery('');\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      handleSearch(searchQuery);\n    }\n  };\n\n  return (\n    <div className=\"app\">\n      {/* Sidebar */}\n      <div className=\"sidebar\">\n        {/* Logo */}\n        <div className=\"logo\">\n          <div className=\"logo-icon\">B</div>\n          <span className=\"logo-text\">Borouge ESG</span>\n        </div>\n\n        {/* New Search Button */}\n        <button className=\"new-search-btn\">\n          <MessageSquare size={16} />\n          Start new search\n        </button>\n\n        {/* Navigation */}\n        <div className=\"nav-section\">\n          <div className=\"nav-item\">\n            <div style={{ display: 'flex', alignItems: 'center' }}>\n              <Users className=\"nav-icon\" />\n              All Intelligence\n            </div>\n            <span className=\"nav-count\">{conversations.length}</span>\n          </div>\n          <div className=\"nav-item\">\n            <div style={{ display: 'flex', alignItems: 'center' }}>\n              <Bookmark className=\"nav-icon\" />\n              Saved\n            </div>\n            <span className=\"nav-count\">0</span>\n          </div>\n        </div>\n\n        {/* Recent Chats */}\n        <div className=\"recent-chats\">\n          <h3>Recent Chats</h3>\n          {conversations.length === 0 ? (\n            <div className=\"no-chats\">No saved chats yet.</div>\n          ) : (\n            <div>\n              {conversations.slice(0, 5).map((conversation) => (\n                <div key={conversation.id} style={{\n                  padding: '8px 0',\n                  fontSize: '14px',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}>\n                  {conversation.query}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"main-content\">\n        {/* Header */}\n        <div className=\"header\">\n          <h1 className=\"title\">The ESG Intelligence Engine for</h1>\n          <div className=\"subtitle\">\n            Petrochemicals\n            <ChevronDown className=\"dropdown-icon\" />\n          </div>\n        </div>\n\n        {/* Search Container */}\n        <div className=\"search-container\">\n          <div className=\"search-box\">\n            <input\n              type=\"text\"\n              className=\"search-input\"\n              placeholder=\"EU plastic regulations impact on Borouge operations\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              onKeyPress={handleKeyPress}\n            />\n            <div className=\"search-controls\">\n              <span className=\"research-badge\">INTELLIGENCE</span>\n              <button\n                className=\"search-btn\"\n                onClick={() => handleSearch(searchQuery)}\n              >\n                <Search className=\"search-icon\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Suggestion Chips */}\n        <div className=\"suggestions\">\n          {suggestionChips.map((chip, index) => (\n            <button\n              key={index}\n              className=\"suggestion-chip\"\n              onClick={() => handleSearch(chip)}\n            >\n              {chip}\n            </button>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAC3F,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMyB,eAAe,GAAG,CACtB,6BAA6B,EAC7B,+BAA+B,EAC/B,4BAA4B,EAC5B,+BAA+B,EAC/B,6BAA6B,EAC7B,4BAA4B,EAC5B,8BAA8B,EAC9B,4BAA4B,CAC7B;EAED,MAAMC,YAAY,GAAIC,KAAK,IAAK;IAC9B,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MAChBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,KAAK,CAAC;MACpC;MACA,MAAMI,eAAe,GAAG;QACtBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdP,KAAK,EAAEA,KAAK;QACZQ,SAAS,EAAE,IAAIF,IAAI,CAAC;MACtB,CAAC;MACDf,gBAAgB,CAAC,CAACa,eAAe,EAAE,GAAGd,aAAa,CAAC,CAAC;MACrDD,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;EAED,MAAMoB,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBZ,YAAY,CAACX,WAAW,CAAC;IAC3B;EACF,CAAC;EAED,oBACEH,OAAA;IAAK2B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElB5B,OAAA;MAAK2B,SAAS,EAAC,SAAS;MAAAC,QAAA,gBAEtB5B,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB5B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClChC,OAAA;UAAM2B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGNhC,OAAA;QAAQ2B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAChC5B,OAAA,CAACP,aAAa;UAACwC,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGThC,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5B,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAKkC,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAR,QAAA,gBACpD5B,OAAA,CAACN,KAAK;cAACiC,SAAS,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNhC,OAAA;YAAM2B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEvB,aAAa,CAACgC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAKkC,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAR,QAAA,gBACpD5B,OAAA,CAACL,QAAQ;cAACgC,SAAS,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNhC,OAAA;YAAM2B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAA4B,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACpB3B,aAAa,CAACgC,MAAM,KAAK,CAAC,gBACzBrC,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEnDhC,OAAA;UAAA4B,QAAA,EACGvB,aAAa,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,YAAY,iBAC1CxC,OAAA;YAA2BkC,KAAK,EAAE;cAChCO,OAAO,EAAE,OAAO;cAChBC,QAAQ,EAAE,MAAM;cAChBC,KAAK,EAAE,SAAS;cAChBC,MAAM,EAAE,SAAS;cACjBC,QAAQ,EAAE,QAAQ;cAClBC,YAAY,EAAE,UAAU;cACxBC,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EACCY,YAAY,CAACzB;UAAK,GATXyB,YAAY,CAACpB,EAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUpB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA;MAAK2B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3B5B,OAAA;QAAK2B,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB5B,OAAA;UAAI2B,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DhC,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,GAAC,gBAExB,eAAA5B,OAAA,CAACR,WAAW;YAACmC,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B5B,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5B,OAAA;YACEgD,IAAI,EAAC,MAAM;YACXrB,SAAS,EAAC,cAAc;YACxBsB,WAAW,EAAC,qDAAqD;YACjEC,KAAK,EAAE/C,WAAY;YACnBgD,QAAQ,EAAG1B,CAAC,IAAKrB,cAAc,CAACqB,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;YAChDG,UAAU,EAAE7B;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFhC,OAAA;YAAK2B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B5B,OAAA;cAAM2B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDhC,OAAA;cACE2B,SAAS,EAAC,YAAY;cACtB2B,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAACX,WAAW,CAAE;cAAAyB,QAAA,eAEzC5B,OAAA,CAACT,MAAM;gBAACoC,SAAS,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBf,eAAe,CAAC0B,GAAG,CAAC,CAACgB,IAAI,EAAEC,KAAK,kBAC/BxD,OAAA;UAEE2B,SAAS,EAAC,iBAAiB;UAC3B2B,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAACyC,IAAI,CAAE;UAAA3B,QAAA,EAEjC2B;QAAI,GAJAC,KAAK;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9B,EAAA,CAnJQD,GAAG;AAAAwD,EAAA,GAAHxD,GAAG;AAqJZ,eAAeA,GAAG;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}