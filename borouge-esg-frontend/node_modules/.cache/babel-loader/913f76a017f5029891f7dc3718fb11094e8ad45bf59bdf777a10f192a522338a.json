{"ast": null, "code": "import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\nfunction isWaapiSupportedEasing(easing) {\n  return Boolean(typeof easing === \"function\" && supportsLinearEasing() || !easing || typeof easing === \"string\" && (easing in supportedWaapiEasing || supportsLinearEasing()) || isBezierDefinition(easing) || Array.isArray(easing) && easing.every(isWaapiSupportedEasing));\n}\nexport { isWaapiSupportedEasing };", "map": {"version": 3, "names": ["isBezierDefinition", "supportsLinearEasing", "supportedWaapiEasing", "isWaapiSupportedEasing", "easing", "Boolean", "Array", "isArray", "every"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/animation/waapi/easing/is-supported.mjs"], "sourcesContent": ["import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && supportsLinearEasing()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in supportedWaapiEasing || supportsLinearEasing())) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\n\nexport { isWaapiSupportedEasing };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,oBAAoB,QAAQ,iBAAiB;AAEtD,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAOC,OAAO,CAAE,OAAOD,MAAM,KAAK,UAAU,IAAIH,oBAAoB,CAAC,CAAC,IAClE,CAACG,MAAM,IACN,OAAOA,MAAM,KAAK,QAAQ,KACtBA,MAAM,IAAIF,oBAAoB,IAAID,oBAAoB,CAAC,CAAC,CAAE,IAC/DD,kBAAkB,CAACI,MAAM,CAAC,IACzBE,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,IAAIA,MAAM,CAACI,KAAK,CAACL,sBAAsB,CAAE,CAAC;AACxE;AAEA,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}