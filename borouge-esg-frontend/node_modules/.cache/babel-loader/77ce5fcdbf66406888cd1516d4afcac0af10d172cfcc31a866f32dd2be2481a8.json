{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"M17 21h2a2 2 0 0 0 2-2\",\n  key: \"130fy9\"\n}], [\"path\", {\n  d: \"M21 12v3\",\n  key: \"1wzk3p\"\n}], [\"path\", {\n  d: \"m21 3-5 5\",\n  key: \"1g5oa7\"\n}], [\"path\", {\n  d: \"M3 7V5a2 2 0 0 1 2-2\",\n  key: \"kk3yz1\"\n}], [\"path\", {\n  d: \"m5 21 4.144-4.144a1.21 1.21 0 0 1 1.712 0L13 19\",\n  key: \"fyekpt\"\n}], [\"path\", {\n  d: \"M9 3h3\",\n  key: \"d52fa\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"11\",\n  width: \"10\",\n  height: \"10\",\n  rx: \"1\",\n  key: \"1wpmix\"\n}]];\nconst ImageUpscale = createLucideIcon(\"image-upscale\", __iconNode);\nexport { __iconNode, ImageUpscale as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "ImageUpscale", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/image-upscale.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 3h5v5', key: '1806ms' }],\n  ['path', { d: 'M17 21h2a2 2 0 0 0 2-2', key: '130fy9' }],\n  ['path', { d: 'M21 12v3', key: '1wzk3p' }],\n  ['path', { d: 'm21 3-5 5', key: '1g5oa7' }],\n  ['path', { d: 'M3 7V5a2 2 0 0 1 2-2', key: 'kk3yz1' }],\n  ['path', { d: 'm5 21 4.144-4.144a1.21 1.21 0 0 1 1.712 0L13 19', key: 'fyekpt' }],\n  ['path', { d: 'M9 3h3', key: 'd52fa' }],\n  ['rect', { x: '3', y: '11', width: '10', height: '10', rx: '1', key: '1wpmix' }],\n];\n\n/**\n * @component @name ImageUpscale\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgM2g1djUiIC8+CiAgPHBhdGggZD0iTTE3IDIxaDJhMiAyIDAgMCAwIDItMiIgLz4KICA8cGF0aCBkPSJNMjEgMTJ2MyIgLz4KICA8cGF0aCBkPSJtMjEgMy01IDUiIC8+CiAgPHBhdGggZD0iTTMgN1Y1YTIgMiAwIDAgMSAyLTIiIC8+CiAgPHBhdGggZD0ibTUgMjEgNC4xNDQtNC4xNDRhMS4yMSAxLjIxIDAgMCAxIDEuNzEyIDBMMTMgMTkiIC8+CiAgPHBhdGggZD0iTTkgM2gzIiAvPgogIDxyZWN0IHg9IjMiIHk9IjExIiB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/image-upscale\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ImageUpscale = createLucideIcon('image-upscale', __iconNode);\n\nexport default ImageUpscale;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAS,GACtC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAMC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EACjF;AAaM,MAAAM,YAAA,GAAeC,gBAAiB,kBAAiBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}