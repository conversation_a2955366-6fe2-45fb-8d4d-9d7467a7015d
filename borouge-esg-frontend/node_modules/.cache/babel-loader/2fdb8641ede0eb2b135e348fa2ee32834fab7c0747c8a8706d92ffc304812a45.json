{"ast": null, "code": "import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n  const start = time.now();\n  const checkElapsed = ({\n    timestamp\n  }) => {\n    const elapsed = timestamp - start;\n    if (elapsed >= timeout) {\n      cancelFrame(checkElapsed);\n      callback(elapsed - timeout);\n    }\n  };\n  frame.setup(checkElapsed, true);\n  return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n  return delay(callback, secondsToMilliseconds(timeout));\n}\nexport { delay, delayInSeconds };", "map": {"version": 3, "names": ["time", "frame", "cancelFrame", "secondsToMilliseconds", "delay", "callback", "timeout", "start", "now", "checkElapsed", "timestamp", "elapsed", "setup", "delayInSeconds"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/utils/delay.mjs"], "sourcesContent": ["import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = time.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.setup(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n    return delay(callback, secondsToMilliseconds(timeout));\n}\n\nexport { delay, delayInSeconds };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,WAAW,QAAQ,YAAY;AACrD,SAASC,qBAAqB,QAAQ,cAAc;;AAEpD;AACA;AACA;AACA,SAASC,KAAKA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC9B,MAAMC,KAAK,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC;EACxB,MAAMC,YAAY,GAAGA,CAAC;IAAEC;EAAU,CAAC,KAAK;IACpC,MAAMC,OAAO,GAAGD,SAAS,GAAGH,KAAK;IACjC,IAAII,OAAO,IAAIL,OAAO,EAAE;MACpBJ,WAAW,CAACO,YAAY,CAAC;MACzBJ,QAAQ,CAACM,OAAO,GAAGL,OAAO,CAAC;IAC/B;EACJ,CAAC;EACDL,KAAK,CAACW,KAAK,CAACH,YAAY,EAAE,IAAI,CAAC;EAC/B,OAAO,MAAMP,WAAW,CAACO,YAAY,CAAC;AAC1C;AACA,SAASI,cAAcA,CAACR,QAAQ,EAAEC,OAAO,EAAE;EACvC,OAAOF,KAAK,CAACC,QAAQ,EAAEF,qBAAqB,CAACG,OAAO,CAAC,CAAC;AAC1D;AAEA,SAASF,KAAK,EAAES,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}