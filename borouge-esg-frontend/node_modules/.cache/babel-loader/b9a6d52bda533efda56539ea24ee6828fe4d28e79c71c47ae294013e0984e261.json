{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v14\",\n  key: \"jyx4ut\"\n}], [\"path\", {\n  d: \"m19 9-7 7-7-7\",\n  key: \"1oe3oy\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"21\",\n  r: \"1\",\n  key: \"o0uj5v\"\n}]];\nconst ArrowDownToDot = createLucideIcon(\"arrow-down-to-dot\", __iconNode);\nexport { __iconNode, ArrowDownToDot as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "ArrowDownToDot", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/arrow-down-to-dot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v14', key: 'jyx4ut' }],\n  ['path', { d: 'm19 9-7 7-7-7', key: '1oe3oy' }],\n  ['circle', { cx: '12', cy: '21', r: '1', key: 'o0uj5v' }],\n];\n\n/**\n * @component @name ArrowDownToDot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYxNCIgLz4KICA8cGF0aCBkPSJtMTkgOS03IDctNy03IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMjEiIHI9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-down-to-dot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownToDot = createLucideIcon('arrow-down-to-dot', __iconNode);\n\nexport default ArrowDownToDot;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EAC1D;AAaM,MAAAI,cAAA,GAAiBC,gBAAiB,sBAAqBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}