{"ast": null, "code": "/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n  return typeof v === \"string\" || Array.isArray(v);\n}\nexport { isVariantLabel };", "map": {"version": 3, "names": ["isVariantLabel", "v", "Array", "isArray"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs"], "sourcesContent": ["/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\nexport { isVariantLabel };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,CAAC,EAAE;EACvB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC;AACpD;AAEA,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}