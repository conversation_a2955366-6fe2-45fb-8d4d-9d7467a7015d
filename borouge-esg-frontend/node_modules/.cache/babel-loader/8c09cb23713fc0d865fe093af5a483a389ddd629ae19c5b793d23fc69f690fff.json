{"ast": null, "code": "import { frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, {\n  container = document.scrollingElement,\n  ...options\n} = {}) {\n  if (!container) return noop;\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info = createScrollInfo();\n  const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const measureAll = () => {\n      for (const handler of containerHandlers) {\n        handler.measure(frameData.timestamp);\n      }\n      frame.preUpdate(notifyAll);\n    };\n    const notifyAll = () => {\n      for (const handler of containerHandlers) {\n        handler.notify();\n      }\n    };\n    const listener = () => frame.read(measureAll);\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n    listener();\n  }\n  const listener = scrollListeners.get(container);\n  frame.read(listener, false, true);\n  return () => {\n    cancelFrame(listener);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const currentHandlers = onScrollHandlers.get(container);\n    if (!currentHandlers) return;\n    currentHandlers.delete(containerHandler);\n    if (currentHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const scrollListener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (scrollListener) {\n      getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n      resizeListeners.get(container)?.();\n      window.removeEventListener(\"resize\", scrollListener);\n    }\n  };\n}\nexport { scrollInfo };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "noop", "resize", "createScrollInfo", "createOnScrollHandler", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "scrollingElement", "window", "scrollInfo", "onScroll", "container", "options", "containerHandlers", "get", "Set", "set", "info", "containerHandler", "add", "has", "measureAll", "handler", "measure", "timestamp", "preUpdate", "notifyAll", "notify", "listener", "read", "target", "addEventListener", "passive", "documentElement", "currentHandlers", "delete", "size", "scrollListener", "removeEventListener"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(frameData.timestamp);\n            }\n            frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AAC1D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,gBAAgB,GAAGC,MAAM,GAAGH,OAAO;AAC5F,SAASI,UAAUA,CAACC,QAAQ,EAAE;EAAEC,SAAS,GAAGL,QAAQ,CAACC,gBAAgB;EAAE,GAAGK;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EACtF,IAAI,CAACD,SAAS,EACV,OAAOf,IAAI;EACf,IAAIiB,iBAAiB,GAAGV,gBAAgB,CAACW,GAAG,CAACH,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACE,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC7BZ,gBAAgB,CAACa,GAAG,CAACL,SAAS,EAAEE,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,IAAI,GAAGnB,gBAAgB,CAAC,CAAC;EAC/B,MAAMoB,gBAAgB,GAAGnB,qBAAqB,CAACY,SAAS,EAAED,QAAQ,EAAEO,IAAI,EAAEL,OAAO,CAAC;EAClFC,iBAAiB,CAACM,GAAG,CAACD,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAAClB,eAAe,CAACoB,GAAG,CAACT,SAAS,CAAC,EAAE;IACjC,MAAMU,UAAU,GAAGA,CAAA,KAAM;MACrB,KAAK,MAAMC,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACC,OAAO,CAAC5B,SAAS,CAAC6B,SAAS,CAAC;MACxC;MACA/B,KAAK,CAACgC,SAAS,CAACC,SAAS,CAAC;IAC9B,CAAC;IACD,MAAMA,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAMJ,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACK,MAAM,CAAC,CAAC;MACpB;IACJ,CAAC;IACD,MAAMC,QAAQ,GAAGA,CAAA,KAAMnC,KAAK,CAACoC,IAAI,CAACR,UAAU,CAAC;IAC7CrB,eAAe,CAACgB,GAAG,CAACL,SAAS,EAAEiB,QAAQ,CAAC;IACxC,MAAME,MAAM,GAAG1B,cAAc,CAACO,SAAS,CAAC;IACxCH,MAAM,CAACuB,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAIrB,SAAS,KAAKL,QAAQ,CAAC2B,eAAe,EAAE;MACxC/B,eAAe,CAACc,GAAG,CAACL,SAAS,EAAEd,MAAM,CAACc,SAAS,EAAEiB,QAAQ,CAAC,CAAC;IAC/D;IACAE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9DJ,QAAQ,CAAC,CAAC;EACd;EACA,MAAMA,QAAQ,GAAG5B,eAAe,CAACc,GAAG,CAACH,SAAS,CAAC;EAC/ClB,KAAK,CAACoC,IAAI,CAACD,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;EACjC,OAAO,MAAM;IACTlC,WAAW,CAACkC,QAAQ,CAAC;IACrB;AACR;AACA;IACQ,MAAMM,eAAe,GAAG/B,gBAAgB,CAACW,GAAG,CAACH,SAAS,CAAC;IACvD,IAAI,CAACuB,eAAe,EAChB;IACJA,eAAe,CAACC,MAAM,CAACjB,gBAAgB,CAAC;IACxC,IAAIgB,eAAe,CAACE,IAAI,EACpB;IACJ;AACR;AACA;IACQ,MAAMC,cAAc,GAAGrC,eAAe,CAACc,GAAG,CAACH,SAAS,CAAC;IACrDX,eAAe,CAACmC,MAAM,CAACxB,SAAS,CAAC;IACjC,IAAI0B,cAAc,EAAE;MAChBjC,cAAc,CAACO,SAAS,CAAC,CAAC2B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;MACvEnC,eAAe,CAACY,GAAG,CAACH,SAAS,CAAC,GAAG,CAAC;MAClCH,MAAM,CAAC8B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;IACxD;EACJ,CAAC;AACL;AAEA,SAAS5B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}