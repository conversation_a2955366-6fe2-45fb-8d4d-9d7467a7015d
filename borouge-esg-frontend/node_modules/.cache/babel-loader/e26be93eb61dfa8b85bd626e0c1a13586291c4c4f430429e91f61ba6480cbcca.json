{"ast": null, "code": "import { mixNumber } from 'motion-dom';\nconst SCALE_PRECISION = 0.0001;\nconst SCALE_MIN = 1 - SCALE_PRECISION;\nconst SCALE_MAX = 1 + SCALE_PRECISION;\nconst TRANSLATE_PRECISION = 0.01;\nconst TRANSLATE_MIN = 0 - TRANSLATE_PRECISION;\nconst TRANSLATE_MAX = 0 + TRANSLATE_PRECISION;\nfunction calcLength(axis) {\n  return axis.max - axis.min;\n}\nfunction isNear(value, target, maxDistance) {\n  return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n  delta.origin = origin;\n  delta.originPoint = mixNumber(source.min, source.max, delta.origin);\n  delta.scale = calcLength(target) / calcLength(source);\n  delta.translate = mixNumber(target.min, target.max, delta.origin) - delta.originPoint;\n  if (delta.scale >= SCALE_MIN && delta.scale <= SCALE_MAX || isNaN(delta.scale)) {\n    delta.scale = 1.0;\n  }\n  if (delta.translate >= TRANSLATE_MIN && delta.translate <= TRANSLATE_MAX || isNaN(delta.translate)) {\n    delta.translate = 0.0;\n  }\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n  calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n  calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n  target.min = parent.min + relative.min;\n  target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n  calcRelativeAxis(target.x, relative.x, parent.x);\n  calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n  target.min = layout.min - parent.min;\n  target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n  calcRelativeAxisPosition(target.x, layout.x, parent.x);\n  calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };", "map": {"version": 3, "names": ["mixNumber", "SCALE_PRECISION", "SCALE_MIN", "SCALE_MAX", "TRANSLATE_PRECISION", "TRANSLATE_MIN", "TRANSLATE_MAX", "calcLength", "axis", "max", "min", "isNear", "value", "target", "maxDistance", "Math", "abs", "calcAxisDelta", "delta", "source", "origin", "originPoint", "scale", "translate", "isNaN", "calcBoxDelta", "x", "originX", "undefined", "y", "originY", "calcRelativeAxis", "relative", "parent", "calcRelativeBox", "calcRelativeAxisPosition", "layout", "calcRelativePosition"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs"], "sourcesContent": ["import { mixNumber } from 'motion-dom';\n\nconst SCALE_PRECISION = 0.0001;\nconst SCALE_MIN = 1 - SCALE_PRECISION;\nconst SCALE_MAX = 1 + SCALE_PRECISION;\nconst TRANSLATE_PRECISION = 0.01;\nconst TRANSLATE_MIN = 0 - TRANSLATE_PRECISION;\nconst TRANSLATE_MAX = 0 + TRANSLATE_PRECISION;\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target, maxDistance) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = mixNumber(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    delta.translate =\n        mixNumber(target.min, target.max, delta.origin) - delta.originPoint;\n    if ((delta.scale >= SCALE_MIN && delta.scale <= SCALE_MAX) ||\n        isNaN(delta.scale)) {\n        delta.scale = 1.0;\n    }\n    if ((delta.translate >= TRANSLATE_MIN &&\n        delta.translate <= TRANSLATE_MAX) ||\n        isNaN(delta.translate)) {\n        delta.translate = 0.0;\n    }\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AAEtC,MAAMC,eAAe,GAAG,MAAM;AAC9B,MAAMC,SAAS,GAAG,CAAC,GAAGD,eAAe;AACrC,MAAME,SAAS,GAAG,CAAC,GAAGF,eAAe;AACrC,MAAMG,mBAAmB,GAAG,IAAI;AAChC,MAAMC,aAAa,GAAG,CAAC,GAAGD,mBAAmB;AAC7C,MAAME,aAAa,GAAG,CAAC,GAAGF,mBAAmB;AAC7C,SAASG,UAAUA,CAACC,IAAI,EAAE;EACtB,OAAOA,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE,GAAG;AAC9B;AACA,SAASC,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACxC,OAAOC,IAAI,CAACC,GAAG,CAACJ,KAAK,GAAGC,MAAM,CAAC,IAAIC,WAAW;AAClD;AACA,SAASG,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAEN,MAAM,EAAEO,MAAM,GAAG,GAAG,EAAE;EACxDF,KAAK,CAACE,MAAM,GAAGA,MAAM;EACrBF,KAAK,CAACG,WAAW,GAAGrB,SAAS,CAACmB,MAAM,CAACT,GAAG,EAAES,MAAM,CAACV,GAAG,EAAES,KAAK,CAACE,MAAM,CAAC;EACnEF,KAAK,CAACI,KAAK,GAAGf,UAAU,CAACM,MAAM,CAAC,GAAGN,UAAU,CAACY,MAAM,CAAC;EACrDD,KAAK,CAACK,SAAS,GACXvB,SAAS,CAACa,MAAM,CAACH,GAAG,EAAEG,MAAM,CAACJ,GAAG,EAAES,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACG,WAAW;EACvE,IAAKH,KAAK,CAACI,KAAK,IAAIpB,SAAS,IAAIgB,KAAK,CAACI,KAAK,IAAInB,SAAS,IACrDqB,KAAK,CAACN,KAAK,CAACI,KAAK,CAAC,EAAE;IACpBJ,KAAK,CAACI,KAAK,GAAG,GAAG;EACrB;EACA,IAAKJ,KAAK,CAACK,SAAS,IAAIlB,aAAa,IACjCa,KAAK,CAACK,SAAS,IAAIjB,aAAa,IAChCkB,KAAK,CAACN,KAAK,CAACK,SAAS,CAAC,EAAE;IACxBL,KAAK,CAACK,SAAS,GAAG,GAAG;EACzB;AACJ;AACA,SAASE,YAAYA,CAACP,KAAK,EAAEC,MAAM,EAAEN,MAAM,EAAEO,MAAM,EAAE;EACjDH,aAAa,CAACC,KAAK,CAACQ,CAAC,EAAEP,MAAM,CAACO,CAAC,EAAEb,MAAM,CAACa,CAAC,EAAEN,MAAM,GAAGA,MAAM,CAACO,OAAO,GAAGC,SAAS,CAAC;EAC/EX,aAAa,CAACC,KAAK,CAACW,CAAC,EAAEV,MAAM,CAACU,CAAC,EAAEhB,MAAM,CAACgB,CAAC,EAAET,MAAM,GAAGA,MAAM,CAACU,OAAO,GAAGF,SAAS,CAAC;AACnF;AACA,SAASG,gBAAgBA,CAAClB,MAAM,EAAEmB,QAAQ,EAAEC,MAAM,EAAE;EAChDpB,MAAM,CAACH,GAAG,GAAGuB,MAAM,CAACvB,GAAG,GAAGsB,QAAQ,CAACtB,GAAG;EACtCG,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAACH,GAAG,GAAGH,UAAU,CAACyB,QAAQ,CAAC;AAClD;AACA,SAASE,eAAeA,CAACrB,MAAM,EAAEmB,QAAQ,EAAEC,MAAM,EAAE;EAC/CF,gBAAgB,CAAClB,MAAM,CAACa,CAAC,EAAEM,QAAQ,CAACN,CAAC,EAAEO,MAAM,CAACP,CAAC,CAAC;EAChDK,gBAAgB,CAAClB,MAAM,CAACgB,CAAC,EAAEG,QAAQ,CAACH,CAAC,EAAEI,MAAM,CAACJ,CAAC,CAAC;AACpD;AACA,SAASM,wBAAwBA,CAACtB,MAAM,EAAEuB,MAAM,EAAEH,MAAM,EAAE;EACtDpB,MAAM,CAACH,GAAG,GAAG0B,MAAM,CAAC1B,GAAG,GAAGuB,MAAM,CAACvB,GAAG;EACpCG,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAACH,GAAG,GAAGH,UAAU,CAAC6B,MAAM,CAAC;AAChD;AACA,SAASC,oBAAoBA,CAACxB,MAAM,EAAEuB,MAAM,EAAEH,MAAM,EAAE;EAClDE,wBAAwB,CAACtB,MAAM,CAACa,CAAC,EAAEU,MAAM,CAACV,CAAC,EAAEO,MAAM,CAACP,CAAC,CAAC;EACtDS,wBAAwB,CAACtB,MAAM,CAACgB,CAAC,EAAEO,MAAM,CAACP,CAAC,EAAEI,MAAM,CAACJ,CAAC,CAAC;AAC1D;AAEA,SAASZ,aAAa,EAAEQ,YAAY,EAAElB,UAAU,EAAEwB,gBAAgB,EAAEI,wBAAwB,EAAED,eAAe,EAAEG,oBAAoB,EAAE1B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}