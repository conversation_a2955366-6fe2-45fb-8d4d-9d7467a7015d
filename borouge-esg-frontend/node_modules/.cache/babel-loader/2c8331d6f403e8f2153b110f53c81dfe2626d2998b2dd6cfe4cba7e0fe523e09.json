{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\nconst Icon = forwardRef(({\n  color = \"currentColor\",\n  size = 24,\n  strokeWidth = 2,\n  absoluteStrokeWidth,\n  className = \"\",\n  children,\n  iconNode,\n  ...rest\n}, ref) => createElement(\"svg\", {\n  ref,\n  ...defaultAttributes,\n  width: size,\n  height: size,\n  stroke: color,\n  strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n  className: mergeClasses(\"lucide\", className),\n  ...(!children && !hasA11yProp(rest) && {\n    \"aria-hidden\": \"true\"\n  }),\n  ...rest\n}, [...iconNode.map(([tag, attrs]) => createElement(tag, attrs)), ...(Array.isArray(children) ? children : [children])]));\nexport { Icon as default };", "map": {"version": 3, "names": ["Icon", "forwardRef", "color", "size", "strokeWidth", "absoluteStrokeWidth", "className", "children", "iconNode", "rest", "ref", "createElement", "defaultAttributes", "width", "height", "stroke", "Number", "mergeClasses", "hasA11yProp", "map", "tag", "attrs", "Array", "isArray"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "mappings": ";;;;;;;;;;AAwBA,MAAMA,IAAO,GAAAC,UAAA,CACX,CACE;EACEC,KAAQ;EACRC,IAAO;EACPC,WAAc;EACdC,mBAAA;EACAC,SAAY;EACZC,QAAA;EACAC,QAAA;EACA,GAAGC;AAAA,GAELC,GAEA,KAAAC,aAAA,CACE,OACA;EACED,GAAA;EACA,GAAGE,iBAAA;EACHC,KAAO,EAAAV,IAAA;EACPW,MAAQ,EAAAX,IAAA;EACRY,MAAQ,EAAAb,KAAA;EACRE,WAAA,EAAaC,mBAAA,GAAuBW,MAAO,CAAAZ,WAAW,IAAI,EAAM,GAAAY,MAAA,CAAOb,IAAI,CAAI,GAAAC,WAAA;EAC/EE,SAAA,EAAWW,YAAa,WAAUX,SAAS;EAC3C,IAAI,CAACC,QAAY,KAACW,WAAA,CAAYT,IAAI,KAAK;IAAE,eAAe;EAAO;EAC/D,GAAGA;AACL,GACA,CACE,GAAGD,QAAS,CAAAW,GAAA,CAAI,CAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,KAAAV,aAAA,CAAcS,GAAK,EAAAC,KAAK,CAAC,GAC3D,IAAIC,KAAM,CAAAC,OAAA,CAAQhB,QAAQ,CAAI,GAAAA,QAAA,GAAW,CAACA,QAAQ,GAEtD,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}