{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18.518 17.347A7 7 0 0 1 14 19\",\n  key: \"1emhpo\"\n}], [\"path\", {\n  d: \"M18.8 4A11 11 0 0 1 20 9\",\n  key: \"127b67\"\n}], [\"path\", {\n  d: \"M9 9h.01\",\n  key: \"1q5me6\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1v9bxh\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"7\",\n  key: \"p2h5vp\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"16\",\n  width: \"10\",\n  height: \"6\",\n  rx: \"2\",\n  key: \"bfnviv\"\n}]];\nconst BellElectric = createLucideIcon(\"bell-electric\", __iconNode);\nexport { __iconNode, BellElectric as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "x", "y", "width", "height", "rx", "BellElectric", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/bell-electric.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18.518 17.347A7 7 0 0 1 14 19', key: '1emhpo' }],\n  ['path', { d: 'M18.8 4A11 11 0 0 1 20 9', key: '127b67' }],\n  ['path', { d: 'M9 9h.01', key: '1q5me6' }],\n  ['circle', { cx: '20', cy: '16', r: '2', key: '1v9bxh' }],\n  ['circle', { cx: '9', cy: '9', r: '7', key: 'p2h5vp' }],\n  ['rect', { x: '4', y: '16', width: '10', height: '6', rx: '2', key: 'bfnviv' }],\n];\n\n/**\n * @component @name BellElectric\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTguNTE4IDE3LjM0N0E3IDcgMCAwIDEgMTQgMTkiIC8+CiAgPHBhdGggZD0iTTE4LjggNEExMSAxMSAwIDAgMSAyMCA5IiAvPgogIDxwYXRoIGQ9Ik05IDloLjAxIiAvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjIiIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjkiIHI9IjciIC8+CiAgPHJlY3QgeD0iNCIgeT0iMTYiIHdpZHRoPSIxMCIgaGVpZ2h0PSI2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bell-electric\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BellElectric = createLucideIcon('bell-electric', __iconNode);\n\nexport default BellElectric;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAEI,CAAA,EAAG;EAAKC,CAAG;EAAMC,KAAO;EAAMC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAR,GAAA,EAAK;AAAA,CAAU,EAChF;AAaM,MAAAS,YAAA,GAAeC,gBAAiB,kBAAiBZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}