{"ast": null, "code": "import { createMotionComponentFactory } from '../create-factory.mjs';\nconst createMinimalMotionComponent = /*@__PURE__*/createMotionComponentFactory();\nexport { createMinimalMotionComponent };", "map": {"version": 3, "names": ["createMotionComponentFactory", "createMinimalMotionComponent"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/components/m/create.mjs"], "sourcesContent": ["import { createMotionComponentFactory } from '../create-factory.mjs';\n\nconst createMinimalMotionComponent = \n/*@__PURE__*/ createMotionComponentFactory();\n\nexport { createMinimalMotionComponent };\n"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,uBAAuB;AAEpE,MAAMC,4BAA4B,GAClC,aAAcD,4BAA4B,CAAC,CAAC;AAE5C,SAASC,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}