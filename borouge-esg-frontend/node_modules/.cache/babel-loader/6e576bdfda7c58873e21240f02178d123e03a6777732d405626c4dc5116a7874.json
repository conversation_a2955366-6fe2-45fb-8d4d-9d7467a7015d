{"ast": null, "code": "import { createDOMMotionComponentProxy } from '../create-proxy.mjs';\nimport { createMinimalMotionComponent } from './create.mjs';\nconst m = /*@__PURE__*/createDOMMotionComponentProxy(createMinimalMotionComponent);\nexport { m };", "map": {"version": 3, "names": ["createDOMMotionComponentProxy", "createMinimalMotionComponent", "m"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/components/m/proxy.mjs"], "sourcesContent": ["import { createDOMMotionComponentProxy } from '../create-proxy.mjs';\nimport { createMinimalMotionComponent } from './create.mjs';\n\nconst m = /*@__PURE__*/ createDOMMotionComponentProxy(createMinimalMotionComponent);\n\nexport { m };\n"], "mappings": "AAAA,SAASA,6BAA6B,QAAQ,qBAAqB;AACnE,SAASC,4BAA4B,QAAQ,cAAc;AAE3D,MAAMC,CAAC,GAAG,aAAcF,6BAA6B,CAACC,4BAA4B,CAAC;AAEnF,SAASC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}