{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowLeft, Send, Download, ExternalLink, TrendingUp, AlertTriangle, Info, Loader2, Copy, Share2, ChevronDown, ChevronUp, Target, DollarSign, Clock, Users } from 'lucide-react';\nimport './ConversationView.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationView = ({\n  initialQuery,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n  const generateMockResponse = query => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = query => {\n      const lowerQuery = query.toLowerCase();\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n    return getReportByQuery(query);\n  };\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [{\n        type: \"regulatory\",\n        title: \"Mandatory Recycled Content Requirements\",\n        impact: \"Critical\",\n        description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n        action: \"Secure recycling partnerships immediately\"\n      }, {\n        type: \"financial\",\n        title: \"Investment Requirements\",\n        impact: \"High\",\n        description: \"$800M-1.2B needed for compliance infrastructure\",\n        action: \"Establish dedicated compliance budget\"\n      }, {\n        type: \"competitive\",\n        title: \"SABIC Competitive Threat\",\n        impact: \"High\",\n        description: \"Risk losing 15-20% EU market share to competitors\",\n        action: \"Accelerate sustainable product development\"\n      }],\n      detailedFindings: [{\n        type: \"regulatory\",\n        title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n        details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n        confidence: 98,\n        timeline: \"Implementation: Jan 2025, Full compliance: 2030\",\n        isBorogueSpecific: false\n      }, {\n        type: \"financial\",\n        title: \"Compliance Investment Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n        details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2027\",\n        isBorogueSpecific: false\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Landscape Shift\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n        details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n        confidence: 92,\n        timeline: \"Competitive threat: Immediate\",\n        isBorogueSpecific: false\n      }, {\n        type: \"market\",\n        title: \"Borouge Strategic Partnership with ALPLA Group\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Exclusive partnership opportunity with ALPLA Group to secure 40% of required recycled content supply for EU compliance.\",\n        details: \"Strategic alliance would provide Borouge with preferential access to high-quality recycled polyolefins from ALPLA's European network, ensuring compliance while creating competitive moat against SABIC and Dow.\",\n        confidence: 94,\n        timeline: \"Partnership agreement needed within 6 months\",\n        isBorogueSpecific: true\n      }, {\n        type: \"technology\",\n        title: \"Borouge Advanced Chemical Recycling Initiative\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Proprietary chemical recycling technology development could position Borouge as market leader in circular polyolefins.\",\n        details: \"Investment in advanced pyrolysis and depolymerization technologies would enable Borouge to process mixed plastic waste into virgin-quality feedstock, creating new revenue streams worth €300-500M annually by 2030.\",\n        confidence: 87,\n        timeline: \"36 months to commercial deployment\",\n        isBorogueSpecific: true\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [{\n        priority: \"Critical\",\n        action: \"Form EU Compliance Task Force\",\n        timeline: \"Next 30 days\",\n        investment: \"$5M\",\n        description: \"Immediate action team to coordinate regulatory response\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Recycling Partnerships\",\n        timeline: \"6 months\",\n        investment: \"$200-300M\",\n        description: \"Lock in technology partnerships before competitors\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line\",\n        timeline: \"12 months\",\n        investment: \"$150M\",\n        description: \"Develop premium recycled content products\"\n      }],\n      allRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish EU Regulatory Compliance Task Force\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$5M\",\n        description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Chemical Recycling Technology Partnerships\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$200-300M\",\n        description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line Development\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$150M\",\n        description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n      }, {\n        priority: \"Medium\",\n        action: \"Establish European Recycling Hub\",\n        timeline: \"24 months (Q4 2025)\",\n        investment: \"$400-500M\",\n        description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n      }],\n      competitiveBenchmarking: [{\n        company: \"SABIC\",\n        strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n        advantage: \"First-mover in chemical recycling, strong EU presence\",\n        weakness: \"Higher cost base, limited feedstock security\"\n      }, {\n        company: \"Dow\",\n        strategy: \"Advanced recycling partnerships, circular design principles\",\n        advantage: \"Technology leadership, established partnerships\",\n        weakness: \"Focus on specialty applications, limited commodity exposure\"\n      }, {\n        company: \"LyondellBasell\",\n        strategy: \"Molecular recycling technology, circular economy solutions\",\n        advantage: \"Integrated technology development, scale advantages\",\n        weakness: \"Limited EU manufacturing footprint\"\n      }],\n      sources: [{\n        title: \"EU Packaging & Packaging Waste Regulation\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2024-01-15\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Environment Agency Circular Economy Report\",\n        url: \"eea.europa.eu\",\n        date: \"2024-02-01\",\n        type: \"research\",\n        confidence: \"High\"\n      }, {\n        title: \"SABIC Circular Economy Strategy Update\",\n        url: \"sabic.com\",\n        date: \"2024-01-30\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"Plastics Europe Market Data 2024\",\n        url: \"plasticseurope.org\",\n        date: \"2024-02-15\",\n        type: \"industry\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey Circular Economy in Chemicals\",\n        url: \"mckinsey.com\",\n        date: \"2024-01-20\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }, {\n        title: \"Wood Mackenzie Petrochemicals Outlook\",\n        url: \"woodmac.com\",\n        date: \"2024-02-10\",\n        type: \"market\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [{\n        type: \"financial\",\n        title: \"Direct CBAM Cost Impact\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n        details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n        confidence: 92,\n        timeline: \"Implementation: January 2026\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Advantage Opportunity\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n        details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n        confidence: 88,\n        timeline: \"Advantage period: 2026-2035\"\n      }, {\n        type: \"technology\",\n        title: \"Carbon Reduction Investment Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n        details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2030\"\n      }, {\n        type: \"regulatory\",\n        title: \"CBAM Reporting and Verification Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n        details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n        confidence: 95,\n        timeline: \"Reporting starts: October 2023\"\n      }, {\n        type: \"market\",\n        title: \"Premium Low-Carbon Product Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n        details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n        confidence: 78,\n        timeline: \"Market development: 2025-2030\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Implement Comprehensive Carbon Accounting System\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$10M\",\n        description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n      }, {\n        priority: \"High\",\n        action: \"Accelerate Renewable Energy Integration\",\n        timeline: \"18 months (Q3 2025)\",\n        investment: \"$200M\",\n        description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n      }, {\n        priority: \"High\",\n        action: \"Develop Low-Carbon Product Certification\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$5M\",\n        description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n      }],\n      sources: [{\n        title: \"EU CBAM Regulation 2023/956\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2023-05-17\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Commission CBAM Implementation Guide\",\n        url: \"taxation-customs.ec.europa.eu\",\n        date: \"2024-01-10\",\n        type: \"guidance\",\n        confidence: \"Official\"\n      }, {\n        title: \"IEA Petrochemicals Carbon Intensity Database\",\n        url: \"iea.org\",\n        date: \"2024-02-05\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [{\n        type: \"environmental\",\n        title: \"Decarbonization Pathway Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n        details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n        confidence: 90,\n        timeline: \"Transformation period: 2024-2050\"\n      }, {\n        type: \"social\",\n        title: \"UAE Emiratization and Skills Development\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n        details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n        confidence: 85,\n        timeline: \"Achievement target: 2030\"\n      }, {\n        type: \"governance\",\n        title: \"ESG Reporting and Transparency Enhancement\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n        details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n        confidence: 88,\n        timeline: \"Full compliance: 2025\"\n      }, {\n        type: \"financial\",\n        title: \"Sustainable Finance and Green Bonds Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n        details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n        confidence: 82,\n        timeline: \"Financing window: 2024-2027\"\n      }, {\n        type: \"technology\",\n        title: \"Digital ESG Management Platform\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n        details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n        confidence: 75,\n        timeline: \"Implementation: 2024-2025\"\n      }],\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish Chief Sustainability Officer Role\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$2M\",\n        description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Comprehensive Decarbonization Program\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$500M\",\n        description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n      }, {\n        priority: \"High\",\n        action: \"Implement Advanced Emiratization Strategy\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$100M\",\n        description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n      }],\n      sources: [{\n        title: \"UAE Vision 2071 Strategic Framework\",\n        url: \"government.ae\",\n        date: \"2023-12-01\",\n        type: \"policy\",\n        confidence: \"Official\"\n      }, {\n        title: \"ADNOC Sustainability Strategy 2030\",\n        url: \"adnoc.ae\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey ESG in Chemicals Industry\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }]\n    };\n  };\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Recycled Polyethylene Market Growth\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n        details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n        confidence: 88,\n        timeline: \"Market expansion: 2024-2030\"\n      }],\n      sources: [{\n        title: \"Ellen MacArthur Foundation Circular Economy Report\",\n        url: \"ellenmacarthurfoundation.org\",\n        date: \"2024-01-10\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [{\n        type: \"competitive\",\n        title: \"SABIC Circular Economy Leadership\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n        details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n        confidence: 95,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      sources: [{\n        title: \"SABIC Sustainability Strategy 2030\",\n        url: \"sabic.com\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Sustainable Packaging Demand Surge\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n        details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n        confidence: 90,\n        timeline: \"Market shift: 2024-2027\"\n      }],\n      sources: [{\n        title: \"McKinsey Sustainable Packaging Report\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n  const copyMessage = content => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"conversation-view\",\n    initial: {\n      opacity: 0,\n      x: 20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    exit: {\n      opacity: 0,\n      x: -20\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversation-header\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"back-btn\",\n        onClick: onBack,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this), \"Back to Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-actions\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          onClick: exportToPDF,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Share2, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), \"Share\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.map(message => {\n          var _message$content$keyF, _message$content$stra, _message$content$sour, _message$content$sour2;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            className: `message ${message.type}`,\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-message\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-content\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-time\",\n                children: message.timestamp.toLocaleTimeString([], {\n                  hour: '2-digit',\n                  minute: '2-digit'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-message\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ai-response\",\n                children: typeof message.content === 'object' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"intelligence-report simplified\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"report-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"report-title-section\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: message.content.reportType || 'ESG Intelligence Report'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"report-actions\",\n                        children: /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"copy-btn secondary\",\n                          onClick: () => copyMessage(message.content),\n                          title: \"Copy report\",\n                          children: /*#__PURE__*/_jsxDEV(Copy, {\n                            size: 14\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 641,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 636,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"problem-solution-summary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"problem-statement\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"problem-icon\",\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 651,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"problem-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Business Challenge\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 654,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: message.content.problem\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 655,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"impact-highlight\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"impact-text\",\n                            children: message.content.impact\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 657,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"urgency-text\",\n                            children: message.content.urgency\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 658,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 656,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 27\n                    }, this), message.content.opportunity && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"opportunity-statement\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"opportunity-icon\",\n                        children: /*#__PURE__*/_jsxDEV(Target, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 666,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"opportunity-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Market Opportunity\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 669,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: message.content.opportunity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 670,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"key-insights\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Critical Findings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 678,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"insights-grid\",\n                      children: (message.content.topFindings || ((_message$content$keyF = message.content.keyFindings) === null || _message$content$keyF === void 0 ? void 0 : _message$content$keyF.slice(0, 3)) || []).map((finding, index) => {\n                        var _finding$impact, _finding$impact2;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"insight-card\",\n                          initial: {\n                            opacity: 0,\n                            y: 10\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          transition: {\n                            delay: index * 0.1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"insight-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `impact-indicator ${(_finding$impact = finding.impact) === null || _finding$impact === void 0 ? void 0 : _finding$impact.toLowerCase()}`,\n                              children: [finding.impact === 'Critical' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 690,\n                                columnNumber: 71\n                              }, this), finding.impact === 'High' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 691,\n                                columnNumber: 67\n                              }, this), finding.impact === 'Medium' && /*#__PURE__*/_jsxDEV(Info, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 692,\n                                columnNumber: 69\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 689,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `impact-label ${(_finding$impact2 = finding.impact) === null || _finding$impact2 === void 0 ? void 0 : _finding$impact2.toLowerCase()}`,\n                              children: finding.impact\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 694,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 688,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                            children: finding.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 698,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: finding.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 699,\n                            columnNumber: 33\n                          }, this), finding.action && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"quick-action\",\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Action:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 702,\n                              columnNumber: 37\n                            }, this), \" \", finding.action]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 701,\n                            columnNumber: 35\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 681,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 677,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"next-steps\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Immediate Next Steps\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"steps-list\",\n                      children: (message.content.nextSteps || ((_message$content$stra = message.content.strategicRecommendations) === null || _message$content$stra === void 0 ? void 0 : _message$content$stra.slice(0, 3)) || []).map((step, index) => {\n                        var _step$priority;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"step-card\",\n                          initial: {\n                            opacity: 0,\n                            x: -10\n                          },\n                          animate: {\n                            opacity: 1,\n                            x: 0\n                          },\n                          transition: {\n                            delay: 0.2 + index * 0.1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"step-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `priority-indicator ${(_step$priority = step.priority) === null || _step$priority === void 0 ? void 0 : _step$priority.toLowerCase()}`,\n                              children: index + 1\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 723,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"step-meta\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"step-timeline\",\n                                children: [/*#__PURE__*/_jsxDEV(Clock, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 728,\n                                  columnNumber: 39\n                                }, this), step.timeline]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 727,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"step-investment\",\n                                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 732,\n                                  columnNumber: 39\n                                }, this), step.investment]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 731,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 726,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 722,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                            children: step.action\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 737,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: step.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 33\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 715,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detailed-sections\",\n                    children: [(message.content.detailedFindings || message.content.keyFindings) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"collapsible-section\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"section-toggle\",\n                        onClick: () => toggleSection(message.id, 'detailed-findings'),\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Detailed Analysis\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 753,\n                          columnNumber: 33\n                        }, this), expandedSections[`${message.id}-detailed-findings`] ? /*#__PURE__*/_jsxDEV(ChevronUp, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 35\n                        }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 749,\n                        columnNumber: 31\n                      }, this), expandedSections[`${message.id}-detailed-findings`] && /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"section-content\",\n                        initial: {\n                          opacity: 0,\n                          height: 0\n                        },\n                        animate: {\n                          opacity: 1,\n                          height: 'auto'\n                        },\n                        exit: {\n                          opacity: 0,\n                          height: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"detailed-findings\",\n                          children: (message.content.detailedFindings || message.content.keyFindings || []).map((finding, index) => {\n                            var _finding$impact3;\n                            return /*#__PURE__*/_jsxDEV(motion.div, {\n                              className: `finding-card ${finding.isBorogueSpecific ? 'borouge-recommendation' : ''}`,\n                              \"data-type\": finding.type,\n                              initial: {\n                                opacity: 0,\n                                x: -20\n                              },\n                              animate: {\n                                opacity: 1,\n                                x: 0\n                              },\n                              transition: {\n                                delay: index * 0.1\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"finding-header\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"finding-icon\",\n                                  style: {\n                                    background: finding.type === 'regulatory' ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)' : finding.type === 'financial' ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)' : finding.type === 'competitive' ? 'linear-gradient(135deg, #0066cc 0%, #3b82f6 100%)' : finding.type === 'market' ? 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)' : finding.type === 'technology' ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)' : finding.type === 'environmental' ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)' : finding.type === 'social' ? 'linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)' : finding.type === 'governance' ? 'linear-gradient(135deg, #4338ca 0%, #6366f1 100%)' : 'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)'\n                                  },\n                                  children: [finding.type === 'regulatory' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 791,\n                                    columnNumber: 79\n                                  }, this), finding.type === 'financial' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 792,\n                                    columnNumber: 78\n                                  }, this), finding.type === 'competitive' && /*#__PURE__*/_jsxDEV(Users, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 793,\n                                    columnNumber: 80\n                                  }, this), finding.type === 'market' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 794,\n                                    columnNumber: 75\n                                  }, this), finding.type === 'technology' && /*#__PURE__*/_jsxDEV(Info, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 795,\n                                    columnNumber: 79\n                                  }, this), finding.type === 'environmental' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 796,\n                                    columnNumber: 82\n                                  }, this), finding.type === 'social' && /*#__PURE__*/_jsxDEV(Users, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 797,\n                                    columnNumber: 75\n                                  }, this), finding.type === 'governance' && /*#__PURE__*/_jsxDEV(Info, {\n                                    size: 20\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 798,\n                                    columnNumber: 79\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 777,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"finding-title\",\n                                  children: finding.title\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 800,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"finding-badges\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: `impact-badge ${(_finding$impact3 = finding.impact) === null || _finding$impact3 === void 0 ? void 0 : _finding$impact3.toLowerCase()}`,\n                                    children: [finding.impact, \" Impact\"]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 802,\n                                    columnNumber: 45\n                                  }, this), finding.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: `urgency-badge ${finding.urgency.toLowerCase()}`,\n                                    children: finding.urgency\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 806,\n                                    columnNumber: 47\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 801,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 776,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                className: \"finding-description\",\n                                children: finding.description\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 812,\n                                columnNumber: 41\n                              }, this), finding.details && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"finding-details\",\n                                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                                  children: finding.details\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 815,\n                                  columnNumber: 45\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 814,\n                                columnNumber: 43\n                              }, this), finding.timeline && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"finding-timeline\",\n                                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Timeline:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 820,\n                                  columnNumber: 45\n                                }, this), \" \", finding.timeline]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 819,\n                                columnNumber: 43\n                              }, this), finding.confidence && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"confidence-bar\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"confidence-label\",\n                                  children: [\"Confidence: \", finding.confidence, \"%\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 825,\n                                  columnNumber: 45\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"confidence-progress\",\n                                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                                    className: \"confidence-fill\",\n                                    initial: {\n                                      width: 0\n                                    },\n                                    animate: {\n                                      width: `${finding.confidence}%`\n                                    },\n                                    transition: {\n                                      duration: 1,\n                                      delay: 0.5\n                                    }\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 827,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 826,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 824,\n                                columnNumber: 43\n                              }, this)]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 768,\n                              columnNumber: 39\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 766,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"collapsible-section\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"section-toggle\",\n                        onClick: () => toggleSection(message.id, 'sources'),\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [\"Sources & References (\", ((_message$content$sour = message.content.sources) === null || _message$content$sour === void 0 ? void 0 : _message$content$sour.length) || 0, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 31\n                        }, this), expandedSections[`${message.id}-sources`] ? /*#__PURE__*/_jsxDEV(ChevronUp, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(ChevronDown, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 59\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 846,\n                        columnNumber: 29\n                      }, this), expandedSections[`${message.id}-sources`] && /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"section-content\",\n                        initial: {\n                          opacity: 0,\n                          height: 0\n                        },\n                        animate: {\n                          opacity: 1,\n                          height: 'auto'\n                        },\n                        exit: {\n                          opacity: 0,\n                          height: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"sources-section\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"sources-grid\",\n                            children: (_message$content$sour2 = message.content.sources) === null || _message$content$sour2 === void 0 ? void 0 : _message$content$sour2.map((source, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"source-card\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"source-header\",\n                                children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 868,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"source-title\",\n                                  children: source.title\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 869,\n                                  columnNumber: 43\n                                }, this), source.confidence && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: `source-confidence ${source.confidence.toLowerCase()}`,\n                                  children: source.confidence\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 871,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 867,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"source-meta\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"source-url\",\n                                  children: source.url\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 877,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"source-date\",\n                                  children: source.date\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 878,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"source-type\",\n                                  children: source.type\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 879,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 876,\n                                columnNumber: 41\n                              }, this)]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 866,\n                              columnNumber: 39\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 864,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 863,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 857,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 845,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"simple-response\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-time\",\n                children: message.timestamp.toLocaleTimeString([], {\n                  hour: '2-digit',\n                  minute: '2-digit'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-message\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(Loader2, {\n            className: \"loading-spinner\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Analyzing ESG data and regulations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-input-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-input-box\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask a follow-up question about ESG intelligence...\",\n          className: \"message-input\",\n          rows: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"send-btn\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || isLoading,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 922,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 568,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationView, \"Jfa2Q+54F4smiufyXm/+bCymPuA=\");\n_c = ConversationView;\nexport default ConversationView;\nvar _c;\n$RefreshReg$(_c, \"ConversationView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "ArrowLeft", "Send", "Download", "ExternalLink", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Loader2", "Copy", "Share2", "ChevronDown", "ChevronUp", "Target", "DollarSign", "Clock", "Users", "jsxDEV", "_jsxDEV", "ConversationView", "initialQuery", "onBack", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "isLoading", "setIsLoading", "expandedSections", "setExpandedSections", "messagesEndRef", "toggleSection", "messageId", "section", "prev", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "userMessage", "id", "type", "content", "timestamp", "Date", "setTimeout", "aiResponse", "generateMockResponse", "query", "getReportByQuery", "lowerQuery", "toLowerCase", "includes", "generateEURegulationReport", "generateCBAMReport", "generateCircularEconomyReport", "generateCompetitorReport", "generateMarketTrendsReport", "generateComprehensiveESGReport", "reportType", "problem", "impact", "urgency", "opportunity", "topFindings", "title", "description", "action", "detailedFindings", "details", "confidence", "timeline", "isBorogueSpecific", "marketImpact", "revenueAtRisk", "investmentRequired", "timelineForCompliance", "marketOpportunity", "riskAssessment", "high", "medium", "low", "nextSteps", "priority", "investment", "allRecommendations", "competitiveBenchmarking", "company", "strategy", "advantage", "weakness", "sources", "url", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyFindings", "strategicRecommendations", "handleSendMessage", "trim", "length", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "exportToPDF", "console", "log", "copyMessage", "navigator", "clipboard", "writeText", "JSON", "stringify", "div", "className", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "children", "button", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "message", "_message$content$keyF", "_message$content$stra", "_message$content$sour", "_message$content$sour2", "y", "toLocaleTimeString", "hour", "minute", "slice", "finding", "index", "_finding$impact", "_finding$impact2", "delay", "step", "_step$priority", "height", "_finding$impact3", "style", "background", "width", "source", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ArrowLeft,\n  Send,\n  Download,\n  ExternalLink,\n  TrendingUp,\n  AlertTriangle,\n  Info,\n  Loader2,\n  Copy,\n  Share2,\n  ChevronDown,\n  ChevronUp,\n  Target,\n  DollarSign,\n  Clock,\n  Users\n} from 'lucide-react';\nimport './ConversationView.css';\n\nconst ConversationView = ({ initialQuery, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n\n  const generateMockResponse = (query) => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = (query) => {\n      const lowerQuery = query.toLowerCase();\n\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n\n    return getReportByQuery(query);\n  };\n\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [\n        {\n          type: \"regulatory\",\n          title: \"Mandatory Recycled Content Requirements\",\n          impact: \"Critical\",\n          description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n          action: \"Secure recycling partnerships immediately\"\n        },\n        {\n          type: \"financial\",\n          title: \"Investment Requirements\",\n          impact: \"High\",\n          description: \"$800M-1.2B needed for compliance infrastructure\",\n          action: \"Establish dedicated compliance budget\"\n        },\n        {\n          type: \"competitive\",\n          title: \"SABIC Competitive Threat\",\n          impact: \"High\",\n          description: \"Risk losing 15-20% EU market share to competitors\",\n          action: \"Accelerate sustainable product development\"\n        }\n      ],\n      detailedFindings: [\n        {\n          type: \"regulatory\",\n          title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n          details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n          confidence: 98,\n          timeline: \"Implementation: Jan 2025, Full compliance: 2030\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"financial\",\n          title: \"Compliance Investment Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n          details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2027\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Landscape Shift\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n          details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n          confidence: 92,\n          timeline: \"Competitive threat: Immediate\",\n          isBorogueSpecific: false\n        },\n        {\n          type: \"market\",\n          title: \"Borouge Strategic Partnership with ALPLA Group\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Exclusive partnership opportunity with ALPLA Group to secure 40% of required recycled content supply for EU compliance.\",\n          details: \"Strategic alliance would provide Borouge with preferential access to high-quality recycled polyolefins from ALPLA's European network, ensuring compliance while creating competitive moat against SABIC and Dow.\",\n          confidence: 94,\n          timeline: \"Partnership agreement needed within 6 months\",\n          isBorogueSpecific: true\n        },\n        {\n          type: \"technology\",\n          title: \"Borouge Advanced Chemical Recycling Initiative\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Proprietary chemical recycling technology development could position Borouge as market leader in circular polyolefins.\",\n          details: \"Investment in advanced pyrolysis and depolymerization technologies would enable Borouge to process mixed plastic waste into virgin-quality feedstock, creating new revenue streams worth €300-500M annually by 2030.\",\n          confidence: 87,\n          timeline: \"36 months to commercial deployment\",\n          isBorogueSpecific: true\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [\n        {\n          priority: \"Critical\",\n          action: \"Form EU Compliance Task Force\",\n          timeline: \"Next 30 days\",\n          investment: \"$5M\",\n          description: \"Immediate action team to coordinate regulatory response\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Recycling Partnerships\",\n          timeline: \"6 months\",\n          investment: \"$200-300M\",\n          description: \"Lock in technology partnerships before competitors\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line\",\n          timeline: \"12 months\",\n          investment: \"$150M\",\n          description: \"Develop premium recycled content products\"\n        }\n      ],\n      allRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish EU Regulatory Compliance Task Force\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$5M\",\n          description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Chemical Recycling Technology Partnerships\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$200-300M\",\n          description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line Development\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$150M\",\n          description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n        },\n        {\n          priority: \"Medium\",\n          action: \"Establish European Recycling Hub\",\n          timeline: \"24 months (Q4 2025)\",\n          investment: \"$400-500M\",\n          description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n        }\n      ],\n      competitiveBenchmarking: [\n        {\n          company: \"SABIC\",\n          strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n          advantage: \"First-mover in chemical recycling, strong EU presence\",\n          weakness: \"Higher cost base, limited feedstock security\"\n        },\n        {\n          company: \"Dow\",\n          strategy: \"Advanced recycling partnerships, circular design principles\",\n          advantage: \"Technology leadership, established partnerships\",\n          weakness: \"Focus on specialty applications, limited commodity exposure\"\n        },\n        {\n          company: \"LyondellBasell\",\n          strategy: \"Molecular recycling technology, circular economy solutions\",\n          advantage: \"Integrated technology development, scale advantages\",\n          weakness: \"Limited EU manufacturing footprint\"\n        }\n      ],\n      sources: [\n        { title: \"EU Packaging & Packaging Waste Regulation\", url: \"eur-lex.europa.eu\", date: \"2024-01-15\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Environment Agency Circular Economy Report\", url: \"eea.europa.eu\", date: \"2024-02-01\", type: \"research\", confidence: \"High\" },\n        { title: \"SABIC Circular Economy Strategy Update\", url: \"sabic.com\", date: \"2024-01-30\", type: \"corporate\", confidence: \"High\" },\n        { title: \"Plastics Europe Market Data 2024\", url: \"plasticseurope.org\", date: \"2024-02-15\", type: \"industry\", confidence: \"High\" },\n        { title: \"McKinsey Circular Economy in Chemicals\", url: \"mckinsey.com\", date: \"2024-01-20\", type: \"consulting\", confidence: \"Medium\" },\n        { title: \"Wood Mackenzie Petrochemicals Outlook\", url: \"woodmac.com\", date: \"2024-02-10\", type: \"market\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [\n        {\n          type: \"financial\",\n          title: \"Direct CBAM Cost Impact\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n          details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n          confidence: 92,\n          timeline: \"Implementation: January 2026\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Advantage Opportunity\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n          details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n          confidence: 88,\n          timeline: \"Advantage period: 2026-2035\"\n        },\n        {\n          type: \"technology\",\n          title: \"Carbon Reduction Investment Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n          details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2030\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"CBAM Reporting and Verification Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n          details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n          confidence: 95,\n          timeline: \"Reporting starts: October 2023\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Low-Carbon Product Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n          details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n          confidence: 78,\n          timeline: \"Market development: 2025-2030\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Implement Comprehensive Carbon Accounting System\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$10M\",\n          description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Accelerate Renewable Energy Integration\",\n          timeline: \"18 months (Q3 2025)\",\n          investment: \"$200M\",\n          description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Develop Low-Carbon Product Certification\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$5M\",\n          description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n        }\n      ],\n      sources: [\n        { title: \"EU CBAM Regulation 2023/956\", url: \"eur-lex.europa.eu\", date: \"2023-05-17\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Commission CBAM Implementation Guide\", url: \"taxation-customs.ec.europa.eu\", date: \"2024-01-10\", type: \"guidance\", confidence: \"Official\" },\n        { title: \"IEA Petrochemicals Carbon Intensity Database\", url: \"iea.org\", date: \"2024-02-05\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [\n        {\n          type: \"environmental\",\n          title: \"Decarbonization Pathway Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n          details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n          confidence: 90,\n          timeline: \"Transformation period: 2024-2050\"\n        },\n        {\n          type: \"social\",\n          title: \"UAE Emiratization and Skills Development\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n          details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n          confidence: 85,\n          timeline: \"Achievement target: 2030\"\n        },\n        {\n          type: \"governance\",\n          title: \"ESG Reporting and Transparency Enhancement\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n          details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n          confidence: 88,\n          timeline: \"Full compliance: 2025\"\n        },\n        {\n          type: \"financial\",\n          title: \"Sustainable Finance and Green Bonds Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n          details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n          confidence: 82,\n          timeline: \"Financing window: 2024-2027\"\n        },\n        {\n          type: \"technology\",\n          title: \"Digital ESG Management Platform\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n          details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n          confidence: 75,\n          timeline: \"Implementation: 2024-2025\"\n        }\n      ],\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish Chief Sustainability Officer Role\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$2M\",\n          description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Comprehensive Decarbonization Program\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$500M\",\n          description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Implement Advanced Emiratization Strategy\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$100M\",\n          description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n        }\n      ],\n      sources: [\n        { title: \"UAE Vision 2071 Strategic Framework\", url: \"government.ae\", date: \"2023-12-01\", type: \"policy\", confidence: \"Official\" },\n        { title: \"ADNOC Sustainability Strategy 2030\", url: \"adnoc.ae\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" },\n        { title: \"McKinsey ESG in Chemicals Industry\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"Medium\" }\n      ]\n    };\n  };\n\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Recycled Polyethylene Market Growth\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n          details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n          confidence: 88,\n          timeline: \"Market expansion: 2024-2030\"\n        }\n      ],\n      sources: [\n        { title: \"Ellen MacArthur Foundation Circular Economy Report\", url: \"ellenmacarthurfoundation.org\", date: \"2024-01-10\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [\n        {\n          type: \"competitive\",\n          title: \"SABIC Circular Economy Leadership\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n          details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n          confidence: 95,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      sources: [\n        { title: \"SABIC Sustainability Strategy 2030\", url: \"sabic.com\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Sustainable Packaging Demand Surge\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n          details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n          confidence: 90,\n          timeline: \"Market shift: 2024-2027\"\n        }\n      ],\n      sources: [\n        { title: \"McKinsey Sustainable Packaging Report\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n\n  const copyMessage = (content) => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n\n  return (\n    <motion.div\n      className=\"conversation-view\"\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      exit={{ opacity: 0, x: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"conversation-header\">\n        <motion.button\n          className=\"back-btn\"\n          onClick={onBack}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <ArrowLeft size={20} />\n          Back to Search\n        </motion.button>\n\n        <div className=\"conversation-actions\">\n          <motion.button\n            className=\"action-btn\"\n            onClick={exportToPDF}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Download size={18} />\n            Export\n          </motion.button>\n          <motion.button\n            className=\"action-btn\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Share2 size={18} />\n            Share\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"messages-container\">\n        <AnimatePresence>\n          {messages.map((message) => (\n            <motion.div\n              key={message.id}\n              className={`message ${message.type}`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {message.type === 'user' ? (\n                <div className=\"user-message\">\n                  <div className=\"message-content\">{message.content}</div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"ai-message\">\n                  <div className=\"ai-response\">\n                    {typeof message.content === 'object' ? (\n                      <div className=\"intelligence-report simplified\">\n                        <div className=\"report-header\">\n                          <div className=\"report-title-section\">\n                            <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>\n                            <div className=\"report-actions\">\n                              <button\n                                className=\"copy-btn secondary\"\n                                onClick={() => copyMessage(message.content)}\n                                title=\"Copy report\"\n                              >\n                                <Copy size={14} />\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Problem-Solution Summary */}\n                        <div className=\"problem-solution-summary\">\n                          <div className=\"problem-statement\">\n                            <div className=\"problem-icon\">\n                              <AlertTriangle size={20} />\n                            </div>\n                            <div className=\"problem-content\">\n                              <h4>Business Challenge</h4>\n                              <p>{message.content.problem}</p>\n                              <div className=\"impact-highlight\">\n                                <span className=\"impact-text\">{message.content.impact}</span>\n                                <span className=\"urgency-text\">{message.content.urgency}</span>\n                              </div>\n                            </div>\n                          </div>\n\n                          {message.content.opportunity && (\n                            <div className=\"opportunity-statement\">\n                              <div className=\"opportunity-icon\">\n                                <Target size={20} />\n                              </div>\n                              <div className=\"opportunity-content\">\n                                <h4>Market Opportunity</h4>\n                                <p>{message.content.opportunity}</p>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n\n                        {/* Key Insights - Top 3 */}\n                        <div className=\"key-insights\">\n                          <h4>Critical Findings</h4>\n                          <div className=\"insights-grid\">\n                            {(message.content.topFindings || message.content.keyFindings?.slice(0, 3) || []).map((finding, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"insight-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: index * 0.1 }}\n                              >\n                                <div className=\"insight-header\">\n                                  <div className={`impact-indicator ${finding.impact?.toLowerCase()}`}>\n                                    {finding.impact === 'Critical' && <AlertTriangle size={16} />}\n                                    {finding.impact === 'High' && <TrendingUp size={16} />}\n                                    {finding.impact === 'Medium' && <Info size={16} />}\n                                  </div>\n                                  <span className={`impact-label ${finding.impact?.toLowerCase()}`}>\n                                    {finding.impact}\n                                  </span>\n                                </div>\n                                <h5>{finding.title}</h5>\n                                <p>{finding.description}</p>\n                                {finding.action && (\n                                  <div className=\"quick-action\">\n                                    <strong>Action:</strong> {finding.action}\n                                  </div>\n                                )}\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Next Steps - Top 3 Priority Actions */}\n                        <div className=\"next-steps\">\n                          <h4>Immediate Next Steps</h4>\n                          <div className=\"steps-list\">\n                            {(message.content.nextSteps || message.content.strategicRecommendations?.slice(0, 3) || []).map((step, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"step-card\"\n                                initial={{ opacity: 0, x: -10 }}\n                                animate={{ opacity: 1, x: 0 }}\n                                transition={{ delay: 0.2 + index * 0.1 }}\n                              >\n                                <div className=\"step-header\">\n                                  <div className={`priority-indicator ${step.priority?.toLowerCase()}`}>\n                                    {index + 1}\n                                  </div>\n                                  <div className=\"step-meta\">\n                                    <span className=\"step-timeline\">\n                                      <Clock size={14} />\n                                      {step.timeline}\n                                    </span>\n                                    <span className=\"step-investment\">\n                                      <DollarSign size={14} />\n                                      {step.investment}\n                                    </span>\n                                  </div>\n                                </div>\n                                <h5>{step.action}</h5>\n                                <p>{step.description}</p>\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Collapsible Detailed Analysis */}\n                        <div className=\"detailed-sections\">\n                          {/* Detailed Findings */}\n                          {(message.content.detailedFindings || message.content.keyFindings) && (\n                            <div className=\"collapsible-section\">\n                              <button\n                                className=\"section-toggle\"\n                                onClick={() => toggleSection(message.id, 'detailed-findings')}\n                              >\n                                <span>Detailed Analysis</span>\n                                {expandedSections[`${message.id}-detailed-findings`] ?\n                                  <ChevronUp size={16} /> : <ChevronDown size={16} />\n                                }\n                              </button>\n\n                              {expandedSections[`${message.id}-detailed-findings`] && (\n                                <motion.div\n                                  className=\"section-content\"\n                                  initial={{ opacity: 0, height: 0 }}\n                                  animate={{ opacity: 1, height: 'auto' }}\n                                  exit={{ opacity: 0, height: 0 }}\n                                >\n                                  <div className=\"detailed-findings\">\n                                    {(message.content.detailedFindings || message.content.keyFindings || []).map((finding, index) => (\n                                      <motion.div\n                                        key={index}\n                                        className={`finding-card ${finding.isBorogueSpecific ? 'borouge-recommendation' : ''}`}\n                                        data-type={finding.type}\n                                        initial={{ opacity: 0, x: -20 }}\n                                        animate={{ opacity: 1, x: 0 }}\n                                        transition={{ delay: index * 0.1 }}\n                                      >\n                                        <div className=\"finding-header\">\n                                          <div\n                                            className=\"finding-icon\"\n                                            style={{\n                                              background: finding.type === 'regulatory' ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)' :\n                                                         finding.type === 'financial' ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)' :\n                                                         finding.type === 'competitive' ? 'linear-gradient(135deg, #0066cc 0%, #3b82f6 100%)' :\n                                                         finding.type === 'market' ? 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)' :\n                                                         finding.type === 'technology' ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)' :\n                                                         finding.type === 'environmental' ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)' :\n                                                         finding.type === 'social' ? 'linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)' :\n                                                         finding.type === 'governance' ? 'linear-gradient(135deg, #4338ca 0%, #6366f1 100%)' :\n                                                         'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)'\n                                            }}\n                                          >\n                                            {finding.type === 'regulatory' && <AlertTriangle size={20} />}\n                                            {finding.type === 'financial' && <TrendingUp size={20} />}\n                                            {finding.type === 'competitive' && <Users size={20} />}\n                                            {finding.type === 'market' && <TrendingUp size={20} />}\n                                            {finding.type === 'technology' && <Info size={20} />}\n                                            {finding.type === 'environmental' && <AlertTriangle size={20} />}\n                                            {finding.type === 'social' && <Users size={20} />}\n                                            {finding.type === 'governance' && <Info size={20} />}\n                                          </div>\n                                          <div className=\"finding-title\">{finding.title}</div>\n                                          <div className=\"finding-badges\">\n                                            <div className={`impact-badge ${finding.impact?.toLowerCase()}`}>\n                                              {finding.impact} Impact\n                                            </div>\n                                            {finding.urgency && (\n                                              <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>\n                                                {finding.urgency}\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                        <p className=\"finding-description\">{finding.description}</p>\n                                        {finding.details && (\n                                          <div className=\"finding-details\">\n                                            <p>{finding.details}</p>\n                                          </div>\n                                        )}\n                                        {finding.timeline && (\n                                          <div className=\"finding-timeline\">\n                                            <strong>Timeline:</strong> {finding.timeline}\n                                          </div>\n                                        )}\n                                        {finding.confidence && (\n                                          <div className=\"confidence-bar\">\n                                            <div className=\"confidence-label\">Confidence: {finding.confidence}%</div>\n                                            <div className=\"confidence-progress\">\n                                              <motion.div\n                                                className=\"confidence-fill\"\n                                                initial={{ width: 0 }}\n                                                animate={{ width: `${finding.confidence}%` }}\n                                                transition={{ duration: 1, delay: 0.5 }}\n                                              />\n                                            </div>\n                                          </div>\n                                        )}\n                                      </motion.div>\n                                    ))}\n                                  </div>\n                                </motion.div>\n                              )}\n                            </div>\n                          )}\n\n                          {/* Sources Section */}\n                          <div className=\"collapsible-section\">\n                            <button\n                              className=\"section-toggle\"\n                              onClick={() => toggleSection(message.id, 'sources')}\n                            >\n                              <span>Sources & References ({message.content.sources?.length || 0})</span>\n                              {expandedSections[`${message.id}-sources`] ?\n                                <ChevronUp size={16} /> : <ChevronDown size={16} />\n                              }\n                            </button>\n\n                            {expandedSections[`${message.id}-sources`] && (\n                              <motion.div\n                                className=\"section-content\"\n                                initial={{ opacity: 0, height: 0 }}\n                                animate={{ opacity: 1, height: 'auto' }}\n                                exit={{ opacity: 0, height: 0 }}\n                              >\n                                <div className=\"sources-section\">\n                                  <div className=\"sources-grid\">\n                                    {message.content.sources?.map((source, index) => (\n                                      <div key={index} className=\"source-card\">\n                                        <div className=\"source-header\">\n                                          <ExternalLink size={14} />\n                                          <span className=\"source-title\">{source.title}</span>\n                                          {source.confidence && (\n                                            <span className={`source-confidence ${source.confidence.toLowerCase()}`}>\n                                              {source.confidence}\n                                            </span>\n                                          )}\n                                        </div>\n                                        <div className=\"source-meta\">\n                                          <span className=\"source-url\">{source.url}</span>\n                                          <span className=\"source-date\">{source.date}</span>\n                                          <span className=\"source-type\">{source.type}</span>\n                                        </div>\n                                      </div>\n                                    ))}\n                                  </div>\n                                </div>\n                              </motion.div>\n                            )}\n                          </div>\n                        </div>\n\n\n                      </div>\n                    ) : (\n                      <div className=\"simple-response\">{message.content}</div>\n                    )}\n                  </div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            className=\"loading-message\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"loading-content\">\n              <Loader2 className=\"loading-spinner\" size={20} />\n              <span>Analyzing ESG data and regulations...</span>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"message-input-container\">\n        <div className=\"message-input-box\">\n          <textarea\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask a follow-up question about ESG intelligence...\"\n            className=\"message-input\"\n            rows=\"1\"\n          />\n          <motion.button\n            className=\"send-btn\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || isLoading}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Send size={18} />\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ConversationView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,cAAc;AACrB,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMmC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMkC,aAAa,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC5CJ,mBAAmB,CAACK,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE,GAAG,CAACC,IAAI,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAN,cAAc,CAACO,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED3C,SAAS,CAAC,MAAM;IACduC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;EAEd1B,SAAS,CAAC,MAAM;IACd,IAAIuB,YAAY,EAAE;MAChB;MACA,MAAMqB,WAAW,GAAG;QAClBC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAExB,YAAY;QACrByB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDtB,WAAW,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC1Bb,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAmB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAAC7B,YAAY,CAAC;UAC3CyB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CpB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAElB,MAAM6B,oBAAoB,GAAIC,KAAK,IAAK;IACtC;IACA,MAAMC,gBAAgB,GAAID,KAAK,IAAK;MAClC,MAAME,UAAU,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;MAEtC,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpG,OAAOC,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIH,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvE,OAAOE,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIJ,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC9E,OAAOG,6BAA6B,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIL,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC5E,OAAOI,wBAAwB,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIN,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxE,OAAOK,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOC,8BAA8B,CAAC,CAAC;MACzC;IACF,CAAC;IAED,OAAOT,gBAAgB,CAACD,KAAK,CAAC;EAChC,CAAC;EAED,MAAMK,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLM,UAAU,EAAE,iCAAiC;MAC7CC,OAAO,EAAE,qDAAqD;MAC9DC,MAAM,EAAE,uEAAuE;MAC/EC,OAAO,EAAE,kCAAkC;MAC3CC,WAAW,EAAE,8DAA8D;MAC3EC,WAAW,EAAE,CACX;QACEvB,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,yCAAyC;QAChDJ,MAAM,EAAE,UAAU;QAClBK,WAAW,EAAE,uEAAuE;QACpFC,MAAM,EAAE;MACV,CAAC,EACD;QACE1B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,iDAAiD;QAC9DC,MAAM,EAAE;MACV,CAAC,EACD;QACE1B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,0BAA0B;QACjCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,mDAAmD;QAChEC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,gBAAgB,EAAE,CAChB;QACE3B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,uDAAuD;QAC9DJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,mJAAmJ;QAChKG,OAAO,EAAE,8MAA8M;QACvNC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,iDAAiD;QAC3DC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE/B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,qHAAqH;QAClIG,OAAO,EAAE,0LAA0L;QACnMC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,8BAA8B;QACxCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE/B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,6BAA6B;QACpCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,6GAA6G;QAC1HG,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,+BAA+B;QACzCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE/B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,gDAAgD;QACvDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,kNAAkN;QAC3NC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,8CAA8C;QACxDC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACE/B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,gDAAgD;QACvDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,wHAAwH;QACrIG,OAAO,EAAE,sNAAsN;QAC/NC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,oCAAoC;QAC9CC,iBAAiB,EAAE;MACrB,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,yBAAyB;QACxCC,kBAAkB,EAAE,YAAY;QAChCC,qBAAqB,EAAE,yBAAyB;QAChDC,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;QACxFC,MAAM,EAAE,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,8BAA8B,CAAC;QACxGC,GAAG,EAAE,CAAC,qBAAqB,EAAE,+BAA+B;MAC9D,CAAC;MACDC,SAAS,EAAE,CACT;QACEC,QAAQ,EAAE,UAAU;QACpBhB,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,cAAc;QACxBa,UAAU,EAAE,KAAK;QACjBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,UAAU;QACpBa,UAAU,EAAE,WAAW;QACvBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,iCAAiC;QACzCI,QAAQ,EAAE,WAAW;QACrBa,UAAU,EAAE,OAAO;QACnBlB,WAAW,EAAE;MACf,CAAC,CACF;MACDmB,kBAAkB,EAAE,CAClB;QACEF,QAAQ,EAAE,UAAU;QACpBhB,MAAM,EAAE,+CAA+C;QACvDI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,KAAK;QACjBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,mDAAmD;QAC3DI,QAAQ,EAAE,oBAAoB;QAC9Ba,UAAU,EAAE,WAAW;QACvBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,OAAO;QACnBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,QAAQ;QAClBhB,MAAM,EAAE,kCAAkC;QAC1CI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,WAAW;QACvBlB,WAAW,EAAE;MACf,CAAC,CACF;MACDoB,uBAAuB,EAAE,CACvB;QACEC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,qEAAqE;QAC/EC,SAAS,EAAE,uDAAuD;QAClEC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,6DAA6D;QACvEC,SAAS,EAAE,iDAAiD;QAC5DC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE,4DAA4D;QACtEC,SAAS,EAAE,qDAAqD;QAChEC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,OAAO,EAAE,CACP;QAAE1B,KAAK,EAAE,2CAA2C;QAAE2B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAChJ;QAAEL,KAAK,EAAE,qDAAqD;QAAE2B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAChJ;QAAEL,KAAK,EAAE,wCAAwC;QAAE2B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAChI;QAAEL,KAAK,EAAE,kCAAkC;QAAE2B,GAAG,EAAE,oBAAoB;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAClI;QAAEL,KAAK,EAAE,wCAAwC;QAAE2B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAS,CAAC,EACtI;QAAEL,KAAK,EAAE,uCAAuC;QAAE2B,GAAG,EAAE,aAAa;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,QAAQ;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAElI,CAAC;EACH,CAAC;EAED,MAAMhB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACLK,UAAU,EAAE,2DAA2D;MACvEmC,gBAAgB,EAAE,qRAAqR;MACvSC,WAAW,EAAE,CACX;QACEtD,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,6IAA6I;QACtJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uGAAuG;QACpHG,OAAO,EAAE,0JAA0J;QACnKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,gFAAgF;QAC7FG,OAAO,EAAE,kJAAkJ;QAC3JC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,8CAA8C;QACrDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0GAA0G;QACvHG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,wCAAwC;QAC/CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDE,YAAY,EAAE;QACZC,aAAa,EAAE,2BAA2B;QAC1CC,kBAAkB,EAAE,4BAA4B;QAChDC,qBAAqB,EAAE,kCAAkC;QACzDC,iBAAiB,EAAE;MACrB,CAAC;MACDmB,wBAAwB,EAAE,CACxB;QACEb,QAAQ,EAAE,UAAU;QACpBhB,MAAM,EAAE,kDAAkD;QAC1DI,QAAQ,EAAE,oBAAoB;QAC9Ba,UAAU,EAAE,MAAM;QAClBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,yCAAyC;QACjDI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,OAAO;QACnBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,0CAA0C;QAClDI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,KAAK;QACjBlB,WAAW,EAAE;MACf,CAAC,CACF;MACDyB,OAAO,EAAE,CACP;QAAE1B,KAAK,EAAE,6BAA6B;QAAE2B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,+CAA+C;QAAE2B,GAAG,EAAE,+BAA+B;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAC9J;QAAEL,KAAK,EAAE,8CAA8C;QAAE2B,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMZ,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,OAAO;MACLC,UAAU,EAAE,uCAAuC;MACnDmC,gBAAgB,EAAE,oWAAoW;MACtXC,WAAW,EAAE,CACX;QACEtD,IAAI,EAAE,eAAe;QACrBwB,KAAK,EAAE,sCAAsC;QAC7CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,uKAAuK;QAChLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uHAAuH;QACpIG,OAAO,EAAE,sKAAsK;QAC/KC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,4CAA4C;QACnDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,4GAA4G;QACzHG,OAAO,EAAE,6JAA6J;QACtKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,iDAAiD;QACxDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,+JAA+J;QACxKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,iCAAiC;QACxCJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,gJAAgJ;QACzJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDyB,wBAAwB,EAAE,CACxB;QACEb,QAAQ,EAAE,UAAU;QACpBhB,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,KAAK;QACjBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,8CAA8C;QACtDI,QAAQ,EAAE,oBAAoB;QAC9Ba,UAAU,EAAE,OAAO;QACnBlB,WAAW,EAAE;MACf,CAAC,EACD;QACEiB,QAAQ,EAAE,MAAM;QAChBhB,MAAM,EAAE,2CAA2C;QACnDI,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,OAAO;QACnBlB,WAAW,EAAE;MACf,CAAC,CACF;MACDyB,OAAO,EAAE,CACP;QAAE1B,KAAK,EAAE,qCAAqC;QAAE2B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,QAAQ;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,oCAAoC;QAAE2B,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAC3H;QAAEL,KAAK,EAAE,oCAAoC;QAAE2B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAS,CAAC;IAEtI,CAAC;EACH,CAAC;EAED,MAAMf,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,OAAO;MACLI,UAAU,EAAE,sCAAsC;MAClDmC,gBAAgB,EAAE,sQAAsQ;MACxRC,WAAW,EAAE,CACX;QACEtD,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,qCAAqC;QAC5CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0EAA0E;QACvFG,OAAO,EAAE,wIAAwI;QACjJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDoB,OAAO,EAAE,CACP;QAAE1B,KAAK,EAAE,oDAAoD;QAAE2B,GAAG,EAAE,8BAA8B;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAElK,CAAC;EACH,CAAC;EAED,MAAMd,wBAAwB,GAAGA,CAAA,KAAM;IACrC,OAAO;MACLG,UAAU,EAAE,mCAAmC;MAC/CmC,gBAAgB,EAAE,kPAAkP;MACpQC,WAAW,EAAE,CACX;QACEtD,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,wHAAwH;QACrIG,OAAO,EAAE,wHAAwH;QACjIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDoB,OAAO,EAAE,CACP;QAAE1B,KAAK,EAAE,oCAAoC;QAAE2B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEhI,CAAC;EACH,CAAC;EAED,MAAMb,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLE,UAAU,EAAE,kCAAkC;MAC9CmC,gBAAgB,EAAE,gPAAgP;MAClQC,WAAW,EAAE,CACX;QACEtD,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,0HAA0H;QACnIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDoB,OAAO,EAAE,CACP;QAAE1B,KAAK,EAAE,uCAAuC;QAAE2B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAM2B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI1E,UAAU,CAAC2E,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM3D,WAAW,GAAG;QAClBC,EAAE,EAAEnB,QAAQ,CAAC8E,MAAM,GAAG,CAAC;QACvB1D,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEnB,UAAU;QACnBoB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,WAAW,CAAC,CAAC;MAC3Cf,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAmB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAEnB,QAAQ,CAAC8E,MAAM,GAAG,CAAC;UACvB1D,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAACxB,UAAU,CAAC;UACzCoB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CpB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAM0E,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,WAAW,GAAIlE,OAAO,IAAK;IAC/BmE,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,OAAOrE,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGsE,IAAI,CAACC,SAAS,CAACvE,OAAO,CAAC,CAAC;EAChG,CAAC;EAED,oBACE1B,OAAA,CAACnB,MAAM,CAACqH,GAAG;IACTC,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAC7BG,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9B3G,OAAA;MAAKmG,SAAS,EAAC,qBAAqB;MAAAQ,QAAA,gBAClC3G,OAAA,CAACnB,MAAM,CAAC+H,MAAM;QACZT,SAAS,EAAC,UAAU;QACpBU,OAAO,EAAE1G,MAAO;QAChB2G,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAJ,QAAA,gBAE1B3G,OAAA,CAACjB,SAAS;UAACkI,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBrH,OAAA;QAAKmG,SAAS,EAAC,sBAAsB;QAAAQ,QAAA,gBACnC3G,OAAA,CAACnB,MAAM,CAAC+H,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAEpB,WAAY;UACrBqB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B3G,OAAA,CAACf,QAAQ;YAACgI,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChBrH,OAAA,CAACnB,MAAM,CAAC+H,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B3G,OAAA,CAACR,MAAM;YAACyH,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA;MAAKmG,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,gBACjC3G,OAAA,CAAClB,eAAe;QAAA6H,QAAA,EACbtG,QAAQ,CAACiH,GAAG,CAAEC,OAAO;UAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UAAA,oBACpB3H,OAAA,CAACnB,MAAM,CAACqH,GAAG;YAETC,SAAS,EAAE,WAAWoB,OAAO,CAAC9F,IAAI,EAAG;YACrC2E,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEuB,CAAC,EAAE;YAAG,CAAE;YAC/BrB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEuB,CAAC,EAAE;YAAE,CAAE;YAC9BpB,IAAI,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAEuB,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BnB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAC,QAAA,EAE7BY,OAAO,CAAC9F,IAAI,KAAK,MAAM,gBACtBzB,OAAA;cAAKmG,SAAS,EAAC,cAAc;cAAAQ,QAAA,gBAC3B3G,OAAA;gBAAKmG,SAAS,EAAC,iBAAiB;gBAAAQ,QAAA,EAAEY,OAAO,CAAC7F;cAAO;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDrH,OAAA;gBAAKmG,SAAS,EAAC,cAAc;gBAAAQ,QAAA,EAC1BY,OAAO,CAAC5F,SAAS,CAACkG,kBAAkB,CAAC,EAAE,EAAE;kBAAEC,IAAI,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAU,CAAC;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENrH,OAAA;cAAKmG,SAAS,EAAC,YAAY;cAAAQ,QAAA,gBACzB3G,OAAA;gBAAKmG,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EACzB,OAAOY,OAAO,CAAC7F,OAAO,KAAK,QAAQ,gBAClC1B,OAAA;kBAAKmG,SAAS,EAAC,gCAAgC;kBAAAQ,QAAA,gBAC7C3G,OAAA;oBAAKmG,SAAS,EAAC,eAAe;oBAAAQ,QAAA,eAC5B3G,OAAA;sBAAKmG,SAAS,EAAC,sBAAsB;sBAAAQ,QAAA,gBACnC3G,OAAA;wBAAA2G,QAAA,EAAKY,OAAO,CAAC7F,OAAO,CAACiB,UAAU,IAAI;sBAAyB;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClErH,OAAA;wBAAKmG,SAAS,EAAC,gBAAgB;wBAAAQ,QAAA,eAC7B3G,OAAA;0BACEmG,SAAS,EAAC,oBAAoB;0BAC9BU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC2B,OAAO,CAAC7F,OAAO,CAAE;0BAC5CuB,KAAK,EAAC,aAAa;0BAAA0D,QAAA,eAEnB3G,OAAA,CAACT,IAAI;4BAAC0H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrH,OAAA;oBAAKmG,SAAS,EAAC,0BAA0B;oBAAAQ,QAAA,gBACvC3G,OAAA;sBAAKmG,SAAS,EAAC,mBAAmB;sBAAAQ,QAAA,gBAChC3G,OAAA;wBAAKmG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,eAC3B3G,OAAA,CAACZ,aAAa;0BAAC6H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACNrH,OAAA;wBAAKmG,SAAS,EAAC,iBAAiB;wBAAAQ,QAAA,gBAC9B3G,OAAA;0BAAA2G,QAAA,EAAI;wBAAkB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3BrH,OAAA;0BAAA2G,QAAA,EAAIY,OAAO,CAAC7F,OAAO,CAACkB;wBAAO;0BAAAsE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAChCrH,OAAA;0BAAKmG,SAAS,EAAC,kBAAkB;0BAAAQ,QAAA,gBAC/B3G,OAAA;4BAAMmG,SAAS,EAAC,aAAa;4BAAAQ,QAAA,EAAEY,OAAO,CAAC7F,OAAO,CAACmB;0BAAM;4BAAAqE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAC7DrH,OAAA;4BAAMmG,SAAS,EAAC,cAAc;4BAAAQ,QAAA,EAAEY,OAAO,CAAC7F,OAAO,CAACoB;0BAAO;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAELE,OAAO,CAAC7F,OAAO,CAACqB,WAAW,iBAC1B/C,OAAA;sBAAKmG,SAAS,EAAC,uBAAuB;sBAAAQ,QAAA,gBACpC3G,OAAA;wBAAKmG,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,eAC/B3G,OAAA,CAACL,MAAM;0BAACsH,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACNrH,OAAA;wBAAKmG,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,gBAClC3G,OAAA;0BAAA2G,QAAA,EAAI;wBAAkB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3BrH,OAAA;0BAAA2G,QAAA,EAAIY,OAAO,CAAC7F,OAAO,CAACqB;wBAAW;0BAAAmE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAGNrH,OAAA;oBAAKmG,SAAS,EAAC,cAAc;oBAAAQ,QAAA,gBAC3B3G,OAAA;sBAAA2G,QAAA,EAAI;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BrH,OAAA;sBAAKmG,SAAS,EAAC,eAAe;sBAAAQ,QAAA,EAC3B,CAACY,OAAO,CAAC7F,OAAO,CAACsB,WAAW,MAAAwE,qBAAA,GAAID,OAAO,CAAC7F,OAAO,CAACqD,WAAW,cAAAyC,qBAAA,uBAA3BA,qBAAA,CAA6BQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,EAAEV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK;wBAAA,IAAAC,eAAA,EAAAC,gBAAA;wBAAA,oBAClGpI,OAAA,CAACnB,MAAM,CAACqH,GAAG;0BAETC,SAAS,EAAC,cAAc;0BACxBC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEuB,CAAC,EAAE;0BAAG,CAAE;0BAC/BrB,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEuB,CAAC,EAAE;0BAAE,CAAE;0BAC9BnB,UAAU,EAAE;4BAAE4B,KAAK,EAAEH,KAAK,GAAG;0BAAI,CAAE;0BAAAvB,QAAA,gBAEnC3G,OAAA;4BAAKmG,SAAS,EAAC,gBAAgB;4BAAAQ,QAAA,gBAC7B3G,OAAA;8BAAKmG,SAAS,EAAE,qBAAAgC,eAAA,GAAoBF,OAAO,CAACpF,MAAM,cAAAsF,eAAA,uBAAdA,eAAA,CAAgBhG,WAAW,CAAC,CAAC,EAAG;8BAAAwE,QAAA,GACjEsB,OAAO,CAACpF,MAAM,KAAK,UAAU,iBAAI7C,OAAA,CAACZ,aAAa;gCAAC6H,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EAC5DY,OAAO,CAACpF,MAAM,KAAK,MAAM,iBAAI7C,OAAA,CAACb,UAAU;gCAAC8H,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACrDY,OAAO,CAACpF,MAAM,KAAK,QAAQ,iBAAI7C,OAAA,CAACX,IAAI;gCAAC4H,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/C,CAAC,eACNrH,OAAA;8BAAMmG,SAAS,EAAE,iBAAAiC,gBAAA,GAAgBH,OAAO,CAACpF,MAAM,cAAAuF,gBAAA,uBAAdA,gBAAA,CAAgBjG,WAAW,CAAC,CAAC,EAAG;8BAAAwE,QAAA,EAC9DsB,OAAO,CAACpF;4BAAM;8BAAAqE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACNrH,OAAA;4BAAA2G,QAAA,EAAKsB,OAAO,CAAChF;0BAAK;4BAAAiE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACxBrH,OAAA;4BAAA2G,QAAA,EAAIsB,OAAO,CAAC/E;0BAAW;4BAAAgE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,EAC3BY,OAAO,CAAC9E,MAAM,iBACbnD,OAAA;4BAAKmG,SAAS,EAAC,cAAc;4BAAAQ,QAAA,gBAC3B3G,OAAA;8BAAA2G,QAAA,EAAQ;4BAAO;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAAC9E,MAAM;0BAAA;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrC,CACN;wBAAA,GAtBIa,KAAK;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuBA,CAAC;sBAAA,CACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrH,OAAA;oBAAKmG,SAAS,EAAC,YAAY;oBAAAQ,QAAA,gBACzB3G,OAAA;sBAAA2G,QAAA,EAAI;oBAAoB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7BrH,OAAA;sBAAKmG,SAAS,EAAC,YAAY;sBAAAQ,QAAA,EACxB,CAACY,OAAO,CAAC7F,OAAO,CAACwC,SAAS,MAAAuD,qBAAA,GAAIF,OAAO,CAAC7F,OAAO,CAACsD,wBAAwB,cAAAyC,qBAAA,uBAAxCA,qBAAA,CAA0CO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,EAAEV,GAAG,CAAC,CAACgB,IAAI,EAAEJ,KAAK;wBAAA,IAAAK,cAAA;wBAAA,oBAC1GvI,OAAA,CAACnB,MAAM,CAACqH,GAAG;0BAETC,SAAS,EAAC,WAAW;0BACrBC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAChCC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BG,UAAU,EAAE;4BAAE4B,KAAK,EAAE,GAAG,GAAGH,KAAK,GAAG;0BAAI,CAAE;0BAAAvB,QAAA,gBAEzC3G,OAAA;4BAAKmG,SAAS,EAAC,aAAa;4BAAAQ,QAAA,gBAC1B3G,OAAA;8BAAKmG,SAAS,EAAE,uBAAAoC,cAAA,GAAsBD,IAAI,CAACnE,QAAQ,cAAAoE,cAAA,uBAAbA,cAAA,CAAepG,WAAW,CAAC,CAAC,EAAG;8BAAAwE,QAAA,EAClEuB,KAAK,GAAG;4BAAC;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACNrH,OAAA;8BAAKmG,SAAS,EAAC,WAAW;8BAAAQ,QAAA,gBACxB3G,OAAA;gCAAMmG,SAAS,EAAC,eAAe;gCAAAQ,QAAA,gBAC7B3G,OAAA,CAACH,KAAK;kCAACoH,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EAClBiB,IAAI,CAAC/E,QAAQ;8BAAA;gCAAA2D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV,CAAC,eACPrH,OAAA;gCAAMmG,SAAS,EAAC,iBAAiB;gCAAAQ,QAAA,gBAC/B3G,OAAA,CAACJ,UAAU;kCAACqH,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EACvBiB,IAAI,CAAClE,UAAU;8BAAA;gCAAA8C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACNrH,OAAA;4BAAA2G,QAAA,EAAK2B,IAAI,CAACnF;0BAAM;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtBrH,OAAA;4BAAA2G,QAAA,EAAI2B,IAAI,CAACpF;0BAAW;4BAAAgE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA,GAtBpBa,KAAK;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuBA,CAAC;sBAAA,CACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrH,OAAA;oBAAKmG,SAAS,EAAC,mBAAmB;oBAAAQ,QAAA,GAE/B,CAACY,OAAO,CAAC7F,OAAO,CAAC0B,gBAAgB,IAAImE,OAAO,CAAC7F,OAAO,CAACqD,WAAW,kBAC/D/E,OAAA;sBAAKmG,SAAS,EAAC,qBAAqB;sBAAAQ,QAAA,gBAClC3G,OAAA;wBACEmG,SAAS,EAAC,gBAAgB;wBAC1BU,OAAO,EAAEA,CAAA,KAAM/F,aAAa,CAACyG,OAAO,CAAC/F,EAAE,EAAE,mBAAmB,CAAE;wBAAAmF,QAAA,gBAE9D3G,OAAA;0BAAA2G,QAAA,EAAM;wBAAiB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EAC7B1G,gBAAgB,CAAC,GAAG4G,OAAO,CAAC/F,EAAE,oBAAoB,CAAC,gBAClDxB,OAAA,CAACN,SAAS;0BAACuH,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGrH,OAAA,CAACP,WAAW;0BAACwH,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE/C,CAAC,EAER1G,gBAAgB,CAAC,GAAG4G,OAAO,CAAC/F,EAAE,oBAAoB,CAAC,iBAClDxB,OAAA,CAACnB,MAAM,CAACqH,GAAG;wBACTC,SAAS,EAAC,iBAAiB;wBAC3BC,OAAO,EAAE;0BAAEC,OAAO,EAAE,CAAC;0BAAEmC,MAAM,EAAE;wBAAE,CAAE;wBACnCjC,OAAO,EAAE;0BAAEF,OAAO,EAAE,CAAC;0BAAEmC,MAAM,EAAE;wBAAO,CAAE;wBACxChC,IAAI,EAAE;0BAAEH,OAAO,EAAE,CAAC;0BAAEmC,MAAM,EAAE;wBAAE,CAAE;wBAAA7B,QAAA,eAEhC3G,OAAA;0BAAKmG,SAAS,EAAC,mBAAmB;0BAAAQ,QAAA,EAC/B,CAACY,OAAO,CAAC7F,OAAO,CAAC0B,gBAAgB,IAAImE,OAAO,CAAC7F,OAAO,CAACqD,WAAW,IAAI,EAAE,EAAEuC,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK;4BAAA,IAAAO,gBAAA;4BAAA,oBAC1FzI,OAAA,CAACnB,MAAM,CAACqH,GAAG;8BAETC,SAAS,EAAE,gBAAgB8B,OAAO,CAACzE,iBAAiB,GAAG,wBAAwB,GAAG,EAAE,EAAG;8BACvF,aAAWyE,OAAO,CAACxG,IAAK;8BACxB2E,OAAO,EAAE;gCAAEC,OAAO,EAAE,CAAC;gCAAEC,CAAC,EAAE,CAAC;8BAAG,CAAE;8BAChCC,OAAO,EAAE;gCAAEF,OAAO,EAAE,CAAC;gCAAEC,CAAC,EAAE;8BAAE,CAAE;8BAC9BG,UAAU,EAAE;gCAAE4B,KAAK,EAAEH,KAAK,GAAG;8BAAI,CAAE;8BAAAvB,QAAA,gBAEnC3G,OAAA;gCAAKmG,SAAS,EAAC,gBAAgB;gCAAAQ,QAAA,gBAC7B3G,OAAA;kCACEmG,SAAS,EAAC,cAAc;kCACxBuC,KAAK,EAAE;oCACLC,UAAU,EAAEV,OAAO,CAACxG,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACpFwG,OAAO,CAACxG,IAAI,KAAK,WAAW,GAAG,mDAAmD,GAClFwG,OAAO,CAACxG,IAAI,KAAK,aAAa,GAAG,mDAAmD,GACpFwG,OAAO,CAACxG,IAAI,KAAK,QAAQ,GAAG,mDAAmD,GAC/EwG,OAAO,CAACxG,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACnFwG,OAAO,CAACxG,IAAI,KAAK,eAAe,GAAG,mDAAmD,GACtFwG,OAAO,CAACxG,IAAI,KAAK,QAAQ,GAAG,mDAAmD,GAC/EwG,OAAO,CAACxG,IAAI,KAAK,YAAY,GAAG,mDAAmD,GACnF;kCACb,CAAE;kCAAAkF,QAAA,GAEDsB,OAAO,CAACxG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACZ,aAAa;oCAAC6H,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EAC5DY,OAAO,CAACxG,IAAI,KAAK,WAAW,iBAAIzB,OAAA,CAACb,UAAU;oCAAC8H,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EACxDY,OAAO,CAACxG,IAAI,KAAK,aAAa,iBAAIzB,OAAA,CAACF,KAAK;oCAACmH,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EACrDY,OAAO,CAACxG,IAAI,KAAK,QAAQ,iBAAIzB,OAAA,CAACb,UAAU;oCAAC8H,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EACrDY,OAAO,CAACxG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACX,IAAI;oCAAC4H,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EACnDY,OAAO,CAACxG,IAAI,KAAK,eAAe,iBAAIzB,OAAA,CAACZ,aAAa;oCAAC6H,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EAC/DY,OAAO,CAACxG,IAAI,KAAK,QAAQ,iBAAIzB,OAAA,CAACF,KAAK;oCAACmH,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC,EAChDY,OAAO,CAACxG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACX,IAAI;oCAAC4H,IAAI,EAAE;kCAAG;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACjD,CAAC,eACNrH,OAAA;kCAAKmG,SAAS,EAAC,eAAe;kCAAAQ,QAAA,EAAEsB,OAAO,CAAChF;gCAAK;kCAAAiE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CAAC,eACpDrH,OAAA;kCAAKmG,SAAS,EAAC,gBAAgB;kCAAAQ,QAAA,gBAC7B3G,OAAA;oCAAKmG,SAAS,EAAE,iBAAAsC,gBAAA,GAAgBR,OAAO,CAACpF,MAAM,cAAA4F,gBAAA,uBAAdA,gBAAA,CAAgBtG,WAAW,CAAC,CAAC,EAAG;oCAAAwE,QAAA,GAC7DsB,OAAO,CAACpF,MAAM,EAAC,SAClB;kCAAA;oCAAAqE,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAK,CAAC,EACLY,OAAO,CAACnF,OAAO,iBACd9C,OAAA;oCAAKmG,SAAS,EAAE,iBAAiB8B,OAAO,CAACnF,OAAO,CAACX,WAAW,CAAC,CAAC,EAAG;oCAAAwE,QAAA,EAC9DsB,OAAO,CAACnF;kCAAO;oCAAAoE,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACb,CACN;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,eACNrH,OAAA;gCAAGmG,SAAS,EAAC,qBAAqB;gCAAAQ,QAAA,EAAEsB,OAAO,CAAC/E;8BAAW;gCAAAgE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC,EAC3DY,OAAO,CAAC5E,OAAO,iBACdrD,OAAA;gCAAKmG,SAAS,EAAC,iBAAiB;gCAAAQ,QAAA,eAC9B3G,OAAA;kCAAA2G,QAAA,EAAIsB,OAAO,CAAC5E;gCAAO;kCAAA6D,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrB,CACN,EACAY,OAAO,CAAC1E,QAAQ,iBACfvD,OAAA;gCAAKmG,SAAS,EAAC,kBAAkB;gCAAAQ,QAAA,gBAC/B3G,OAAA;kCAAA2G,QAAA,EAAQ;gCAAS;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAAC1E,QAAQ;8BAAA;gCAAA2D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACzC,CACN,EACAY,OAAO,CAAC3E,UAAU,iBACjBtD,OAAA;gCAAKmG,SAAS,EAAC,gBAAgB;gCAAAQ,QAAA,gBAC7B3G,OAAA;kCAAKmG,SAAS,EAAC,kBAAkB;kCAAAQ,QAAA,GAAC,cAAY,EAACsB,OAAO,CAAC3E,UAAU,EAAC,GAAC;gCAAA;kCAAA4D,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eACzErH,OAAA;kCAAKmG,SAAS,EAAC,qBAAqB;kCAAAQ,QAAA,eAClC3G,OAAA,CAACnB,MAAM,CAACqH,GAAG;oCACTC,SAAS,EAAC,iBAAiB;oCAC3BC,OAAO,EAAE;sCAAEwC,KAAK,EAAE;oCAAE,CAAE;oCACtBrC,OAAO,EAAE;sCAAEqC,KAAK,EAAE,GAAGX,OAAO,CAAC3E,UAAU;oCAAI,CAAE;oCAC7CmD,UAAU,EAAE;sCAAEC,QAAQ,EAAE,CAAC;sCAAE2B,KAAK,EAAE;oCAAI;kCAAE;oCAAAnB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACzC;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN;4BAAA,GAlEIa,KAAK;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAmEA,CAAC;0BAAA,CACd;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN,eAGDrH,OAAA;sBAAKmG,SAAS,EAAC,qBAAqB;sBAAAQ,QAAA,gBAClC3G,OAAA;wBACEmG,SAAS,EAAC,gBAAgB;wBAC1BU,OAAO,EAAEA,CAAA,KAAM/F,aAAa,CAACyG,OAAO,CAAC/F,EAAE,EAAE,SAAS,CAAE;wBAAAmF,QAAA,gBAEpD3G,OAAA;0BAAA2G,QAAA,GAAM,wBAAsB,EAAC,EAAAe,qBAAA,GAAAH,OAAO,CAAC7F,OAAO,CAACiD,OAAO,cAAA+C,qBAAA,uBAAvBA,qBAAA,CAAyBvC,MAAM,KAAI,CAAC,EAAC,GAAC;wBAAA;0BAAA+B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,EACzE1G,gBAAgB,CAAC,GAAG4G,OAAO,CAAC/F,EAAE,UAAU,CAAC,gBACxCxB,OAAA,CAACN,SAAS;0BAACuH,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGrH,OAAA,CAACP,WAAW;0BAACwH,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE/C,CAAC,EAER1G,gBAAgB,CAAC,GAAG4G,OAAO,CAAC/F,EAAE,UAAU,CAAC,iBACxCxB,OAAA,CAACnB,MAAM,CAACqH,GAAG;wBACTC,SAAS,EAAC,iBAAiB;wBAC3BC,OAAO,EAAE;0BAAEC,OAAO,EAAE,CAAC;0BAAEmC,MAAM,EAAE;wBAAE,CAAE;wBACnCjC,OAAO,EAAE;0BAAEF,OAAO,EAAE,CAAC;0BAAEmC,MAAM,EAAE;wBAAO,CAAE;wBACxChC,IAAI,EAAE;0BAAEH,OAAO,EAAE,CAAC;0BAAEmC,MAAM,EAAE;wBAAE,CAAE;wBAAA7B,QAAA,eAEhC3G,OAAA;0BAAKmG,SAAS,EAAC,iBAAiB;0BAAAQ,QAAA,eAC9B3G,OAAA;4BAAKmG,SAAS,EAAC,cAAc;4BAAAQ,QAAA,GAAAgB,sBAAA,GAC1BJ,OAAO,CAAC7F,OAAO,CAACiD,OAAO,cAAAgD,sBAAA,uBAAvBA,sBAAA,CAAyBL,GAAG,CAAC,CAACuB,MAAM,EAAEX,KAAK,kBAC1ClI,OAAA;8BAAiBmG,SAAS,EAAC,aAAa;8BAAAQ,QAAA,gBACtC3G,OAAA;gCAAKmG,SAAS,EAAC,eAAe;gCAAAQ,QAAA,gBAC5B3G,OAAA,CAACd,YAAY;kCAAC+H,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC1BrH,OAAA;kCAAMmG,SAAS,EAAC,cAAc;kCAAAQ,QAAA,EAAEkC,MAAM,CAAC5F;gCAAK;kCAAAiE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC,EACnDwB,MAAM,CAACvF,UAAU,iBAChBtD,OAAA;kCAAMmG,SAAS,EAAE,qBAAqB0C,MAAM,CAACvF,UAAU,CAACnB,WAAW,CAAC,CAAC,EAAG;kCAAAwE,QAAA,EACrEkC,MAAM,CAACvF;gCAAU;kCAAA4D,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACd,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eACNrH,OAAA;gCAAKmG,SAAS,EAAC,aAAa;gCAAAQ,QAAA,gBAC1B3G,OAAA;kCAAMmG,SAAS,EAAC,YAAY;kCAAAQ,QAAA,EAAEkC,MAAM,CAACjE;gCAAG;kCAAAsC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC,eAChDrH,OAAA;kCAAMmG,SAAS,EAAC,aAAa;kCAAAQ,QAAA,EAAEkC,MAAM,CAAChE;gCAAI;kCAAAqC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC,eAClDrH,OAAA;kCAAMmG,SAAS,EAAC,aAAa;kCAAAQ,QAAA,EAAEkC,MAAM,CAACpH;gCAAI;kCAAAyF,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/C,CAAC;4BAAA,GAdEa,KAAK;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAeV,CACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGH,CAAC,gBAENrH,OAAA;kBAAKmG,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,EAAEY,OAAO,CAAC7F;gBAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACxD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrH,OAAA;gBAAKmG,SAAS,EAAC,cAAc;gBAAAQ,QAAA,EAC1BY,OAAO,CAAC5F,SAAS,CAACkG,kBAAkB,CAAC,EAAE,EAAE;kBAAEC,IAAI,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAU,CAAC;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GA/RIE,OAAO,CAAC/F,EAAE;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgSL,CAAC;QAAA,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEjB5G,SAAS,iBACRT,OAAA,CAACnB,MAAM,CAACqH,GAAG;QACTC,SAAS,EAAC,iBAAiB;QAC3BC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEuB,CAAC,EAAE;QAAG,CAAE;QAC/BrB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEuB,CAAC,EAAE;QAAE,CAAE;QAAAjB,QAAA,eAE9B3G,OAAA;UAAKmG,SAAS,EAAC,iBAAiB;UAAAQ,QAAA,gBAC9B3G,OAAA,CAACV,OAAO;YAAC6G,SAAS,EAAC,iBAAiB;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDrH,OAAA;YAAA2G,QAAA,EAAM;UAAqC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDrH,OAAA;QAAK8I,GAAG,EAAEjI;MAAe;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNrH,OAAA;MAAKmG,SAAS,EAAC,yBAAyB;MAAAQ,QAAA,eACtC3G,OAAA;QAAKmG,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChC3G,OAAA;UACE+I,KAAK,EAAExI,UAAW;UAClByI,QAAQ,EAAG3D,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAE9D,cAAe;UAC3B+D,WAAW,EAAC,oDAAoD;UAChEhD,SAAS,EAAC,eAAe;UACzBiD,IAAI,EAAC;QAAG;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFrH,OAAA,CAACnB,MAAM,CAAC+H,MAAM;UACZT,SAAS,EAAC,UAAU;UACpBU,OAAO,EAAE5B,iBAAkB;UAC3BoE,QAAQ,EAAE,CAAC9I,UAAU,CAAC2E,IAAI,CAAC,CAAC,IAAIzE,SAAU;UAC1CqG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,eAE1B3G,OAAA,CAAChB,IAAI;YAACiI,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACjH,EAAA,CA15BIH,gBAAgB;AAAAqJ,EAAA,GAAhBrJ,gBAAgB;AA45BtB,eAAeA,gBAAgB;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}