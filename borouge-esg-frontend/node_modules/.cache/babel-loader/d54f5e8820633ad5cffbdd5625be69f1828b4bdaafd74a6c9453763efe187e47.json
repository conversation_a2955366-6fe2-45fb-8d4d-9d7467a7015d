{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowLeft, Send, Download, ExternalLink, TrendingUp, AlertTriangle, Info, Loader2, Copy, Share2, ChevronDown, ChevronUp, Target, DollarSign, Clock, Users } from 'lucide-react';\nimport './ConversationView.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationView = ({\n  initialQuery,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n  const generateMockResponse = query => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = query => {\n      const lowerQuery = query.toLowerCase();\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n    return getReportByQuery(query);\n  };\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [{\n        type: \"regulatory\",\n        title: \"Mandatory Recycled Content Requirements\",\n        impact: \"Critical\",\n        description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n        action: \"Secure recycling partnerships immediately\"\n      }, {\n        type: \"financial\",\n        title: \"Investment Requirements\",\n        impact: \"High\",\n        description: \"$800M-1.2B needed for compliance infrastructure\",\n        action: \"Establish dedicated compliance budget\"\n      }, {\n        type: \"competitive\",\n        title: \"SABIC Competitive Threat\",\n        impact: \"High\",\n        description: \"Risk losing 15-20% EU market share to competitors\",\n        action: \"Accelerate sustainable product development\"\n      }],\n      detailedFindings: [{\n        type: \"regulatory\",\n        title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n        details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n        confidence: 98,\n        timeline: \"Implementation: Jan 2025, Full compliance: 2030\"\n      }, {\n        type: \"financial\",\n        title: \"Compliance Investment Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n        details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2027\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Landscape Shift\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n        details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n        confidence: 92,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [{\n        priority: \"Critical\",\n        action: \"Form EU Compliance Task Force\",\n        timeline: \"Next 30 days\",\n        investment: \"$5M\",\n        description: \"Immediate action team to coordinate regulatory response\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Recycling Partnerships\",\n        timeline: \"6 months\",\n        investment: \"$200-300M\",\n        description: \"Lock in technology partnerships before competitors\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line\",\n        timeline: \"12 months\",\n        investment: \"$150M\",\n        description: \"Develop premium recycled content products\"\n      }],\n      allRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish EU Regulatory Compliance Task Force\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$5M\",\n        description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Chemical Recycling Technology Partnerships\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$200-300M\",\n        description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line Development\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$150M\",\n        description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n      }, {\n        priority: \"Medium\",\n        action: \"Establish European Recycling Hub\",\n        timeline: \"24 months (Q4 2025)\",\n        investment: \"$400-500M\",\n        description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n      }],\n      competitiveBenchmarking: [{\n        company: \"SABIC\",\n        strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n        advantage: \"First-mover in chemical recycling, strong EU presence\",\n        weakness: \"Higher cost base, limited feedstock security\"\n      }, {\n        company: \"Dow\",\n        strategy: \"Advanced recycling partnerships, circular design principles\",\n        advantage: \"Technology leadership, established partnerships\",\n        weakness: \"Focus on specialty applications, limited commodity exposure\"\n      }, {\n        company: \"LyondellBasell\",\n        strategy: \"Molecular recycling technology, circular economy solutions\",\n        advantage: \"Integrated technology development, scale advantages\",\n        weakness: \"Limited EU manufacturing footprint\"\n      }],\n      sources: [{\n        title: \"EU Packaging & Packaging Waste Regulation\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2024-01-15\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Environment Agency Circular Economy Report\",\n        url: \"eea.europa.eu\",\n        date: \"2024-02-01\",\n        type: \"research\",\n        confidence: \"High\"\n      }, {\n        title: \"SABIC Circular Economy Strategy Update\",\n        url: \"sabic.com\",\n        date: \"2024-01-30\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"Plastics Europe Market Data 2024\",\n        url: \"plasticseurope.org\",\n        date: \"2024-02-15\",\n        type: \"industry\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey Circular Economy in Chemicals\",\n        url: \"mckinsey.com\",\n        date: \"2024-01-20\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }, {\n        title: \"Wood Mackenzie Petrochemicals Outlook\",\n        url: \"woodmac.com\",\n        date: \"2024-02-10\",\n        type: \"market\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [{\n        type: \"financial\",\n        title: \"Direct CBAM Cost Impact\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n        details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n        confidence: 92,\n        timeline: \"Implementation: January 2026\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Advantage Opportunity\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n        details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n        confidence: 88,\n        timeline: \"Advantage period: 2026-2035\"\n      }, {\n        type: \"technology\",\n        title: \"Carbon Reduction Investment Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n        details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2030\"\n      }, {\n        type: \"regulatory\",\n        title: \"CBAM Reporting and Verification Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n        details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n        confidence: 95,\n        timeline: \"Reporting starts: October 2023\"\n      }, {\n        type: \"market\",\n        title: \"Premium Low-Carbon Product Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n        details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n        confidence: 78,\n        timeline: \"Market development: 2025-2030\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Implement Comprehensive Carbon Accounting System\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$10M\",\n        description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n      }, {\n        priority: \"High\",\n        action: \"Accelerate Renewable Energy Integration\",\n        timeline: \"18 months (Q3 2025)\",\n        investment: \"$200M\",\n        description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n      }, {\n        priority: \"High\",\n        action: \"Develop Low-Carbon Product Certification\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$5M\",\n        description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n      }],\n      sources: [{\n        title: \"EU CBAM Regulation 2023/956\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2023-05-17\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Commission CBAM Implementation Guide\",\n        url: \"taxation-customs.ec.europa.eu\",\n        date: \"2024-01-10\",\n        type: \"guidance\",\n        confidence: \"Official\"\n      }, {\n        title: \"IEA Petrochemicals Carbon Intensity Database\",\n        url: \"iea.org\",\n        date: \"2024-02-05\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [{\n        type: \"environmental\",\n        title: \"Decarbonization Pathway Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n        details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n        confidence: 90,\n        timeline: \"Transformation period: 2024-2050\"\n      }, {\n        type: \"social\",\n        title: \"UAE Emiratization and Skills Development\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n        details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n        confidence: 85,\n        timeline: \"Achievement target: 2030\"\n      }, {\n        type: \"governance\",\n        title: \"ESG Reporting and Transparency Enhancement\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n        details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n        confidence: 88,\n        timeline: \"Full compliance: 2025\"\n      }, {\n        type: \"financial\",\n        title: \"Sustainable Finance and Green Bonds Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n        details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n        confidence: 82,\n        timeline: \"Financing window: 2024-2027\"\n      }, {\n        type: \"technology\",\n        title: \"Digital ESG Management Platform\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n        details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n        confidence: 75,\n        timeline: \"Implementation: 2024-2025\"\n      }],\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish Chief Sustainability Officer Role\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$2M\",\n        description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Comprehensive Decarbonization Program\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$500M\",\n        description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n      }, {\n        priority: \"High\",\n        action: \"Implement Advanced Emiratization Strategy\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$100M\",\n        description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n      }],\n      sources: [{\n        title: \"UAE Vision 2071 Strategic Framework\",\n        url: \"government.ae\",\n        date: \"2023-12-01\",\n        type: \"policy\",\n        confidence: \"Official\"\n      }, {\n        title: \"ADNOC Sustainability Strategy 2030\",\n        url: \"adnoc.ae\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey ESG in Chemicals Industry\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }]\n    };\n  };\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Recycled Polyethylene Market Growth\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n        details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n        confidence: 88,\n        timeline: \"Market expansion: 2024-2030\"\n      }],\n      sources: [{\n        title: \"Ellen MacArthur Foundation Circular Economy Report\",\n        url: \"ellenmacarthurfoundation.org\",\n        date: \"2024-01-10\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [{\n        type: \"competitive\",\n        title: \"SABIC Circular Economy Leadership\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n        details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n        confidence: 95,\n        timeline: \"Competitive threat: Immediate\"\n      }],\n      sources: [{\n        title: \"SABIC Sustainability Strategy 2030\",\n        url: \"sabic.com\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [{\n        type: \"market\",\n        title: \"Sustainable Packaging Demand Surge\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n        details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n        confidence: 90,\n        timeline: \"Market shift: 2024-2027\"\n      }],\n      sources: [{\n        title: \"McKinsey Sustainable Packaging Report\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n  const copyMessage = content => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"conversation-view\",\n    initial: {\n      opacity: 0,\n      x: 20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    exit: {\n      opacity: 0,\n      x: -20\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversation-header\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"back-btn\",\n        onClick: onBack,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this), \"Back to Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-actions\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          onClick: exportToPDF,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Share2, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), \"Share\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.map(message => {\n          var _message$content$keyF, _message$content$stra;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            className: `message ${message.type}`,\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-message\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-content\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-time\",\n                children: message.timestamp.toLocaleTimeString([], {\n                  hour: '2-digit',\n                  minute: '2-digit'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-message\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ai-response\",\n                children: typeof message.content === 'object' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"intelligence-report simplified\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"report-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"report-title-section\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: message.content.reportType || 'ESG Intelligence Report'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"report-actions\",\n                        children: /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"copy-btn secondary\",\n                          onClick: () => copyMessage(message.content),\n                          title: \"Copy report\",\n                          children: /*#__PURE__*/_jsxDEV(Copy, {\n                            size: 14\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 616,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 611,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"problem-solution-summary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"problem-statement\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"problem-icon\",\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 626,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 625,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"problem-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Business Challenge\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 629,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: message.content.problem\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 630,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"impact-highlight\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"impact-text\",\n                            children: message.content.impact\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 632,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"urgency-text\",\n                            children: message.content.urgency\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 633,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 631,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 27\n                    }, this), message.content.opportunity && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"opportunity-statement\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"opportunity-icon\",\n                        children: /*#__PURE__*/_jsxDEV(Target, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 641,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"opportunity-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Market Opportunity\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 644,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: message.content.opportunity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 645,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 639,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"key-insights\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Critical Findings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"insights-grid\",\n                      children: (message.content.topFindings || ((_message$content$keyF = message.content.keyFindings) === null || _message$content$keyF === void 0 ? void 0 : _message$content$keyF.slice(0, 3)) || []).map((finding, index) => {\n                        var _finding$impact, _finding$impact2;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"insight-card\",\n                          initial: {\n                            opacity: 0,\n                            y: 10\n                          },\n                          animate: {\n                            opacity: 1,\n                            y: 0\n                          },\n                          transition: {\n                            delay: index * 0.1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"insight-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `impact-indicator ${(_finding$impact = finding.impact) === null || _finding$impact === void 0 ? void 0 : _finding$impact.toLowerCase()}`,\n                              children: [finding.impact === 'Critical' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 665,\n                                columnNumber: 71\n                              }, this), finding.impact === 'High' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 666,\n                                columnNumber: 67\n                              }, this), finding.impact === 'Medium' && /*#__PURE__*/_jsxDEV(Info, {\n                                size: 16\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 667,\n                                columnNumber: 69\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 664,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `impact-label ${(_finding$impact2 = finding.impact) === null || _finding$impact2 === void 0 ? void 0 : _finding$impact2.toLowerCase()}`,\n                              children: finding.impact\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 669,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 663,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                            children: finding.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 673,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: finding.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 674,\n                            columnNumber: 33\n                          }, this), finding.action && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"quick-action\",\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Action:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 677,\n                              columnNumber: 37\n                            }, this), \" \", finding.action]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 676,\n                            columnNumber: 35\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 656,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"next-steps\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Immediate Next Steps\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"steps-list\",\n                      children: (message.content.nextSteps || ((_message$content$stra = message.content.strategicRecommendations) === null || _message$content$stra === void 0 ? void 0 : _message$content$stra.slice(0, 3)) || []).map((step, index) => {\n                        var _step$priority;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"step-card\",\n                          initial: {\n                            opacity: 0,\n                            x: -10\n                          },\n                          animate: {\n                            opacity: 1,\n                            x: 0\n                          },\n                          transition: {\n                            delay: 0.2 + index * 0.1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"step-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `priority-indicator ${(_step$priority = step.priority) === null || _step$priority === void 0 ? void 0 : _step$priority.toLowerCase()}`,\n                              children: index + 1\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 698,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"step-meta\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"step-timeline\",\n                                children: [/*#__PURE__*/_jsxDEV(Clock, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 703,\n                                  columnNumber: 39\n                                }, this), step.timeline]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 702,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"step-investment\",\n                                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 707,\n                                  columnNumber: 39\n                                }, this), step.investment]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 706,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 701,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 697,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                            children: step.action\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 712,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            children: step.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 713,\n                            columnNumber: 33\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 690,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"key-findings\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Key Findings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 27\n                    }, this), message.content.keyFindings.map((finding, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"finding-card\",\n                      initial: {\n                        opacity: 0,\n                        x: -20\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0\n                      },\n                      transition: {\n                        delay: index * 0.1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-header\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"finding-icon\",\n                          children: [finding.type === 'regulatory' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 731,\n                            columnNumber: 69\n                          }, this), finding.type === 'financial' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 732,\n                            columnNumber: 68\n                          }, this), finding.type === 'competitive' && /*#__PURE__*/_jsxDEV(Info, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 733,\n                            columnNumber: 70\n                          }, this), finding.type === 'market' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 734,\n                            columnNumber: 65\n                          }, this), finding.type === 'technology' && /*#__PURE__*/_jsxDEV(Info, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 735,\n                            columnNumber: 69\n                          }, this), finding.type === 'environmental' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 736,\n                            columnNumber: 72\n                          }, this), finding.type === 'social' && /*#__PURE__*/_jsxDEV(Info, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 737,\n                            columnNumber: 65\n                          }, this), finding.type === 'governance' && /*#__PURE__*/_jsxDEV(Info, {\n                            size: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 730,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"finding-title\",\n                          children: finding.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 740,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"finding-badges\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `impact-badge ${finding.impact.toLowerCase()}`,\n                            children: [finding.impact, \" Impact\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 742,\n                            columnNumber: 35\n                          }, this), finding.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `urgency-badge ${finding.urgency.toLowerCase()}`,\n                            children: finding.urgency\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 746,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 741,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 729,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"finding-description\",\n                        children: finding.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 752,\n                        columnNumber: 31\n                      }, this), finding.details && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-details\",\n                        children: /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: finding.details\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 33\n                      }, this), finding.timeline && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-timeline\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Timeline:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 760,\n                          columnNumber: 35\n                        }, this), \" \", finding.timeline]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 759,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"confidence-bar\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"confidence-label\",\n                          children: [\"Confidence: \", finding.confidence, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 764,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"confidence-progress\",\n                          children: /*#__PURE__*/_jsxDEV(motion.div, {\n                            className: \"confidence-fill\",\n                            initial: {\n                              width: 0\n                            },\n                            animate: {\n                              width: `${finding.confidence}%`\n                            },\n                            transition: {\n                              duration: 1,\n                              delay: 0.5\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 766,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 765,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 25\n                  }, this), message.content.strategicRecommendations && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"strategic-recommendations\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Strategic Recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 29\n                    }, this), message.content.strategicRecommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"recommendation-card\",\n                      initial: {\n                        opacity: 0,\n                        y: 10\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.3 + index * 0.1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"recommendation-header\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `priority-badge ${rec.priority.toLowerCase()}`,\n                          children: [rec.priority, \" Priority\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 790,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"recommendation-investment\",\n                          children: rec.investment\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 793,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 789,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"recommendation-action\",\n                        children: rec.action\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"recommendation-description\",\n                        children: rec.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"recommendation-timeline\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Timeline:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 35\n                        }, this), \" \", rec.timeline]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 797,\n                        columnNumber: 33\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 31\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 27\n                  }, this), message.content.competitiveBenchmarking && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"competitive-benchmarking\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Competitive Benchmarking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 807,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"competitors-grid\",\n                      children: message.content.competitiveBenchmarking.map((comp, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"competitor-card\",\n                        initial: {\n                          opacity: 0,\n                          scale: 0.95\n                        },\n                        animate: {\n                          opacity: 1,\n                          scale: 1\n                        },\n                        transition: {\n                          delay: 0.4 + index * 0.1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"competitor-name\",\n                          children: comp.company\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 817,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"competitor-strategy\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Strategy:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 819,\n                            columnNumber: 37\n                          }, this), \" \", comp.strategy]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 818,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"competitor-analysis\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"advantage\",\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Advantage:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 823,\n                              columnNumber: 39\n                            }, this), \" \", comp.advantage]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 822,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"weakness\",\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Weakness:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 826,\n                              columnNumber: 39\n                            }, this), \" \", comp.weakness]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 35\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 810,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 808,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 27\n                  }, this), message.content.riskAssessment && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"risk-assessment\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Risk Assessment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"risk-categories\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"risk-category high-risk\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          children: \"High Risk\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 840,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          children: message.content.riskAssessment.high.map((risk, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: risk\n                          }, index, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 843,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 841,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 839,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"risk-category medium-risk\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          children: \"Medium Risk\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 848,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          children: message.content.riskAssessment.medium.map((risk, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: risk\n                          }, index, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 851,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 849,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 847,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"risk-category low-risk\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          children: \"Low Risk\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 856,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          children: message.content.riskAssessment.low.map((risk, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: risk\n                          }, index, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 859,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 857,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 838,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"sources-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Sources & References\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"sources-grid\",\n                      children: message.content.sources.map((source, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"source-card\",\n                        initial: {\n                          opacity: 0,\n                          y: 10\n                        },\n                        animate: {\n                          opacity: 1,\n                          y: 0\n                        },\n                        transition: {\n                          delay: 0.3 + index * 0.1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"source-header\",\n                          children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                            size: 14\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 879,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"source-title\",\n                            children: source.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 880,\n                            columnNumber: 35\n                          }, this), source.confidence && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `source-confidence ${source.confidence.toLowerCase()}`,\n                            children: source.confidence\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 882,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 878,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"source-meta\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"source-url\",\n                            children: source.url\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 888,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"source-date\",\n                            children: source.date\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 889,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"source-type\",\n                            children: source.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 890,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 887,\n                          columnNumber: 33\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 871,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 25\n                  }, this), message.content.actionItems && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"action-items\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Quick Action Items\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      children: message.content.actionItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.li, {\n                        initial: {\n                          opacity: 0,\n                          x: -10\n                        },\n                        animate: {\n                          opacity: 1,\n                          x: 0\n                        },\n                        transition: {\n                          delay: 0.5 + index * 0.1\n                        },\n                        children: item\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 900,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"simple-response\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-time\",\n                children: message.timestamp.toLocaleTimeString([], {\n                  hour: '2-digit',\n                  minute: '2-digit'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-message\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(Loader2, {\n            className: \"loading-spinner\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Analyzing ESG data and regulations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 934,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 929,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-input-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-input-box\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask a follow-up question about ESG intelligence...\",\n          className: \"message-input\",\n          rows: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"send-btn\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || isLoading,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 946,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 945,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 543,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationView, \"Jfa2Q+54F4smiufyXm/+bCymPuA=\");\n_c = ConversationView;\nexport default ConversationView;\nvar _c;\n$RefreshReg$(_c, \"ConversationView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "ArrowLeft", "Send", "Download", "ExternalLink", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Loader2", "Copy", "Share2", "ChevronDown", "ChevronUp", "Target", "DollarSign", "Clock", "Users", "jsxDEV", "_jsxDEV", "ConversationView", "initialQuery", "onBack", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "isLoading", "setIsLoading", "expandedSections", "setExpandedSections", "messagesEndRef", "toggleSection", "messageId", "section", "prev", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "userMessage", "id", "type", "content", "timestamp", "Date", "setTimeout", "aiResponse", "generateMockResponse", "query", "getReportByQuery", "lowerQuery", "toLowerCase", "includes", "generateEURegulationReport", "generateCBAMReport", "generateCircularEconomyReport", "generateCompetitorReport", "generateMarketTrendsReport", "generateComprehensiveESGReport", "reportType", "problem", "impact", "urgency", "opportunity", "topFindings", "title", "description", "action", "detailedFindings", "details", "confidence", "timeline", "marketImpact", "revenueAtRisk", "investmentRequired", "timelineForCompliance", "marketOpportunity", "riskAssessment", "high", "medium", "low", "nextSteps", "priority", "investment", "allRecommendations", "competitiveBenchmarking", "company", "strategy", "advantage", "weakness", "sources", "url", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyFindings", "strategicRecommendations", "handleSendMessage", "trim", "length", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "exportToPDF", "console", "log", "copyMessage", "navigator", "clipboard", "writeText", "JSON", "stringify", "div", "className", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "children", "button", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "message", "_message$content$keyF", "_message$content$stra", "y", "toLocaleTimeString", "hour", "minute", "slice", "finding", "index", "_finding$impact", "_finding$impact2", "delay", "step", "_step$priority", "width", "rec", "comp", "risk", "source", "actionItems", "item", "li", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ArrowLeft,\n  Send,\n  Download,\n  ExternalLink,\n  TrendingUp,\n  AlertTriangle,\n  Info,\n  Loader2,\n  Copy,\n  Share2,\n  ChevronDown,\n  ChevronUp,\n  Target,\n  DollarSign,\n  Clock,\n  Users\n} from 'lucide-react';\nimport './ConversationView.css';\n\nconst ConversationView = ({ initialQuery, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [expandedSections, setExpandedSections] = useState({});\n  const messagesEndRef = useRef(null);\n\n  const toggleSection = (messageId, section) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [`${messageId}-${section}`]: !prev[`${messageId}-${section}`]\n    }));\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n\n  const generateMockResponse = (query) => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = (query) => {\n      const lowerQuery = query.toLowerCase();\n\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n\n    return getReportByQuery(query);\n  };\n\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"EU Packaging Regulations Impact\",\n      problem: \"New EU regulations threaten €2.1B in annual revenue\",\n      impact: \"65% of Borouge's EU exports affected by recycled content requirements\",\n      urgency: \"18 months to compliance deadline\",\n      opportunity: \"€150-250M premium pricing potential for sustainable products\",\n      topFindings: [\n        {\n          type: \"regulatory\",\n          title: \"Mandatory Recycled Content Requirements\",\n          impact: \"Critical\",\n          description: \"30% recycled content required by 2030, affecting €2.1B revenue stream\",\n          action: \"Secure recycling partnerships immediately\"\n        },\n        {\n          type: \"financial\",\n          title: \"Investment Requirements\",\n          impact: \"High\",\n          description: \"$800M-1.2B needed for compliance infrastructure\",\n          action: \"Establish dedicated compliance budget\"\n        },\n        {\n          type: \"competitive\",\n          title: \"SABIC Competitive Threat\",\n          impact: \"High\",\n          description: \"Risk losing 15-20% EU market share to competitors\",\n          action: \"Accelerate sustainable product development\"\n        }\n      ],\n      detailedFindings: [\n        {\n          type: \"regulatory\",\n          title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n          details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n          confidence: 98,\n          timeline: \"Implementation: Jan 2025, Full compliance: 2030\"\n        },\n        {\n          type: \"financial\",\n          title: \"Compliance Investment Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n          details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2027\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Landscape Shift\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n          details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n          confidence: 92,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      nextSteps: [\n        {\n          priority: \"Critical\",\n          action: \"Form EU Compliance Task Force\",\n          timeline: \"Next 30 days\",\n          investment: \"$5M\",\n          description: \"Immediate action team to coordinate regulatory response\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Recycling Partnerships\",\n          timeline: \"6 months\",\n          investment: \"$200-300M\",\n          description: \"Lock in technology partnerships before competitors\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line\",\n          timeline: \"12 months\",\n          investment: \"$150M\",\n          description: \"Develop premium recycled content products\"\n        }\n      ],\n      allRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish EU Regulatory Compliance Task Force\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$5M\",\n          description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Chemical Recycling Technology Partnerships\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$200-300M\",\n          description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line Development\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$150M\",\n          description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n        },\n        {\n          priority: \"Medium\",\n          action: \"Establish European Recycling Hub\",\n          timeline: \"24 months (Q4 2025)\",\n          investment: \"$400-500M\",\n          description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n        }\n      ],\n      competitiveBenchmarking: [\n        {\n          company: \"SABIC\",\n          strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n          advantage: \"First-mover in chemical recycling, strong EU presence\",\n          weakness: \"Higher cost base, limited feedstock security\"\n        },\n        {\n          company: \"Dow\",\n          strategy: \"Advanced recycling partnerships, circular design principles\",\n          advantage: \"Technology leadership, established partnerships\",\n          weakness: \"Focus on specialty applications, limited commodity exposure\"\n        },\n        {\n          company: \"LyondellBasell\",\n          strategy: \"Molecular recycling technology, circular economy solutions\",\n          advantage: \"Integrated technology development, scale advantages\",\n          weakness: \"Limited EU manufacturing footprint\"\n        }\n      ],\n      sources: [\n        { title: \"EU Packaging & Packaging Waste Regulation\", url: \"eur-lex.europa.eu\", date: \"2024-01-15\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Environment Agency Circular Economy Report\", url: \"eea.europa.eu\", date: \"2024-02-01\", type: \"research\", confidence: \"High\" },\n        { title: \"SABIC Circular Economy Strategy Update\", url: \"sabic.com\", date: \"2024-01-30\", type: \"corporate\", confidence: \"High\" },\n        { title: \"Plastics Europe Market Data 2024\", url: \"plasticseurope.org\", date: \"2024-02-15\", type: \"industry\", confidence: \"High\" },\n        { title: \"McKinsey Circular Economy in Chemicals\", url: \"mckinsey.com\", date: \"2024-01-20\", type: \"consulting\", confidence: \"Medium\" },\n        { title: \"Wood Mackenzie Petrochemicals Outlook\", url: \"woodmac.com\", date: \"2024-02-10\", type: \"market\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [\n        {\n          type: \"financial\",\n          title: \"Direct CBAM Cost Impact\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n          details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n          confidence: 92,\n          timeline: \"Implementation: January 2026\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Advantage Opportunity\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n          details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n          confidence: 88,\n          timeline: \"Advantage period: 2026-2035\"\n        },\n        {\n          type: \"technology\",\n          title: \"Carbon Reduction Investment Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n          details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2030\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"CBAM Reporting and Verification Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n          details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n          confidence: 95,\n          timeline: \"Reporting starts: October 2023\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Low-Carbon Product Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n          details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n          confidence: 78,\n          timeline: \"Market development: 2025-2030\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Implement Comprehensive Carbon Accounting System\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$10M\",\n          description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Accelerate Renewable Energy Integration\",\n          timeline: \"18 months (Q3 2025)\",\n          investment: \"$200M\",\n          description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Develop Low-Carbon Product Certification\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$5M\",\n          description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n        }\n      ],\n      sources: [\n        { title: \"EU CBAM Regulation 2023/956\", url: \"eur-lex.europa.eu\", date: \"2023-05-17\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Commission CBAM Implementation Guide\", url: \"taxation-customs.ec.europa.eu\", date: \"2024-01-10\", type: \"guidance\", confidence: \"Official\" },\n        { title: \"IEA Petrochemicals Carbon Intensity Database\", url: \"iea.org\", date: \"2024-02-05\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [\n        {\n          type: \"environmental\",\n          title: \"Decarbonization Pathway Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n          details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n          confidence: 90,\n          timeline: \"Transformation period: 2024-2050\"\n        },\n        {\n          type: \"social\",\n          title: \"UAE Emiratization and Skills Development\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n          details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n          confidence: 85,\n          timeline: \"Achievement target: 2030\"\n        },\n        {\n          type: \"governance\",\n          title: \"ESG Reporting and Transparency Enhancement\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n          details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n          confidence: 88,\n          timeline: \"Full compliance: 2025\"\n        },\n        {\n          type: \"financial\",\n          title: \"Sustainable Finance and Green Bonds Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n          details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n          confidence: 82,\n          timeline: \"Financing window: 2024-2027\"\n        },\n        {\n          type: \"technology\",\n          title: \"Digital ESG Management Platform\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n          details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n          confidence: 75,\n          timeline: \"Implementation: 2024-2025\"\n        }\n      ],\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish Chief Sustainability Officer Role\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$2M\",\n          description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Comprehensive Decarbonization Program\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$500M\",\n          description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Implement Advanced Emiratization Strategy\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$100M\",\n          description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n        }\n      ],\n      sources: [\n        { title: \"UAE Vision 2071 Strategic Framework\", url: \"government.ae\", date: \"2023-12-01\", type: \"policy\", confidence: \"Official\" },\n        { title: \"ADNOC Sustainability Strategy 2030\", url: \"adnoc.ae\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" },\n        { title: \"McKinsey ESG in Chemicals Industry\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"Medium\" }\n      ]\n    };\n  };\n\n  const generateCircularEconomyReport = () => {\n    return {\n      reportType: \"Circular Economy Transition Analysis\",\n      executiveSummary: \"Borouge's transition to circular economy principles presents a $1.5B investment opportunity with potential to capture 30% market share in recycled polyethylene by 2030. Strategic partnerships and technology investments are critical for competitive positioning.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Recycled Polyethylene Market Growth\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Global recycled PE market growing at 8.2% CAGR, reaching $15.6B by 2030.\",\n          details: \"Driven by regulatory requirements and brand commitments. Borouge could capture $4.7B market opportunity through strategic positioning.\",\n          confidence: 88,\n          timeline: \"Market expansion: 2024-2030\"\n        }\n      ],\n      sources: [\n        { title: \"Ellen MacArthur Foundation Circular Economy Report\", url: \"ellenmacarthurfoundation.org\", date: \"2024-01-10\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCompetitorReport = () => {\n    return {\n      reportType: \"Competitive Intelligence Analysis\",\n      executiveSummary: \"SABIC leads in circular economy investments with $2B commitment, while Dow focuses on advanced recycling partnerships. Borouge must accelerate sustainability initiatives to maintain competitive position in evolving petrochemicals landscape.\",\n      keyFindings: [\n        {\n          type: \"competitive\",\n          title: \"SABIC Circular Economy Leadership\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"SABIC's $2B circular economy investment and 1M tonnes recycled content target by 2030 positions them as market leader.\",\n          details: \"SABIC's TRUCIRCLE portfolio and partnerships with Plastic Energy create competitive advantage in sustainable products.\",\n          confidence: 95,\n          timeline: \"Competitive threat: Immediate\"\n        }\n      ],\n      sources: [\n        { title: \"SABIC Sustainability Strategy 2030\", url: \"sabic.com\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateMarketTrendsReport = () => {\n    return {\n      reportType: \"Market Trends & Outlook Analysis\",\n      executiveSummary: \"Petrochemicals market experiencing fundamental shift toward sustainability, with 60% of customers prioritizing low-carbon products. Borouge must adapt product portfolio and operations to capture emerging opportunities worth $3-5B by 2030.\",\n      keyFindings: [\n        {\n          type: \"market\",\n          title: \"Sustainable Packaging Demand Surge\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"85% of global brands committed to sustainable packaging by 2030, driving 15-25% premium pricing for certified products.\",\n          details: \"Major customers (Unilever, P&G, Nestlé) mandating recycled content. Market opportunity: $500M premium pricing potential.\",\n          confidence: 90,\n          timeline: \"Market shift: 2024-2027\"\n        }\n      ],\n      sources: [\n        { title: \"McKinsey Sustainable Packaging Report\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n\n  const copyMessage = (content) => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n\n  return (\n    <motion.div\n      className=\"conversation-view\"\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      exit={{ opacity: 0, x: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"conversation-header\">\n        <motion.button\n          className=\"back-btn\"\n          onClick={onBack}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <ArrowLeft size={20} />\n          Back to Search\n        </motion.button>\n\n        <div className=\"conversation-actions\">\n          <motion.button\n            className=\"action-btn\"\n            onClick={exportToPDF}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Download size={18} />\n            Export\n          </motion.button>\n          <motion.button\n            className=\"action-btn\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Share2 size={18} />\n            Share\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"messages-container\">\n        <AnimatePresence>\n          {messages.map((message) => (\n            <motion.div\n              key={message.id}\n              className={`message ${message.type}`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {message.type === 'user' ? (\n                <div className=\"user-message\">\n                  <div className=\"message-content\">{message.content}</div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"ai-message\">\n                  <div className=\"ai-response\">\n                    {typeof message.content === 'object' ? (\n                      <div className=\"intelligence-report simplified\">\n                        <div className=\"report-header\">\n                          <div className=\"report-title-section\">\n                            <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>\n                            <div className=\"report-actions\">\n                              <button\n                                className=\"copy-btn secondary\"\n                                onClick={() => copyMessage(message.content)}\n                                title=\"Copy report\"\n                              >\n                                <Copy size={14} />\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Problem-Solution Summary */}\n                        <div className=\"problem-solution-summary\">\n                          <div className=\"problem-statement\">\n                            <div className=\"problem-icon\">\n                              <AlertTriangle size={20} />\n                            </div>\n                            <div className=\"problem-content\">\n                              <h4>Business Challenge</h4>\n                              <p>{message.content.problem}</p>\n                              <div className=\"impact-highlight\">\n                                <span className=\"impact-text\">{message.content.impact}</span>\n                                <span className=\"urgency-text\">{message.content.urgency}</span>\n                              </div>\n                            </div>\n                          </div>\n\n                          {message.content.opportunity && (\n                            <div className=\"opportunity-statement\">\n                              <div className=\"opportunity-icon\">\n                                <Target size={20} />\n                              </div>\n                              <div className=\"opportunity-content\">\n                                <h4>Market Opportunity</h4>\n                                <p>{message.content.opportunity}</p>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n\n                        {/* Key Insights - Top 3 */}\n                        <div className=\"key-insights\">\n                          <h4>Critical Findings</h4>\n                          <div className=\"insights-grid\">\n                            {(message.content.topFindings || message.content.keyFindings?.slice(0, 3) || []).map((finding, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"insight-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: index * 0.1 }}\n                              >\n                                <div className=\"insight-header\">\n                                  <div className={`impact-indicator ${finding.impact?.toLowerCase()}`}>\n                                    {finding.impact === 'Critical' && <AlertTriangle size={16} />}\n                                    {finding.impact === 'High' && <TrendingUp size={16} />}\n                                    {finding.impact === 'Medium' && <Info size={16} />}\n                                  </div>\n                                  <span className={`impact-label ${finding.impact?.toLowerCase()}`}>\n                                    {finding.impact}\n                                  </span>\n                                </div>\n                                <h5>{finding.title}</h5>\n                                <p>{finding.description}</p>\n                                {finding.action && (\n                                  <div className=\"quick-action\">\n                                    <strong>Action:</strong> {finding.action}\n                                  </div>\n                                )}\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Next Steps - Top 3 Priority Actions */}\n                        <div className=\"next-steps\">\n                          <h4>Immediate Next Steps</h4>\n                          <div className=\"steps-list\">\n                            {(message.content.nextSteps || message.content.strategicRecommendations?.slice(0, 3) || []).map((step, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"step-card\"\n                                initial={{ opacity: 0, x: -10 }}\n                                animate={{ opacity: 1, x: 0 }}\n                                transition={{ delay: 0.2 + index * 0.1 }}\n                              >\n                                <div className=\"step-header\">\n                                  <div className={`priority-indicator ${step.priority?.toLowerCase()}`}>\n                                    {index + 1}\n                                  </div>\n                                  <div className=\"step-meta\">\n                                    <span className=\"step-timeline\">\n                                      <Clock size={14} />\n                                      {step.timeline}\n                                    </span>\n                                    <span className=\"step-investment\">\n                                      <DollarSign size={14} />\n                                      {step.investment}\n                                    </span>\n                                  </div>\n                                </div>\n                                <h5>{step.action}</h5>\n                                <p>{step.description}</p>\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        <div className=\"key-findings\">\n                          <h4>Key Findings</h4>\n                          {message.content.keyFindings.map((finding, index) => (\n                            <motion.div\n                              key={index}\n                              className=\"finding-card\"\n                              initial={{ opacity: 0, x: -20 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              transition={{ delay: index * 0.1 }}\n                            >\n                              <div className=\"finding-header\">\n                                <div className=\"finding-icon\">\n                                  {finding.type === 'regulatory' && <AlertTriangle size={16} />}\n                                  {finding.type === 'financial' && <TrendingUp size={16} />}\n                                  {finding.type === 'competitive' && <Info size={16} />}\n                                  {finding.type === 'market' && <TrendingUp size={16} />}\n                                  {finding.type === 'technology' && <Info size={16} />}\n                                  {finding.type === 'environmental' && <AlertTriangle size={16} />}\n                                  {finding.type === 'social' && <Info size={16} />}\n                                  {finding.type === 'governance' && <Info size={16} />}\n                                </div>\n                                <div className=\"finding-title\">{finding.title}</div>\n                                <div className=\"finding-badges\">\n                                  <div className={`impact-badge ${finding.impact.toLowerCase()}`}>\n                                    {finding.impact} Impact\n                                  </div>\n                                  {finding.urgency && (\n                                    <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>\n                                      {finding.urgency}\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n                              <p className=\"finding-description\">{finding.description}</p>\n                              {finding.details && (\n                                <div className=\"finding-details\">\n                                  <p>{finding.details}</p>\n                                </div>\n                              )}\n                              {finding.timeline && (\n                                <div className=\"finding-timeline\">\n                                  <strong>Timeline:</strong> {finding.timeline}\n                                </div>\n                              )}\n                              <div className=\"confidence-bar\">\n                                <div className=\"confidence-label\">Confidence: {finding.confidence}%</div>\n                                <div className=\"confidence-progress\">\n                                  <motion.div\n                                    className=\"confidence-fill\"\n                                    initial={{ width: 0 }}\n                                    animate={{ width: `${finding.confidence}%` }}\n                                    transition={{ duration: 1, delay: 0.5 }}\n                                  />\n                                </div>\n                              </div>\n                            </motion.div>\n                          ))}\n                        </div>\n\n                        {message.content.strategicRecommendations && (\n                          <div className=\"strategic-recommendations\">\n                            <h4>Strategic Recommendations</h4>\n                            {message.content.strategicRecommendations.map((rec, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"recommendation-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: 0.3 + index * 0.1 }}\n                              >\n                                <div className=\"recommendation-header\">\n                                  <div className={`priority-badge ${rec.priority.toLowerCase()}`}>\n                                    {rec.priority} Priority\n                                  </div>\n                                  <div className=\"recommendation-investment\">{rec.investment}</div>\n                                </div>\n                                <h5 className=\"recommendation-action\">{rec.action}</h5>\n                                <p className=\"recommendation-description\">{rec.description}</p>\n                                <div className=\"recommendation-timeline\">\n                                  <strong>Timeline:</strong> {rec.timeline}\n                                </div>\n                              </motion.div>\n                            ))}\n                          </div>\n                        )}\n\n                        {message.content.competitiveBenchmarking && (\n                          <div className=\"competitive-benchmarking\">\n                            <h4>Competitive Benchmarking</h4>\n                            <div className=\"competitors-grid\">\n                              {message.content.competitiveBenchmarking.map((comp, index) => (\n                                <motion.div\n                                  key={index}\n                                  className=\"competitor-card\"\n                                  initial={{ opacity: 0, scale: 0.95 }}\n                                  animate={{ opacity: 1, scale: 1 }}\n                                  transition={{ delay: 0.4 + index * 0.1 }}\n                                >\n                                  <h6 className=\"competitor-name\">{comp.company}</h6>\n                                  <div className=\"competitor-strategy\">\n                                    <strong>Strategy:</strong> {comp.strategy}\n                                  </div>\n                                  <div className=\"competitor-analysis\">\n                                    <div className=\"advantage\">\n                                      <strong>Advantage:</strong> {comp.advantage}\n                                    </div>\n                                    <div className=\"weakness\">\n                                      <strong>Weakness:</strong> {comp.weakness}\n                                    </div>\n                                  </div>\n                                </motion.div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n\n                        {message.content.riskAssessment && (\n                          <div className=\"risk-assessment\">\n                            <h4>Risk Assessment</h4>\n                            <div className=\"risk-categories\">\n                              <div className=\"risk-category high-risk\">\n                                <h6>High Risk</h6>\n                                <ul>\n                                  {message.content.riskAssessment.high.map((risk, index) => (\n                                    <li key={index}>{risk}</li>\n                                  ))}\n                                </ul>\n                              </div>\n                              <div className=\"risk-category medium-risk\">\n                                <h6>Medium Risk</h6>\n                                <ul>\n                                  {message.content.riskAssessment.medium.map((risk, index) => (\n                                    <li key={index}>{risk}</li>\n                                  ))}\n                                </ul>\n                              </div>\n                              <div className=\"risk-category low-risk\">\n                                <h6>Low Risk</h6>\n                                <ul>\n                                  {message.content.riskAssessment.low.map((risk, index) => (\n                                    <li key={index}>{risk}</li>\n                                  ))}\n                                </ul>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        <div className=\"sources-section\">\n                          <h4>Sources & References</h4>\n                          <div className=\"sources-grid\">\n                            {message.content.sources.map((source, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"source-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: 0.3 + index * 0.1 }}\n                              >\n                                <div className=\"source-header\">\n                                  <ExternalLink size={14} />\n                                  <span className=\"source-title\">{source.title}</span>\n                                  {source.confidence && (\n                                    <span className={`source-confidence ${source.confidence.toLowerCase()}`}>\n                                      {source.confidence}\n                                    </span>\n                                  )}\n                                </div>\n                                <div className=\"source-meta\">\n                                  <span className=\"source-url\">{source.url}</span>\n                                  <span className=\"source-date\">{source.date}</span>\n                                  <span className=\"source-type\">{source.type}</span>\n                                </div>\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        {message.content.actionItems && (\n                          <div className=\"action-items\">\n                            <h4>Quick Action Items</h4>\n                            <ul>\n                              {message.content.actionItems.map((item, index) => (\n                                <motion.li\n                                  key={index}\n                                  initial={{ opacity: 0, x: -10 }}\n                                  animate={{ opacity: 1, x: 0 }}\n                                  transition={{ delay: 0.5 + index * 0.1 }}\n                                >\n                                  {item}\n                                </motion.li>\n                              ))}\n                            </ul>\n                          </div>\n                        )}\n                      </div>\n                    ) : (\n                      <div className=\"simple-response\">{message.content}</div>\n                    )}\n                  </div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            className=\"loading-message\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"loading-content\">\n              <Loader2 className=\"loading-spinner\" size={20} />\n              <span>Analyzing ESG data and regulations...</span>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"message-input-container\">\n        <div className=\"message-input-box\">\n          <textarea\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask a follow-up question about ESG intelligence...\"\n            className=\"message-input\"\n            rows=\"1\"\n          />\n          <motion.button\n            className=\"send-btn\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || isLoading}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Send size={18} />\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ConversationView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,cAAc;AACrB,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMmC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMkC,aAAa,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC5CJ,mBAAmB,CAACK,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE,GAAG,CAACC,IAAI,CAAC,GAAGF,SAAS,IAAIC,OAAO,EAAE;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAN,cAAc,CAACO,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED3C,SAAS,CAAC,MAAM;IACduC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;EAEd1B,SAAS,CAAC,MAAM;IACd,IAAIuB,YAAY,EAAE;MAChB;MACA,MAAMqB,WAAW,GAAG;QAClBC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAExB,YAAY;QACrByB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDtB,WAAW,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC1Bb,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAmB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAAC7B,YAAY,CAAC;UAC3CyB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CpB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAElB,MAAM6B,oBAAoB,GAAIC,KAAK,IAAK;IACtC;IACA,MAAMC,gBAAgB,GAAID,KAAK,IAAK;MAClC,MAAME,UAAU,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;MAEtC,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpG,OAAOC,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIH,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvE,OAAOE,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIJ,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC9E,OAAOG,6BAA6B,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIL,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC5E,OAAOI,wBAAwB,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIN,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxE,OAAOK,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOC,8BAA8B,CAAC,CAAC;MACzC;IACF,CAAC;IAED,OAAOT,gBAAgB,CAACD,KAAK,CAAC;EAChC,CAAC;EAED,MAAMK,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLM,UAAU,EAAE,iCAAiC;MAC7CC,OAAO,EAAE,qDAAqD;MAC9DC,MAAM,EAAE,uEAAuE;MAC/EC,OAAO,EAAE,kCAAkC;MAC3CC,WAAW,EAAE,8DAA8D;MAC3EC,WAAW,EAAE,CACX;QACEvB,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,yCAAyC;QAChDJ,MAAM,EAAE,UAAU;QAClBK,WAAW,EAAE,uEAAuE;QACpFC,MAAM,EAAE;MACV,CAAC,EACD;QACE1B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,iDAAiD;QAC9DC,MAAM,EAAE;MACV,CAAC,EACD;QACE1B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,0BAA0B;QACjCJ,MAAM,EAAE,MAAM;QACdK,WAAW,EAAE,mDAAmD;QAChEC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,gBAAgB,EAAE,CAChB;QACE3B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,uDAAuD;QAC9DJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,mJAAmJ;QAChKG,OAAO,EAAE,8MAA8M;QACvNC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,qHAAqH;QAClIG,OAAO,EAAE,0LAA0L;QACnMC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,6BAA6B;QACpCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,6GAA6G;QAC1HG,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,yBAAyB;QACxCC,kBAAkB,EAAE,YAAY;QAChCC,qBAAqB,EAAE,yBAAyB;QAChDC,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;QACxFC,MAAM,EAAE,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,8BAA8B,CAAC;QACxGC,GAAG,EAAE,CAAC,qBAAqB,EAAE,+BAA+B;MAC9D,CAAC;MACDC,SAAS,EAAE,CACT;QACEC,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,cAAc;QACxBY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,+BAA+B;QACvCI,QAAQ,EAAE,UAAU;QACpBY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,iCAAiC;QACzCI,QAAQ,EAAE,WAAW;QACrBY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDkB,kBAAkB,EAAE,CAClB;QACEF,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,+CAA+C;QACvDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,mDAAmD;QAC3DI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,QAAQ;QAClBf,MAAM,EAAE,kCAAkC;QAC1CI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,WAAW;QACvBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDmB,uBAAuB,EAAE,CACvB;QACEC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,qEAAqE;QAC/EC,SAAS,EAAE,uDAAuD;QAClEC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,6DAA6D;QACvEC,SAAS,EAAE,iDAAiD;QAC5DC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE,4DAA4D;QACtEC,SAAS,EAAE,qDAAqD;QAChEC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,2CAA2C;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAChJ;QAAEL,KAAK,EAAE,qDAAqD;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAChJ;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAChI;QAAEL,KAAK,EAAE,kCAAkC;QAAE0B,GAAG,EAAE,oBAAoB;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAClI;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAS,CAAC,EACtI;QAAEL,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,aAAa;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,QAAQ;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAElI,CAAC;EACH,CAAC;EAED,MAAMhB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACLK,UAAU,EAAE,2DAA2D;MACvEkC,gBAAgB,EAAE,qRAAqR;MACvSC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,yBAAyB;QAChCJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,6IAA6I;QACtJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uGAAuG;QACpHG,OAAO,EAAE,0JAA0J;QACnKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,gFAAgF;QAC7FG,OAAO,EAAE,kJAAkJ;QAC3JC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,8CAA8C;QACrDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0GAA0G;QACvHG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,wCAAwC;QAC/CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,8FAA8F;QAC3GG,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,2BAA2B;QAC1CC,kBAAkB,EAAE,4BAA4B;QAChDC,qBAAqB,EAAE,kCAAkC;QACzDC,iBAAiB,EAAE;MACrB,CAAC;MACDmB,wBAAwB,EAAE,CACxB;QACEb,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,kDAAkD;QAC1DI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,MAAM;QAClBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,yCAAyC;QACjDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,0CAA0C;QAClDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDwB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,6BAA6B;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,+CAA+C;QAAE0B,GAAG,EAAE,+BAA+B;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAC9J;QAAEL,KAAK,EAAE,8CAA8C;QAAE0B,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMZ,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,OAAO;MACLC,UAAU,EAAE,uCAAuC;MACnDkC,gBAAgB,EAAE,oWAAoW;MACtXC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,eAAe;QACrBwB,KAAK,EAAE,sCAAsC;QAC7CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,uKAAuK;QAChLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,0CAA0C;QACjDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,uHAAuH;QACpIG,OAAO,EAAE,sKAAsK;QAC/KC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,4CAA4C;QACnDJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,4GAA4G;QACzHG,OAAO,EAAE,6JAA6J;QACtKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,WAAW;QACjBwB,KAAK,EAAE,iDAAiD;QACxDJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,iHAAiH;QAC9HG,OAAO,EAAE,+JAA+J;QACxKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE9B,IAAI,EAAE,YAAY;QAClBwB,KAAK,EAAE,iCAAiC;QACxCJ,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,gJAAgJ;QACzJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDwB,wBAAwB,EAAE,CACxB;QACEb,QAAQ,EAAE,UAAU;QACpBf,MAAM,EAAE,6CAA6C;QACrDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,KAAK;QACjBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,8CAA8C;QACtDI,QAAQ,EAAE,oBAAoB;QAC9BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,EACD;QACEgB,QAAQ,EAAE,MAAM;QAChBf,MAAM,EAAE,2CAA2C;QACnDI,QAAQ,EAAE,qBAAqB;QAC/BY,UAAU,EAAE,OAAO;QACnBjB,WAAW,EAAE;MACf,CAAC,CACF;MACDwB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,qCAAqC;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,QAAQ;QAAE6B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC,EAC3H;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAS,CAAC;IAEtI,CAAC;EACH,CAAC;EAED,MAAMf,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,OAAO;MACLI,UAAU,EAAE,sCAAsC;MAClDkC,gBAAgB,EAAE,sQAAsQ;MACxRC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,qCAAqC;QAC5CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,0EAA0E;QACvFG,OAAO,EAAE,wIAAwI;QACjJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,oDAAoD;QAAE0B,GAAG,EAAE,8BAA8B;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,UAAU;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAElK,CAAC;EACH,CAAC;EAED,MAAMd,wBAAwB,GAAGA,CAAA,KAAM;IACrC,OAAO;MACLG,UAAU,EAAE,mCAAmC;MAC/CkC,gBAAgB,EAAE,kPAAkP;MACpQC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,aAAa;QACnBwB,KAAK,EAAE,mCAAmC;QAC1CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBI,WAAW,EAAE,wHAAwH;QACrIG,OAAO,EAAE,wHAAwH;QACjIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,WAAW;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEhI,CAAC;EACH,CAAC;EAED,MAAMb,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLE,UAAU,EAAE,kCAAkC;MAC9CkC,gBAAgB,EAAE,gPAAgP;MAClQC,WAAW,EAAE,CACX;QACErD,IAAI,EAAE,QAAQ;QACdwB,KAAK,EAAE,oCAAoC;QAC3CJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfI,WAAW,EAAE,yHAAyH;QACtIG,OAAO,EAAE,0HAA0H;QACnIC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDmB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEnD,IAAI,EAAE,YAAY;QAAE6B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIzE,UAAU,CAAC0E,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM1D,WAAW,GAAG;QAClBC,EAAE,EAAEnB,QAAQ,CAAC6E,MAAM,GAAG,CAAC;QACvBzD,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEnB,UAAU;QACnBoB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,WAAW,CAAC,CAAC;MAC3Cf,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAmB,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAEnB,QAAQ,CAAC6E,MAAM,GAAG,CAAC;UACvBzD,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAACxB,UAAU,CAAC;UACzCoB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDtB,WAAW,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,UAAU,CAAC,CAAC;QAC1CpB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMyE,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,WAAW,GAAIjE,OAAO,IAAK;IAC/BkE,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,OAAOpE,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGqE,IAAI,CAACC,SAAS,CAACtE,OAAO,CAAC,CAAC;EAChG,CAAC;EAED,oBACE1B,OAAA,CAACnB,MAAM,CAACoH,GAAG;IACTC,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAC7BG,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9B1G,OAAA;MAAKkG,SAAS,EAAC,qBAAqB;MAAAQ,QAAA,gBAClC1G,OAAA,CAACnB,MAAM,CAAC8H,MAAM;QACZT,SAAS,EAAC,UAAU;QACpBU,OAAO,EAAEzG,MAAO;QAChB0G,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAJ,QAAA,gBAE1B1G,OAAA,CAACjB,SAAS;UAACiI,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBpH,OAAA;QAAKkG,SAAS,EAAC,sBAAsB;QAAAQ,QAAA,gBACnC1G,OAAA,CAACnB,MAAM,CAAC8H,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAEpB,WAAY;UACrBqB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B1G,OAAA,CAACf,QAAQ;YAAC+H,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChBpH,OAAA,CAACnB,MAAM,CAAC8H,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B1G,OAAA,CAACR,MAAM;YAACwH,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpH,OAAA;MAAKkG,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,gBACjC1G,OAAA,CAAClB,eAAe;QAAA4H,QAAA,EACbrG,QAAQ,CAACgH,GAAG,CAAEC,OAAO;UAAA,IAAAC,qBAAA,EAAAC,qBAAA;UAAA,oBACpBxH,OAAA,CAACnB,MAAM,CAACoH,GAAG;YAETC,SAAS,EAAE,WAAWoB,OAAO,CAAC7F,IAAI,EAAG;YACrC0E,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEqB,CAAC,EAAE;YAAG,CAAE;YAC/BnB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEqB,CAAC,EAAE;YAAE,CAAE;YAC9BlB,IAAI,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAEqB,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAC,QAAA,EAE7BY,OAAO,CAAC7F,IAAI,KAAK,MAAM,gBACtBzB,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAQ,QAAA,gBAC3B1G,OAAA;gBAAKkG,SAAS,EAAC,iBAAiB;gBAAAQ,QAAA,EAAEY,OAAO,CAAC5F;cAAO;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDpH,OAAA;gBAAKkG,SAAS,EAAC,cAAc;gBAAAQ,QAAA,EAC1BY,OAAO,CAAC3F,SAAS,CAAC+F,kBAAkB,CAAC,EAAE,EAAE;kBAAEC,IAAI,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAU,CAAC;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENpH,OAAA;cAAKkG,SAAS,EAAC,YAAY;cAAAQ,QAAA,gBACzB1G,OAAA;gBAAKkG,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EACzB,OAAOY,OAAO,CAAC5F,OAAO,KAAK,QAAQ,gBAClC1B,OAAA;kBAAKkG,SAAS,EAAC,gCAAgC;kBAAAQ,QAAA,gBAC7C1G,OAAA;oBAAKkG,SAAS,EAAC,eAAe;oBAAAQ,QAAA,eAC5B1G,OAAA;sBAAKkG,SAAS,EAAC,sBAAsB;sBAAAQ,QAAA,gBACnC1G,OAAA;wBAAA0G,QAAA,EAAKY,OAAO,CAAC5F,OAAO,CAACiB,UAAU,IAAI;sBAAyB;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClEpH,OAAA;wBAAKkG,SAAS,EAAC,gBAAgB;wBAAAQ,QAAA,eAC7B1G,OAAA;0BACEkG,SAAS,EAAC,oBAAoB;0BAC9BU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC2B,OAAO,CAAC5F,OAAO,CAAE;0BAC5CuB,KAAK,EAAC,aAAa;0BAAAyD,QAAA,eAEnB1G,OAAA,CAACT,IAAI;4BAACyH,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNpH,OAAA;oBAAKkG,SAAS,EAAC,0BAA0B;oBAAAQ,QAAA,gBACvC1G,OAAA;sBAAKkG,SAAS,EAAC,mBAAmB;sBAAAQ,QAAA,gBAChC1G,OAAA;wBAAKkG,SAAS,EAAC,cAAc;wBAAAQ,QAAA,eAC3B1G,OAAA,CAACZ,aAAa;0BAAC4H,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,iBAAiB;wBAAAQ,QAAA,gBAC9B1G,OAAA;0BAAA0G,QAAA,EAAI;wBAAkB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3BpH,OAAA;0BAAA0G,QAAA,EAAIY,OAAO,CAAC5F,OAAO,CAACkB;wBAAO;0BAAAqE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAChCpH,OAAA;0BAAKkG,SAAS,EAAC,kBAAkB;0BAAAQ,QAAA,gBAC/B1G,OAAA;4BAAMkG,SAAS,EAAC,aAAa;4BAAAQ,QAAA,EAAEY,OAAO,CAAC5F,OAAO,CAACmB;0BAAM;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAC7DpH,OAAA;4BAAMkG,SAAS,EAAC,cAAc;4BAAAQ,QAAA,EAAEY,OAAO,CAAC5F,OAAO,CAACoB;0BAAO;4BAAAmE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAELE,OAAO,CAAC5F,OAAO,CAACqB,WAAW,iBAC1B/C,OAAA;sBAAKkG,SAAS,EAAC,uBAAuB;sBAAAQ,QAAA,gBACpC1G,OAAA;wBAAKkG,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,eAC/B1G,OAAA,CAACL,MAAM;0BAACqH,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,gBAClC1G,OAAA;0BAAA0G,QAAA,EAAI;wBAAkB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3BpH,OAAA;0BAAA0G,QAAA,EAAIY,OAAO,CAAC5F,OAAO,CAACqB;wBAAW;0BAAAkE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAGNpH,OAAA;oBAAKkG,SAAS,EAAC,cAAc;oBAAAQ,QAAA,gBAC3B1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BpH,OAAA;sBAAKkG,SAAS,EAAC,eAAe;sBAAAQ,QAAA,EAC3B,CAACY,OAAO,CAAC5F,OAAO,CAACsB,WAAW,MAAAuE,qBAAA,GAAID,OAAO,CAAC5F,OAAO,CAACoD,WAAW,cAAAyC,qBAAA,uBAA3BA,qBAAA,CAA6BM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,EAAER,GAAG,CAAC,CAACS,OAAO,EAAEC,KAAK;wBAAA,IAAAC,eAAA,EAAAC,gBAAA;wBAAA,oBAClGjI,OAAA,CAACnB,MAAM,CAACoH,GAAG;0BAETC,SAAS,EAAC,cAAc;0BACxBC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEqB,CAAC,EAAE;0BAAG,CAAE;0BAC/BnB,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEqB,CAAC,EAAE;0BAAE,CAAE;0BAC9BjB,UAAU,EAAE;4BAAE0B,KAAK,EAAEH,KAAK,GAAG;0BAAI,CAAE;0BAAArB,QAAA,gBAEnC1G,OAAA;4BAAKkG,SAAS,EAAC,gBAAgB;4BAAAQ,QAAA,gBAC7B1G,OAAA;8BAAKkG,SAAS,EAAE,qBAAA8B,eAAA,GAAoBF,OAAO,CAACjF,MAAM,cAAAmF,eAAA,uBAAdA,eAAA,CAAgB7F,WAAW,CAAC,CAAC,EAAG;8BAAAuE,QAAA,GACjEoB,OAAO,CAACjF,MAAM,KAAK,UAAU,iBAAI7C,OAAA,CAACZ,aAAa;gCAAC4H,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EAC5DU,OAAO,CAACjF,MAAM,KAAK,MAAM,iBAAI7C,OAAA,CAACb,UAAU;gCAAC6H,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACrDU,OAAO,CAACjF,MAAM,KAAK,QAAQ,iBAAI7C,OAAA,CAACX,IAAI;gCAAC2H,IAAI,EAAE;8BAAG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/C,CAAC,eACNpH,OAAA;8BAAMkG,SAAS,EAAE,iBAAA+B,gBAAA,GAAgBH,OAAO,CAACjF,MAAM,cAAAoF,gBAAA,uBAAdA,gBAAA,CAAgB9F,WAAW,CAAC,CAAC,EAAG;8BAAAuE,QAAA,EAC9DoB,OAAO,CAACjF;4BAAM;8BAAAoE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACX,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACNpH,OAAA;4BAAA0G,QAAA,EAAKoB,OAAO,CAAC7E;0BAAK;4BAAAgE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACxBpH,OAAA;4BAAA0G,QAAA,EAAIoB,OAAO,CAAC5E;0BAAW;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,EAC3BU,OAAO,CAAC3E,MAAM,iBACbnD,OAAA;4BAAKkG,SAAS,EAAC,cAAc;4BAAAQ,QAAA,gBAC3B1G,OAAA;8BAAA0G,QAAA,EAAQ;4BAAO;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACU,OAAO,CAAC3E,MAAM;0BAAA;4BAAA8D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrC,CACN;wBAAA,GAtBIW,KAAK;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuBA,CAAC;sBAAA,CACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNpH,OAAA;oBAAKkG,SAAS,EAAC,YAAY;oBAAAQ,QAAA,gBACzB1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAoB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7BpH,OAAA;sBAAKkG,SAAS,EAAC,YAAY;sBAAAQ,QAAA,EACxB,CAACY,OAAO,CAAC5F,OAAO,CAACuC,SAAS,MAAAuD,qBAAA,GAAIF,OAAO,CAAC5F,OAAO,CAACqD,wBAAwB,cAAAyC,qBAAA,uBAAxCA,qBAAA,CAA0CK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,EAAER,GAAG,CAAC,CAACc,IAAI,EAAEJ,KAAK;wBAAA,IAAAK,cAAA;wBAAA,oBAC1GpI,OAAA,CAACnB,MAAM,CAACoH,GAAG;0BAETC,SAAS,EAAC,WAAW;0BACrBC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE,CAAC;0BAAG,CAAE;0BAChCC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAEC,CAAC,EAAE;0BAAE,CAAE;0BAC9BG,UAAU,EAAE;4BAAE0B,KAAK,EAAE,GAAG,GAAGH,KAAK,GAAG;0BAAI,CAAE;0BAAArB,QAAA,gBAEzC1G,OAAA;4BAAKkG,SAAS,EAAC,aAAa;4BAAAQ,QAAA,gBAC1B1G,OAAA;8BAAKkG,SAAS,EAAE,uBAAAkC,cAAA,GAAsBD,IAAI,CAACjE,QAAQ,cAAAkE,cAAA,uBAAbA,cAAA,CAAejG,WAAW,CAAC,CAAC,EAAG;8BAAAuE,QAAA,EAClEqB,KAAK,GAAG;4BAAC;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACNpH,OAAA;8BAAKkG,SAAS,EAAC,WAAW;8BAAAQ,QAAA,gBACxB1G,OAAA;gCAAMkG,SAAS,EAAC,eAAe;gCAAAQ,QAAA,gBAC7B1G,OAAA,CAACH,KAAK;kCAACmH,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EAClBe,IAAI,CAAC5E,QAAQ;8BAAA;gCAAA0D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV,CAAC,eACPpH,OAAA;gCAAMkG,SAAS,EAAC,iBAAiB;gCAAAQ,QAAA,gBAC/B1G,OAAA,CAACJ,UAAU;kCAACoH,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EACvBe,IAAI,CAAChE,UAAU;8BAAA;gCAAA8C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACNpH,OAAA;4BAAA0G,QAAA,EAAKyB,IAAI,CAAChF;0BAAM;4BAAA8D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtBpH,OAAA;4BAAA0G,QAAA,EAAIyB,IAAI,CAACjF;0BAAW;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA,GAtBpBW,KAAK;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuBA,CAAC;sBAAA,CACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENpH,OAAA;oBAAKkG,SAAS,EAAC,cAAc;oBAAAQ,QAAA,gBAC3B1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACpBE,OAAO,CAAC5F,OAAO,CAACoD,WAAW,CAACuC,GAAG,CAAC,CAACS,OAAO,EAAEC,KAAK,kBAC9C/H,OAAA,CAACnB,MAAM,CAACoH,GAAG;sBAETC,SAAS,EAAC,cAAc;sBACxBC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE,CAAC;sBAAG,CAAE;sBAChCC,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAE,CAAE;sBAC9BG,UAAU,EAAE;wBAAE0B,KAAK,EAAEH,KAAK,GAAG;sBAAI,CAAE;sBAAArB,QAAA,gBAEnC1G,OAAA;wBAAKkG,SAAS,EAAC,gBAAgB;wBAAAQ,QAAA,gBAC7B1G,OAAA;0BAAKkG,SAAS,EAAC,cAAc;0BAAAQ,QAAA,GAC1BoB,OAAO,CAACrG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACZ,aAAa;4BAAC4H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EAC5DU,OAAO,CAACrG,IAAI,KAAK,WAAW,iBAAIzB,OAAA,CAACb,UAAU;4BAAC6H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxDU,OAAO,CAACrG,IAAI,KAAK,aAAa,iBAAIzB,OAAA,CAACX,IAAI;4BAAC2H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACpDU,OAAO,CAACrG,IAAI,KAAK,QAAQ,iBAAIzB,OAAA,CAACb,UAAU;4BAAC6H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACrDU,OAAO,CAACrG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACX,IAAI;4BAAC2H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACnDU,OAAO,CAACrG,IAAI,KAAK,eAAe,iBAAIzB,OAAA,CAACZ,aAAa;4BAAC4H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EAC/DU,OAAO,CAACrG,IAAI,KAAK,QAAQ,iBAAIzB,OAAA,CAACX,IAAI;4BAAC2H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EAC/CU,OAAO,CAACrG,IAAI,KAAK,YAAY,iBAAIzB,OAAA,CAACX,IAAI;4BAAC2H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjD,CAAC,eACNpH,OAAA;0BAAKkG,SAAS,EAAC,eAAe;0BAAAQ,QAAA,EAAEoB,OAAO,CAAC7E;wBAAK;0BAAAgE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACpDpH,OAAA;0BAAKkG,SAAS,EAAC,gBAAgB;0BAAAQ,QAAA,gBAC7B1G,OAAA;4BAAKkG,SAAS,EAAE,gBAAgB4B,OAAO,CAACjF,MAAM,CAACV,WAAW,CAAC,CAAC,EAAG;4BAAAuE,QAAA,GAC5DoB,OAAO,CAACjF,MAAM,EAAC,SAClB;0BAAA;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,EACLU,OAAO,CAAChF,OAAO,iBACd9C,OAAA;4BAAKkG,SAAS,EAAE,iBAAiB4B,OAAO,CAAChF,OAAO,CAACX,WAAW,CAAC,CAAC,EAAG;4BAAAuE,QAAA,EAC9DoB,OAAO,CAAChF;0BAAO;4BAAAmE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpH,OAAA;wBAAGkG,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,EAAEoB,OAAO,CAAC5E;sBAAW;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC3DU,OAAO,CAACzE,OAAO,iBACdrD,OAAA;wBAAKkG,SAAS,EAAC,iBAAiB;wBAAAQ,QAAA,eAC9B1G,OAAA;0BAAA0G,QAAA,EAAIoB,OAAO,CAACzE;wBAAO;0BAAA4D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CACN,EACAU,OAAO,CAACvE,QAAQ,iBACfvD,OAAA;wBAAKkG,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,gBAC/B1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACU,OAAO,CAACvE,QAAQ;sBAAA;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CACN,eACDpH,OAAA;wBAAKkG,SAAS,EAAC,gBAAgB;wBAAAQ,QAAA,gBAC7B1G,OAAA;0BAAKkG,SAAS,EAAC,kBAAkB;0BAAAQ,QAAA,GAAC,cAAY,EAACoB,OAAO,CAACxE,UAAU,EAAC,GAAC;wBAAA;0BAAA2D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzEpH,OAAA;0BAAKkG,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,eAClC1G,OAAA,CAACnB,MAAM,CAACoH,GAAG;4BACTC,SAAS,EAAC,iBAAiB;4BAC3BC,OAAO,EAAE;8BAAEkC,KAAK,EAAE;4BAAE,CAAE;4BACtB/B,OAAO,EAAE;8BAAE+B,KAAK,EAAE,GAAGP,OAAO,CAACxE,UAAU;4BAAI,CAAE;4BAC7CkD,UAAU,EAAE;8BAAEC,QAAQ,EAAE,CAAC;8BAAEyB,KAAK,EAAE;4BAAI;0BAAE;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,GAlDDW,KAAK;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAmDA,CACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,EAELE,OAAO,CAAC5F,OAAO,CAACqD,wBAAwB,iBACvC/E,OAAA;oBAAKkG,SAAS,EAAC,2BAA2B;oBAAAQ,QAAA,gBACxC1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAyB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACjCE,OAAO,CAAC5F,OAAO,CAACqD,wBAAwB,CAACsC,GAAG,CAAC,CAACiB,GAAG,EAAEP,KAAK,kBACvD/H,OAAA,CAACnB,MAAM,CAACoH,GAAG;sBAETC,SAAS,EAAC,qBAAqB;sBAC/BC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEqB,CAAC,EAAE;sBAAG,CAAE;sBAC/BnB,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEqB,CAAC,EAAE;sBAAE,CAAE;sBAC9BjB,UAAU,EAAE;wBAAE0B,KAAK,EAAE,GAAG,GAAGH,KAAK,GAAG;sBAAI,CAAE;sBAAArB,QAAA,gBAEzC1G,OAAA;wBAAKkG,SAAS,EAAC,uBAAuB;wBAAAQ,QAAA,gBACpC1G,OAAA;0BAAKkG,SAAS,EAAE,kBAAkBoC,GAAG,CAACpE,QAAQ,CAAC/B,WAAW,CAAC,CAAC,EAAG;0BAAAuE,QAAA,GAC5D4B,GAAG,CAACpE,QAAQ,EAAC,WAChB;wBAAA;0BAAA+C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNpH,OAAA;0BAAKkG,SAAS,EAAC,2BAA2B;0BAAAQ,QAAA,EAAE4B,GAAG,CAACnE;wBAAU;0BAAA8C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC,eACNpH,OAAA;wBAAIkG,SAAS,EAAC,uBAAuB;wBAAAQ,QAAA,EAAE4B,GAAG,CAACnF;sBAAM;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACvDpH,OAAA;wBAAGkG,SAAS,EAAC,4BAA4B;wBAAAQ,QAAA,EAAE4B,GAAG,CAACpF;sBAAW;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/DpH,OAAA;wBAAKkG,SAAS,EAAC,yBAAyB;wBAAAQ,QAAA,gBACtC1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACkB,GAAG,CAAC/E,QAAQ;sBAAA;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA,GAhBDW,KAAK;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAiBA,CACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN,EAEAE,OAAO,CAAC5F,OAAO,CAAC2C,uBAAuB,iBACtCrE,OAAA;oBAAKkG,SAAS,EAAC,0BAA0B;oBAAAQ,QAAA,gBACvC1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAwB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjCpH,OAAA;sBAAKkG,SAAS,EAAC,kBAAkB;sBAAAQ,QAAA,EAC9BY,OAAO,CAAC5F,OAAO,CAAC2C,uBAAuB,CAACgD,GAAG,CAAC,CAACkB,IAAI,EAAER,KAAK,kBACvD/H,OAAA,CAACnB,MAAM,CAACoH,GAAG;wBAETC,SAAS,EAAC,iBAAiB;wBAC3BC,OAAO,EAAE;0BAAEC,OAAO,EAAE,CAAC;0BAAEU,KAAK,EAAE;wBAAK,CAAE;wBACrCR,OAAO,EAAE;0BAAEF,OAAO,EAAE,CAAC;0BAAEU,KAAK,EAAE;wBAAE,CAAE;wBAClCN,UAAU,EAAE;0BAAE0B,KAAK,EAAE,GAAG,GAAGH,KAAK,GAAG;wBAAI,CAAE;wBAAArB,QAAA,gBAEzC1G,OAAA;0BAAIkG,SAAS,EAAC,iBAAiB;0BAAAQ,QAAA,EAAE6B,IAAI,CAACjE;wBAAO;0BAAA2C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACnDpH,OAAA;0BAAKkG,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,gBAClC1G,OAAA;4BAAA0G,QAAA,EAAQ;0BAAS;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACmB,IAAI,CAAChE,QAAQ;wBAAA;0BAAA0C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC,eACNpH,OAAA;0BAAKkG,SAAS,EAAC,qBAAqB;0BAAAQ,QAAA,gBAClC1G,OAAA;4BAAKkG,SAAS,EAAC,WAAW;4BAAAQ,QAAA,gBACxB1G,OAAA;8BAAA0G,QAAA,EAAQ;4BAAU;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACmB,IAAI,CAAC/D,SAAS;0BAAA;4BAAAyC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CAAC,eACNpH,OAAA;4BAAKkG,SAAS,EAAC,UAAU;4BAAAQ,QAAA,gBACvB1G,OAAA;8BAAA0G,QAAA,EAAQ;4BAAS;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,KAAC,EAACmB,IAAI,CAAC9D,QAAQ;0BAAA;4BAAAwC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GAjBDW,KAAK;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAkBA,CACb;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAEAE,OAAO,CAAC5F,OAAO,CAACmC,cAAc,iBAC7B7D,OAAA;oBAAKkG,SAAS,EAAC,iBAAiB;oBAAAQ,QAAA,gBAC9B1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxBpH,OAAA;sBAAKkG,SAAS,EAAC,iBAAiB;sBAAAQ,QAAA,gBAC9B1G,OAAA;wBAAKkG,SAAS,EAAC,yBAAyB;wBAAAQ,QAAA,gBACtC1G,OAAA;0BAAA0G,QAAA,EAAI;wBAAS;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAClBpH,OAAA;0BAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACmC,cAAc,CAACC,IAAI,CAACuD,GAAG,CAAC,CAACmB,IAAI,EAAET,KAAK,kBACnD/H,OAAA;4BAAA0G,QAAA,EAAiB8B;0BAAI,GAAZT,KAAK;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAC3B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,2BAA2B;wBAAAQ,QAAA,gBACxC1G,OAAA;0BAAA0G,QAAA,EAAI;wBAAW;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACpBpH,OAAA;0BAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACmC,cAAc,CAACE,MAAM,CAACsD,GAAG,CAAC,CAACmB,IAAI,EAAET,KAAK,kBACrD/H,OAAA;4BAAA0G,QAAA,EAAiB8B;0BAAI,GAAZT,KAAK;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAC3B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACNpH,OAAA;wBAAKkG,SAAS,EAAC,wBAAwB;wBAAAQ,QAAA,gBACrC1G,OAAA;0BAAA0G,QAAA,EAAI;wBAAQ;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACjBpH,OAAA;0BAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACmC,cAAc,CAACG,GAAG,CAACqD,GAAG,CAAC,CAACmB,IAAI,EAAET,KAAK,kBAClD/H,OAAA;4BAAA0G,QAAA,EAAiB8B;0BAAI,GAAZT,KAAK;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAC3B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAEDpH,OAAA;oBAAKkG,SAAS,EAAC,iBAAiB;oBAAAQ,QAAA,gBAC9B1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAoB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7BpH,OAAA;sBAAKkG,SAAS,EAAC,cAAc;sBAAAQ,QAAA,EAC1BY,OAAO,CAAC5F,OAAO,CAACgD,OAAO,CAAC2C,GAAG,CAAC,CAACoB,MAAM,EAAEV,KAAK,kBACzC/H,OAAA,CAACnB,MAAM,CAACoH,GAAG;wBAETC,SAAS,EAAC,aAAa;wBACvBC,OAAO,EAAE;0BAAEC,OAAO,EAAE,CAAC;0BAAEqB,CAAC,EAAE;wBAAG,CAAE;wBAC/BnB,OAAO,EAAE;0BAAEF,OAAO,EAAE,CAAC;0BAAEqB,CAAC,EAAE;wBAAE,CAAE;wBAC9BjB,UAAU,EAAE;0BAAE0B,KAAK,EAAE,GAAG,GAAGH,KAAK,GAAG;wBAAI,CAAE;wBAAArB,QAAA,gBAEzC1G,OAAA;0BAAKkG,SAAS,EAAC,eAAe;0BAAAQ,QAAA,gBAC5B1G,OAAA,CAACd,YAAY;4BAAC8H,IAAI,EAAE;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC1BpH,OAAA;4BAAMkG,SAAS,EAAC,cAAc;4BAAAQ,QAAA,EAAE+B,MAAM,CAACxF;0BAAK;4BAAAgE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EACnDqB,MAAM,CAACnF,UAAU,iBAChBtD,OAAA;4BAAMkG,SAAS,EAAE,qBAAqBuC,MAAM,CAACnF,UAAU,CAACnB,WAAW,CAAC,CAAC,EAAG;4BAAAuE,QAAA,EACrE+B,MAAM,CAACnF;0BAAU;4BAAA2D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNpH,OAAA;0BAAKkG,SAAS,EAAC,aAAa;0BAAAQ,QAAA,gBAC1B1G,OAAA;4BAAMkG,SAAS,EAAC,YAAY;4BAAAQ,QAAA,EAAE+B,MAAM,CAAC9D;0BAAG;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAChDpH,OAAA;4BAAMkG,SAAS,EAAC,aAAa;4BAAAQ,QAAA,EAAE+B,MAAM,CAAC7D;0BAAI;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAClDpH,OAAA;4BAAMkG,SAAS,EAAC,aAAa;4BAAAQ,QAAA,EAAE+B,MAAM,CAAChH;0BAAI;4BAAAwF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/C,CAAC;sBAAA,GAnBDW,KAAK;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAoBA,CACb;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELE,OAAO,CAAC5F,OAAO,CAACgH,WAAW,iBAC1B1I,OAAA;oBAAKkG,SAAS,EAAC,cAAc;oBAAAQ,QAAA,gBAC3B1G,OAAA;sBAAA0G,QAAA,EAAI;oBAAkB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3BpH,OAAA;sBAAA0G,QAAA,EACGY,OAAO,CAAC5F,OAAO,CAACgH,WAAW,CAACrB,GAAG,CAAC,CAACsB,IAAI,EAAEZ,KAAK,kBAC3C/H,OAAA,CAACnB,MAAM,CAAC+J,EAAE;wBAERzC,OAAO,EAAE;0BAAEC,OAAO,EAAE,CAAC;0BAAEC,CAAC,EAAE,CAAC;wBAAG,CAAE;wBAChCC,OAAO,EAAE;0BAAEF,OAAO,EAAE,CAAC;0BAAEC,CAAC,EAAE;wBAAE,CAAE;wBAC9BG,UAAU,EAAE;0BAAE0B,KAAK,EAAE,GAAG,GAAGH,KAAK,GAAG;wBAAI,CAAE;wBAAArB,QAAA,EAExCiC;sBAAI,GALAZ,KAAK;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAMD,CACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,gBAENpH,OAAA;kBAAKkG,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,EAAEY,OAAO,CAAC5F;gBAAO;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACxD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpH,OAAA;gBAAKkG,SAAS,EAAC,cAAc;gBAAAQ,QAAA,EAC1BY,OAAO,CAAC3F,SAAS,CAAC+F,kBAAkB,CAAC,EAAE,EAAE;kBAAEC,IAAI,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAU,CAAC;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GA/UIE,OAAO,CAAC9F,EAAE;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgVL,CAAC;QAAA,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEjB3G,SAAS,iBACRT,OAAA,CAACnB,MAAM,CAACoH,GAAG;QACTC,SAAS,EAAC,iBAAiB;QAC3BC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEqB,CAAC,EAAE;QAAG,CAAE;QAC/BnB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEqB,CAAC,EAAE;QAAE,CAAE;QAAAf,QAAA,eAE9B1G,OAAA;UAAKkG,SAAS,EAAC,iBAAiB;UAAAQ,QAAA,gBAC9B1G,OAAA,CAACV,OAAO;YAAC4G,SAAS,EAAC,iBAAiB;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDpH,OAAA;YAAA0G,QAAA,EAAM;UAAqC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDpH,OAAA;QAAK6I,GAAG,EAAEhI;MAAe;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNpH,OAAA;MAAKkG,SAAS,EAAC,yBAAyB;MAAAQ,QAAA,eACtC1G,OAAA;QAAKkG,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChC1G,OAAA;UACE8I,KAAK,EAAEvI,UAAW;UAClBwI,QAAQ,EAAG3D,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAE9D,cAAe;UAC3B+D,WAAW,EAAC,oDAAoD;UAChEhD,SAAS,EAAC,eAAe;UACzBiD,IAAI,EAAC;QAAG;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpH,OAAA,CAACnB,MAAM,CAAC8H,MAAM;UACZT,SAAS,EAAC,UAAU;UACpBU,OAAO,EAAE5B,iBAAkB;UAC3BoE,QAAQ,EAAE,CAAC7I,UAAU,CAAC0E,IAAI,CAAC,CAAC,IAAIxE,SAAU;UAC1CoG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,eAE1B1G,OAAA,CAAChB,IAAI;YAACgI,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAChH,EAAA,CAj7BIH,gBAAgB;AAAAoJ,EAAA,GAAhBpJ,gBAAgB;AAm7BtB,eAAeA,gBAAgB;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}