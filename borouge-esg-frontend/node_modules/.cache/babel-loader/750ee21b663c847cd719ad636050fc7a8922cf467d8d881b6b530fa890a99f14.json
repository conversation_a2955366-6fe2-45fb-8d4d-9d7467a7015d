{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowLeft, Send, Download, ExternalLink, TrendingUp, AlertTriangle, Info, Loader2, Copy, Share2 } from 'lucide-react';\nimport './ConversationView.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationView = ({\n  initialQuery,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n  const generateMockResponse = query => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = query => {\n      const lowerQuery = query.toLowerCase();\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n    return getReportByQuery(query);\n  };\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"Regulatory Intelligence Report\",\n      executiveSummary: \"The EU's new packaging regulations present significant compliance challenges and opportunities for Borouge's European operations. With 65% of Borouge's polyethylene exports destined for EU markets, immediate strategic action is required to maintain market position and capitalize on emerging opportunities in sustainable packaging solutions.\",\n      keyFindings: [{\n        type: \"regulatory\",\n        title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n        details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n        confidence: 98,\n        timeline: \"Implementation: Jan 2025, Full compliance: 2030\"\n      }, {\n        type: \"financial\",\n        title: \"Compliance Investment Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n        details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2027\"\n      }, {\n        type: \"market\",\n        title: \"Premium Pricing Opportunity\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Sustainable packaging commands 15-25% price premium in EU markets, potentially offsetting compliance costs.\",\n        details: \"Early movers in sustainable polyethylene have captured premium pricing. Borouge could achieve €150-250M additional annual revenue through premium sustainable product lines.\",\n        confidence: 78,\n        timeline: \"Market opportunity: 2025-2030\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Landscape Shift\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n        details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n        confidence: 92,\n        timeline: \"Competitive threat: Immediate\"\n      }, {\n        type: \"technology\",\n        title: \"Chemical Recycling Technology Gap\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Current mechanical recycling insufficient for quality requirements; chemical recycling technology partnerships essential.\",\n        details: \"Pyrolysis and depolymerization technologies can achieve virgin-quality recycled content. Strategic partnerships with Plastic Energy, Quantafuel, or Agilyx recommended.\",\n        confidence: 88,\n        timeline: \"Technology deployment: 2025-2026\"\n      }, {\n        type: \"regulatory\",\n        title: \"Extended Producer Responsibility (EPR) Expansion\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"EPR schemes expanding across EU member states, creating additional compliance obligations and costs.\",\n        details: \"EPR fees ranging €50-200 per tonne of plastic packaging. Borouge's annual EPR liability estimated at €25-35M across EU markets.\",\n        confidence: 90,\n        timeline: \"Implementation: 2024-2026\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish EU Regulatory Compliance Task Force\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$5M\",\n        description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n      }, {\n        priority: \"High\",\n        action: \"Secure Chemical Recycling Technology Partnerships\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$200-300M\",\n        description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Sustainable Product Line Development\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$150M\",\n        description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n      }, {\n        priority: \"Medium\",\n        action: \"Establish European Recycling Hub\",\n        timeline: \"24 months (Q4 2025)\",\n        investment: \"$400-500M\",\n        description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n      }],\n      competitiveBenchmarking: [{\n        company: \"SABIC\",\n        strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n        advantage: \"First-mover in chemical recycling, strong EU presence\",\n        weakness: \"Higher cost base, limited feedstock security\"\n      }, {\n        company: \"Dow\",\n        strategy: \"Advanced recycling partnerships, circular design principles\",\n        advantage: \"Technology leadership, established partnerships\",\n        weakness: \"Focus on specialty applications, limited commodity exposure\"\n      }, {\n        company: \"LyondellBasell\",\n        strategy: \"Molecular recycling technology, circular economy solutions\",\n        advantage: \"Integrated technology development, scale advantages\",\n        weakness: \"Limited EU manufacturing footprint\"\n      }],\n      sources: [{\n        title: \"EU Packaging & Packaging Waste Regulation\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2024-01-15\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Environment Agency Circular Economy Report\",\n        url: \"eea.europa.eu\",\n        date: \"2024-02-01\",\n        type: \"research\",\n        confidence: \"High\"\n      }, {\n        title: \"SABIC Circular Economy Strategy Update\",\n        url: \"sabic.com\",\n        date: \"2024-01-30\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"Plastics Europe Market Data 2024\",\n        url: \"plasticseurope.org\",\n        date: \"2024-02-15\",\n        type: \"industry\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey Circular Economy in Chemicals\",\n        url: \"mckinsey.com\",\n        date: \"2024-01-20\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }, {\n        title: \"Wood Mackenzie Petrochemicals Outlook\",\n        url: \"woodmac.com\",\n        date: \"2024-02-10\",\n        type: \"market\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [{\n        type: \"financial\",\n        title: \"Direct CBAM Cost Impact\",\n        impact: \"High\",\n        urgency: \"Critical\",\n        description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n        details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n        confidence: 92,\n        timeline: \"Implementation: January 2026\"\n      }, {\n        type: \"competitive\",\n        title: \"Competitive Advantage Opportunity\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n        details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n        confidence: 88,\n        timeline: \"Advantage period: 2026-2035\"\n      }, {\n        type: \"technology\",\n        title: \"Carbon Reduction Investment Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n        details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n        confidence: 85,\n        timeline: \"Investment period: 2024-2030\"\n      }, {\n        type: \"regulatory\",\n        title: \"CBAM Reporting and Verification Requirements\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n        details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n        confidence: 95,\n        timeline: \"Reporting starts: October 2023\"\n      }, {\n        type: \"market\",\n        title: \"Premium Low-Carbon Product Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n        details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n        confidence: 78,\n        timeline: \"Market development: 2025-2030\"\n      }],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Implement Comprehensive Carbon Accounting System\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$10M\",\n        description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n      }, {\n        priority: \"High\",\n        action: \"Accelerate Renewable Energy Integration\",\n        timeline: \"18 months (Q3 2025)\",\n        investment: \"$200M\",\n        description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n      }, {\n        priority: \"High\",\n        action: \"Develop Low-Carbon Product Certification\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$5M\",\n        description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n      }],\n      sources: [{\n        title: \"EU CBAM Regulation 2023/956\",\n        url: \"eur-lex.europa.eu\",\n        date: \"2023-05-17\",\n        type: \"regulation\",\n        confidence: \"Official\"\n      }, {\n        title: \"European Commission CBAM Implementation Guide\",\n        url: \"taxation-customs.ec.europa.eu\",\n        date: \"2024-01-10\",\n        type: \"guidance\",\n        confidence: \"Official\"\n      }, {\n        title: \"IEA Petrochemicals Carbon Intensity Database\",\n        url: \"iea.org\",\n        date: \"2024-02-05\",\n        type: \"research\",\n        confidence: \"High\"\n      }]\n    };\n  };\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [{\n        type: \"environmental\",\n        title: \"Decarbonization Pathway Requirements\",\n        impact: \"High\",\n        urgency: \"High\",\n        description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n        details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n        confidence: 90,\n        timeline: \"Transformation period: 2024-2050\"\n      }, {\n        type: \"social\",\n        title: \"UAE Emiratization and Skills Development\",\n        impact: \"Medium\",\n        urgency: \"High\",\n        description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n        details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n        confidence: 85,\n        timeline: \"Achievement target: 2030\"\n      }, {\n        type: \"governance\",\n        title: \"ESG Reporting and Transparency Enhancement\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n        details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n        confidence: 88,\n        timeline: \"Full compliance: 2025\"\n      }, {\n        type: \"financial\",\n        title: \"Sustainable Finance and Green Bonds Opportunity\",\n        impact: \"High\",\n        urgency: \"Medium\",\n        description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n        details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n        confidence: 82,\n        timeline: \"Financing window: 2024-2027\"\n      }, {\n        type: \"technology\",\n        title: \"Digital ESG Management Platform\",\n        impact: \"Medium\",\n        urgency: \"Medium\",\n        description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n        details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n        confidence: 75,\n        timeline: \"Implementation: 2024-2025\"\n      }],\n      strategicRecommendations: [{\n        priority: \"Critical\",\n        action: \"Establish Chief Sustainability Officer Role\",\n        timeline: \"Immediate (Q1 2024)\",\n        investment: \"$2M\",\n        description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n      }, {\n        priority: \"High\",\n        action: \"Launch Comprehensive Decarbonization Program\",\n        timeline: \"6 months (Q2 2024)\",\n        investment: \"$500M\",\n        description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n      }, {\n        priority: \"High\",\n        action: \"Implement Advanced Emiratization Strategy\",\n        timeline: \"12 months (Q4 2024)\",\n        investment: \"$100M\",\n        description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n      }],\n      sources: [{\n        title: \"UAE Vision 2071 Strategic Framework\",\n        url: \"government.ae\",\n        date: \"2023-12-01\",\n        type: \"policy\",\n        confidence: \"Official\"\n      }, {\n        title: \"ADNOC Sustainability Strategy 2030\",\n        url: \"adnoc.ae\",\n        date: \"2024-01-15\",\n        type: \"corporate\",\n        confidence: \"High\"\n      }, {\n        title: \"McKinsey ESG in Chemicals Industry\",\n        url: \"mckinsey.com\",\n        date: \"2024-02-01\",\n        type: \"consulting\",\n        confidence: \"Medium\"\n      }]\n    };\n  };\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n  const copyMessage = content => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"conversation-view\",\n    initial: {\n      opacity: 0,\n      x: 20\n    },\n    animate: {\n      opacity: 1,\n      x: 0\n    },\n    exit: {\n      opacity: 0,\n      x: -20\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversation-header\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"back-btn\",\n        onClick: onBack,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), \"Back to Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-actions\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          onClick: exportToPDF,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"action-btn\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(Share2, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), \"Share\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `message ${message.type}`,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-response\",\n              children: typeof message.content === 'object' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"intelligence-report\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"report-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"report-title-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: message.content.reportType || 'ESG Intelligence Report'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"report-meta\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"report-timestamp\",\n                        children: [\"Generated: \", new Date().toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"report-confidence\",\n                        children: \"High Confidence Analysis\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 513,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"copy-btn\",\n                    onClick: () => copyMessage(message.content),\n                    children: /*#__PURE__*/_jsxDEV(Copy, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 25\n                }, this), message.content.executiveSummary && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"executive-summary\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Executive Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: message.content.executiveSummary\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 27\n                }, this), message.content.marketImpact && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"market-impact-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Market Impact Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"impact-metrics\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Revenue at Risk\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value risk\",\n                        children: message.content.marketImpact.revenueAtRisk\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Investment Required\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value investment\",\n                        children: message.content.marketImpact.investmentRequired\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Timeline\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value timeline\",\n                        children: message.content.marketImpact.timelineForCompliance\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"impact-metric\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-label\",\n                        children: \"Market Opportunity\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 548,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value opportunity\",\n                        children: message.content.marketImpact.marketOpportunity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"key-findings\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Key Findings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 27\n                  }, this), message.content.keyFindings.map((finding, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"finding-card\",\n                    initial: {\n                      opacity: 0,\n                      x: -20\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: index * 0.1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"finding-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-icon\",\n                        children: [finding.type === 'regulatory' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 567,\n                          columnNumber: 69\n                        }, this), finding.type === 'financial' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 568,\n                          columnNumber: 68\n                        }, this), finding.type === 'competitive' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 569,\n                          columnNumber: 70\n                        }, this), finding.type === 'market' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 570,\n                          columnNumber: 65\n                        }, this), finding.type === 'technology' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 571,\n                          columnNumber: 69\n                        }, this), finding.type === 'environmental' && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 572,\n                          columnNumber: 72\n                        }, this), finding.type === 'social' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 573,\n                          columnNumber: 65\n                        }, this), finding.type === 'governance' && /*#__PURE__*/_jsxDEV(Info, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 574,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-title\",\n                        children: finding.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"finding-badges\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `impact-badge ${finding.impact.toLowerCase()}`,\n                          children: [finding.impact, \" Impact\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 578,\n                          columnNumber: 35\n                        }, this), finding.urgency && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `urgency-badge ${finding.urgency.toLowerCase()}`,\n                          children: finding.urgency\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 582,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"finding-description\",\n                      children: finding.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 31\n                    }, this), finding.details && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"finding-details\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: finding.details\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 33\n                    }, this), finding.timeline && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"finding-timeline\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Timeline:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 35\n                      }, this), \" \", finding.timeline]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"confidence-bar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"confidence-label\",\n                        children: [\"Confidence: \", finding.confidence, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"confidence-progress\",\n                        children: /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"confidence-fill\",\n                          initial: {\n                            width: 0\n                          },\n                          animate: {\n                            width: `${finding.confidence}%`\n                          },\n                          transition: {\n                            duration: 1,\n                            delay: 0.5\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 602,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 29\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sources-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Sources\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"sources-grid\",\n                    children: message.content.sources.map((source, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"source-card\",\n                      initial: {\n                        opacity: 0,\n                        y: 10\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.3 + index * 0.1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"source-header\",\n                        children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 626,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-title\",\n                          children: source.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 627,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 625,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"source-meta\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-url\",\n                          children: source.url\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 630,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"source-date\",\n                          children: source.date\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 631,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 629,\n                        columnNumber: 33\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-items\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Recommended Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: message.content.actionItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.li, {\n                      initial: {\n                        opacity: 0,\n                        x: -10\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0\n                      },\n                      transition: {\n                        delay: 0.5 + index * 0.1\n                      },\n                      children: item\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"simple-response\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: message.timestamp.toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"loading-message\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-content\",\n          children: [/*#__PURE__*/_jsxDEV(Loader2, {\n            className: \"loading-spinner\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Analyzing ESG data and regulations...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-input-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-input-box\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask a follow-up question about ESG intelligence...\",\n          className: \"message-input\",\n          rows: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"send-btn\",\n          onClick: handleSendMessage,\n          disabled: !newMessage.trim() || isLoading,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 684,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 444,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationView, \"u/uHMONfEqCRbPpU5gyD/zw7tTk=\");\n_c = ConversationView;\nexport default ConversationView;\nvar _c;\n$RefreshReg$(_c, \"ConversationView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "ArrowLeft", "Send", "Download", "ExternalLink", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Loader2", "Copy", "Share2", "jsxDEV", "_jsxDEV", "ConversationView", "initialQuery", "onBack", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "isLoading", "setIsLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "userMessage", "id", "type", "content", "timestamp", "Date", "setTimeout", "aiResponse", "generateMockResponse", "prev", "query", "getReportByQuery", "lowerQuery", "toLowerCase", "includes", "generateEURegulationReport", "generateCBAMReport", "generateCircularEconomyReport", "generateCompetitorReport", "generateMarketTrendsReport", "generateComprehensiveESGReport", "reportType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyFindings", "title", "impact", "urgency", "description", "details", "confidence", "timeline", "marketImpact", "revenueAtRisk", "investmentRequired", "timelineForCompliance", "marketOpportunity", "riskAssessment", "high", "medium", "low", "strategicRecommendations", "priority", "action", "investment", "competitiveBenchmarking", "company", "strategy", "advantage", "weakness", "sources", "url", "date", "handleSendMessage", "trim", "length", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "exportToPDF", "console", "log", "copyMessage", "navigator", "clipboard", "writeText", "JSON", "stringify", "div", "className", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "children", "button", "onClick", "whileHover", "scale", "whileTap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "message", "y", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "finding", "index", "delay", "width", "source", "actionItems", "item", "li", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ArrowLeft,\n  Send,\n  Download,\n  ExternalLink,\n  TrendingUp,\n  AlertTriangle,\n  Info,\n  Loader2,\n  Copy,\n  Share2\n} from 'lucide-react';\nimport './ConversationView.css';\n\nconst ConversationView = ({ initialQuery, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (initialQuery) {\n      // Add initial user message\n      const userMessage = {\n        id: 1,\n        type: 'user',\n        content: initialQuery,\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n      setIsLoading(true);\n\n      // Simulate AI response after delay\n      setTimeout(() => {\n        const aiResponse = {\n          id: 2,\n          type: 'assistant',\n          content: generateMockResponse(initialQuery),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [initialQuery]);\n\n  const generateMockResponse = (query) => {\n    // Generate comprehensive intelligence reports based on query type\n    const getReportByQuery = (query) => {\n      const lowerQuery = query.toLowerCase();\n\n      if (lowerQuery.includes('eu') || lowerQuery.includes('regulation') || lowerQuery.includes('plastic')) {\n        return generateEURegulationReport();\n      } else if (lowerQuery.includes('cbam') || lowerQuery.includes('carbon')) {\n        return generateCBAMReport();\n      } else if (lowerQuery.includes('circular') || lowerQuery.includes('recycling')) {\n        return generateCircularEconomyReport();\n      } else if (lowerQuery.includes('sabic') || lowerQuery.includes('competitor')) {\n        return generateCompetitorReport();\n      } else if (lowerQuery.includes('market') || lowerQuery.includes('trend')) {\n        return generateMarketTrendsReport();\n      } else {\n        return generateComprehensiveESGReport();\n      }\n    };\n\n    return getReportByQuery(query);\n  };\n\n  const generateEURegulationReport = () => {\n    return {\n      reportType: \"Regulatory Intelligence Report\",\n      executiveSummary: \"The EU's new packaging regulations present significant compliance challenges and opportunities for Borouge's European operations. With 65% of Borouge's polyethylene exports destined for EU markets, immediate strategic action is required to maintain market position and capitalize on emerging opportunities in sustainable packaging solutions.\",\n      keyFindings: [\n        {\n          type: \"regulatory\",\n          title: \"EU Packaging & Packaging Waste Regulation (PPWR) 2024\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"New mandatory recycled content requirements: 30% for plastic packaging by 2030, 65% by 2040. Affects €2.1B of Borouge's annual EU revenue stream.\",\n          details: \"The regulation specifically targets polyethylene and polypropylene packaging, Borouge's core products. Non-compliance results in market access restrictions and potential fines up to 4% of annual turnover.\",\n          confidence: 98,\n          timeline: \"Implementation: Jan 2025, Full compliance: 2030\"\n        },\n        {\n          type: \"financial\",\n          title: \"Compliance Investment Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Estimated $800M-1.2B investment needed for recycling infrastructure and product reformulation to meet EU standards.\",\n          details: \"Investment breakdown: $400M for chemical recycling facilities, $300M for mechanical recycling partnerships, $200M for R&D and product development, $100M for supply chain modifications.\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2027\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Pricing Opportunity\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Sustainable packaging commands 15-25% price premium in EU markets, potentially offsetting compliance costs.\",\n          details: \"Early movers in sustainable polyethylene have captured premium pricing. Borouge could achieve €150-250M additional annual revenue through premium sustainable product lines.\",\n          confidence: 78,\n          timeline: \"Market opportunity: 2025-2030\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Landscape Shift\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"SABIC and Dow advancing rapidly in recycled content integration, potentially gaining first-mover advantage.\",\n          details: \"SABIC's €2B circular economy investment and Dow's advanced recycling partnerships position them ahead. Borouge risks losing 15-20% EU market share without immediate action.\",\n          confidence: 92,\n          timeline: \"Competitive threat: Immediate\"\n        },\n        {\n          type: \"technology\",\n          title: \"Chemical Recycling Technology Gap\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Current mechanical recycling insufficient for quality requirements; chemical recycling technology partnerships essential.\",\n          details: \"Pyrolysis and depolymerization technologies can achieve virgin-quality recycled content. Strategic partnerships with Plastic Energy, Quantafuel, or Agilyx recommended.\",\n          confidence: 88,\n          timeline: \"Technology deployment: 2025-2026\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"Extended Producer Responsibility (EPR) Expansion\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"EPR schemes expanding across EU member states, creating additional compliance obligations and costs.\",\n          details: \"EPR fees ranging €50-200 per tonne of plastic packaging. Borouge's annual EPR liability estimated at €25-35M across EU markets.\",\n          confidence: 90,\n          timeline: \"Implementation: 2024-2026\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€2.1B (65% of EU sales)\",\n        investmentRequired: \"$800M-1.2B\",\n        timelineForCompliance: \"18 months critical path\",\n        marketOpportunity: \"€150-250M premium pricing potential\"\n      },\n      riskAssessment: {\n        high: [\"Market access restrictions\", \"Competitive disadvantage\", \"Regulatory penalties\"],\n        medium: [\"Supply chain disruption\", \"Technology integration challenges\", \"Customer relationship impact\"],\n        low: [\"Reputational impact\", \"Talent acquisition challenges\"]\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish EU Regulatory Compliance Task Force\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$5M\",\n          description: \"Cross-functional team to coordinate compliance strategy, regulatory monitoring, and stakeholder engagement across EU markets.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Secure Chemical Recycling Technology Partnerships\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$200-300M\",\n          description: \"Strategic partnerships or acquisitions with proven chemical recycling technology providers to ensure recycled content supply.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Sustainable Product Line Development\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$150M\",\n          description: \"Accelerated R&D program for high-recycled-content polyethylene grades targeting premium packaging applications.\"\n        },\n        {\n          priority: \"Medium\",\n          action: \"Establish European Recycling Hub\",\n          timeline: \"24 months (Q4 2025)\",\n          investment: \"$400-500M\",\n          description: \"Dedicated recycling facility in strategic EU location to ensure supply security and cost optimization.\"\n        }\n      ],\n      competitiveBenchmarking: [\n        {\n          company: \"SABIC\",\n          strategy: \"€2B circular economy investment, 1M tonnes recycled content by 2030\",\n          advantage: \"First-mover in chemical recycling, strong EU presence\",\n          weakness: \"Higher cost base, limited feedstock security\"\n        },\n        {\n          company: \"Dow\",\n          strategy: \"Advanced recycling partnerships, circular design principles\",\n          advantage: \"Technology leadership, established partnerships\",\n          weakness: \"Focus on specialty applications, limited commodity exposure\"\n        },\n        {\n          company: \"LyondellBasell\",\n          strategy: \"Molecular recycling technology, circular economy solutions\",\n          advantage: \"Integrated technology development, scale advantages\",\n          weakness: \"Limited EU manufacturing footprint\"\n        }\n      ],\n      sources: [\n        { title: \"EU Packaging & Packaging Waste Regulation\", url: \"eur-lex.europa.eu\", date: \"2024-01-15\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Environment Agency Circular Economy Report\", url: \"eea.europa.eu\", date: \"2024-02-01\", type: \"research\", confidence: \"High\" },\n        { title: \"SABIC Circular Economy Strategy Update\", url: \"sabic.com\", date: \"2024-01-30\", type: \"corporate\", confidence: \"High\" },\n        { title: \"Plastics Europe Market Data 2024\", url: \"plasticseurope.org\", date: \"2024-02-15\", type: \"industry\", confidence: \"High\" },\n        { title: \"McKinsey Circular Economy in Chemicals\", url: \"mckinsey.com\", date: \"2024-01-20\", type: \"consulting\", confidence: \"Medium\" },\n        { title: \"Wood Mackenzie Petrochemicals Outlook\", url: \"woodmac.com\", date: \"2024-02-10\", type: \"market\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateCBAMReport = () => {\n    return {\n      reportType: \"Carbon Border Adjustment Mechanism (CBAM) Impact Analysis\",\n      executiveSummary: \"CBAM implementation will significantly impact Borouge's EU export economics, with estimated additional costs of €45-75M annually. However, strategic positioning in low-carbon production could create competitive advantages and new market opportunities worth €200-300M by 2030.\",\n      keyFindings: [\n        {\n          type: \"financial\",\n          title: \"Direct CBAM Cost Impact\",\n          impact: \"High\",\n          urgency: \"Critical\",\n          description: \"Estimated €45-75M annual CBAM liability for Borouge's EU polyethylene exports starting 2026.\",\n          details: \"Based on current carbon intensity of 2.1 tCO2/tonne PE and export volume of 850,000 tonnes/year to EU. CBAM price projected at €60-85/tCO2.\",\n          confidence: 92,\n          timeline: \"Implementation: January 2026\"\n        },\n        {\n          type: \"competitive\",\n          title: \"Competitive Advantage Opportunity\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"UAE's renewable energy transition positions Borouge favorably vs. coal-dependent competitors in Asia.\",\n          details: \"Borouge's carbon intensity 40% lower than Chinese producers, 25% lower than US Gulf Coast. Potential to capture market share from high-carbon producers.\",\n          confidence: 88,\n          timeline: \"Advantage period: 2026-2035\"\n        },\n        {\n          type: \"technology\",\n          title: \"Carbon Reduction Investment Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"€300-500M investment needed to achieve 50% carbon intensity reduction by 2030.\",\n          details: \"Investment areas: renewable energy integration (€200M), process optimization (€150M), carbon capture utilization (€100M), green hydrogen (€50M).\",\n          confidence: 85,\n          timeline: \"Investment period: 2024-2030\"\n        },\n        {\n          type: \"regulatory\",\n          title: \"CBAM Reporting and Verification Requirements\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"Complex reporting obligations requiring detailed carbon accounting and third-party verification systems.\",\n          details: \"Quarterly reporting of embedded carbon, verification by accredited bodies, potential penalties for non-compliance up to €50/tCO2 equivalent.\",\n          confidence: 95,\n          timeline: \"Reporting starts: October 2023\"\n        },\n        {\n          type: \"market\",\n          title: \"Premium Low-Carbon Product Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Growing demand for low-carbon polyethylene could command 10-15% price premium in EU markets.\",\n          details: \"Major brands (Unilever, P&G, Nestlé) committing to low-carbon packaging. Market size estimated at €500M by 2030 for certified low-carbon PE.\",\n          confidence: 78,\n          timeline: \"Market development: 2025-2030\"\n        }\n      ],\n      marketImpact: {\n        revenueAtRisk: \"€45-75M annual CBAM costs\",\n        investmentRequired: \"€300-500M carbon reduction\",\n        timelineForCompliance: \"30 months to full implementation\",\n        marketOpportunity: \"€200-300M low-carbon premium potential\"\n      },\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Implement Comprehensive Carbon Accounting System\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$10M\",\n          description: \"Deploy enterprise carbon management system for accurate CBAM reporting and carbon footprint optimization.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Accelerate Renewable Energy Integration\",\n          timeline: \"18 months (Q3 2025)\",\n          investment: \"$200M\",\n          description: \"Partner with ADNOC for renewable energy supply agreements and on-site solar installations to reduce carbon intensity.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Develop Low-Carbon Product Certification\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$5M\",\n          description: \"Establish third-party verified low-carbon product lines for premium EU market positioning.\"\n        }\n      ],\n      sources: [\n        { title: \"EU CBAM Regulation 2023/956\", url: \"eur-lex.europa.eu\", date: \"2023-05-17\", type: \"regulation\", confidence: \"Official\" },\n        { title: \"European Commission CBAM Implementation Guide\", url: \"taxation-customs.ec.europa.eu\", date: \"2024-01-10\", type: \"guidance\", confidence: \"Official\" },\n        { title: \"IEA Petrochemicals Carbon Intensity Database\", url: \"iea.org\", date: \"2024-02-05\", type: \"research\", confidence: \"High\" }\n      ]\n    };\n  };\n\n  const generateComprehensiveESGReport = () => {\n    return {\n      reportType: \"Comprehensive ESG Intelligence Report\",\n      executiveSummary: \"Borouge faces a complex ESG landscape requiring integrated strategy across environmental compliance, social responsibility, and governance excellence. Key priorities include decarbonization ($500M investment), circular economy transition ($300M), and stakeholder engagement enhancement to maintain social license to operate in the UAE and global markets.\",\n      keyFindings: [\n        {\n          type: \"environmental\",\n          title: \"Decarbonization Pathway Requirements\",\n          impact: \"High\",\n          urgency: \"High\",\n          description: \"Net-zero commitment by 2050 requires 70% emissions reduction, demanding fundamental operational transformation.\",\n          details: \"Current emissions: 4.2M tCO2e annually. Reduction pathway: 30% by 2030 (renewable energy), 50% by 2040 (process innovation), 70% by 2050 (breakthrough technologies).\",\n          confidence: 90,\n          timeline: \"Transformation period: 2024-2050\"\n        },\n        {\n          type: \"social\",\n          title: \"UAE Emiratization and Skills Development\",\n          impact: \"Medium\",\n          urgency: \"High\",\n          description: \"UAE Vision 2071 requires 75% Emirati workforce in strategic sectors, necessitating accelerated localization programs.\",\n          details: \"Current Emiratization: 42%. Target: 75% by 2030. Investment required: $50M for training programs, $30M for educational partnerships, $20M for retention initiatives.\",\n          confidence: 85,\n          timeline: \"Achievement target: 2030\"\n        },\n        {\n          type: \"governance\",\n          title: \"ESG Reporting and Transparency Enhancement\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Increasing investor and stakeholder demands for comprehensive ESG disclosure and third-party verification.\",\n          details: \"Current ESG reporting covers 60% of material topics. Gap analysis identifies needs in Scope 3 emissions, biodiversity impact, and social value measurement.\",\n          confidence: 88,\n          timeline: \"Full compliance: 2025\"\n        },\n        {\n          type: \"financial\",\n          title: \"Sustainable Finance and Green Bonds Opportunity\",\n          impact: \"High\",\n          urgency: \"Medium\",\n          description: \"Access to $2-3B in green financing for sustainability investments at favorable rates (2-3% below conventional).\",\n          details: \"Green bond market for chemicals growing 25% annually. Borouge eligible for sustainability-linked loans tied to carbon reduction and circular economy targets.\",\n          confidence: 82,\n          timeline: \"Financing window: 2024-2027\"\n        },\n        {\n          type: \"technology\",\n          title: \"Digital ESG Management Platform\",\n          impact: \"Medium\",\n          urgency: \"Medium\",\n          description: \"Integrated ESG data management and reporting platform essential for stakeholder transparency and regulatory compliance.\",\n          details: \"Platform requirements: real-time emissions monitoring, social impact tracking, governance metrics dashboard, automated reporting capabilities.\",\n          confidence: 75,\n          timeline: \"Implementation: 2024-2025\"\n        }\n      ],\n      strategicRecommendations: [\n        {\n          priority: \"Critical\",\n          action: \"Establish Chief Sustainability Officer Role\",\n          timeline: \"Immediate (Q1 2024)\",\n          investment: \"$2M\",\n          description: \"Senior executive position to lead integrated ESG strategy and ensure board-level accountability for sustainability performance.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Launch Comprehensive Decarbonization Program\",\n          timeline: \"6 months (Q2 2024)\",\n          investment: \"$500M\",\n          description: \"Multi-year program covering renewable energy, process optimization, and breakthrough technology development.\"\n        },\n        {\n          priority: \"High\",\n          action: \"Implement Advanced Emiratization Strategy\",\n          timeline: \"12 months (Q4 2024)\",\n          investment: \"$100M\",\n          description: \"Comprehensive talent development program including partnerships with UAE universities and vocational training institutes.\"\n        }\n      ],\n      sources: [\n        { title: \"UAE Vision 2071 Strategic Framework\", url: \"government.ae\", date: \"2023-12-01\", type: \"policy\", confidence: \"Official\" },\n        { title: \"ADNOC Sustainability Strategy 2030\", url: \"adnoc.ae\", date: \"2024-01-15\", type: \"corporate\", confidence: \"High\" },\n        { title: \"McKinsey ESG in Chemicals Industry\", url: \"mckinsey.com\", date: \"2024-02-01\", type: \"consulting\", confidence: \"Medium\" }\n      ]\n    };\n  };\n\n  const handleSendMessage = () => {\n    if (newMessage.trim()) {\n      const userMessage = {\n        id: messages.length + 1,\n        type: 'user',\n        content: newMessage,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setNewMessage('');\n      setIsLoading(true);\n\n      // Simulate AI response\n      setTimeout(() => {\n        const aiResponse = {\n          id: messages.length + 2,\n          type: 'assistant',\n          content: generateMockResponse(newMessage),\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        setIsLoading(false);\n      }, 1500);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const exportToPDF = () => {\n    // Mock export functionality\n    console.log('Exporting conversation to PDF...');\n    // In real implementation, use jsPDF or similar\n  };\n\n  const copyMessage = (content) => {\n    navigator.clipboard.writeText(typeof content === 'string' ? content : JSON.stringify(content));\n  };\n\n  return (\n    <motion.div\n      className=\"conversation-view\"\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      exit={{ opacity: 0, x: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"conversation-header\">\n        <motion.button\n          className=\"back-btn\"\n          onClick={onBack}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <ArrowLeft size={20} />\n          Back to Search\n        </motion.button>\n\n        <div className=\"conversation-actions\">\n          <motion.button\n            className=\"action-btn\"\n            onClick={exportToPDF}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Download size={18} />\n            Export\n          </motion.button>\n          <motion.button\n            className=\"action-btn\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Share2 size={18} />\n            Share\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"messages-container\">\n        <AnimatePresence>\n          {messages.map((message) => (\n            <motion.div\n              key={message.id}\n              className={`message ${message.type}`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {message.type === 'user' ? (\n                <div className=\"user-message\">\n                  <div className=\"message-content\">{message.content}</div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"ai-message\">\n                  <div className=\"ai-response\">\n                    {typeof message.content === 'object' ? (\n                      <div className=\"intelligence-report\">\n                        <div className=\"report-header\">\n                          <div className=\"report-title-section\">\n                            <h3>{message.content.reportType || 'ESG Intelligence Report'}</h3>\n                            <div className=\"report-meta\">\n                              <span className=\"report-timestamp\">Generated: {new Date().toLocaleDateString()}</span>\n                              <span className=\"report-confidence\">High Confidence Analysis</span>\n                            </div>\n                          </div>\n                          <button\n                            className=\"copy-btn\"\n                            onClick={() => copyMessage(message.content)}\n                          >\n                            <Copy size={16} />\n                          </button>\n                        </div>\n\n                        {message.content.executiveSummary && (\n                          <div className=\"executive-summary\">\n                            <h4>Executive Summary</h4>\n                            <p>{message.content.executiveSummary}</p>\n                          </div>\n                        )}\n\n                        {message.content.marketImpact && (\n                          <div className=\"market-impact-section\">\n                            <h4>Market Impact Analysis</h4>\n                            <div className=\"impact-metrics\">\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Revenue at Risk</span>\n                                <span className=\"metric-value risk\">{message.content.marketImpact.revenueAtRisk}</span>\n                              </div>\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Investment Required</span>\n                                <span className=\"metric-value investment\">{message.content.marketImpact.investmentRequired}</span>\n                              </div>\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Timeline</span>\n                                <span className=\"metric-value timeline\">{message.content.marketImpact.timelineForCompliance}</span>\n                              </div>\n                              <div className=\"impact-metric\">\n                                <span className=\"metric-label\">Market Opportunity</span>\n                                <span className=\"metric-value opportunity\">{message.content.marketImpact.marketOpportunity}</span>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        <div className=\"key-findings\">\n                          <h4>Key Findings</h4>\n                          {message.content.keyFindings.map((finding, index) => (\n                            <motion.div\n                              key={index}\n                              className=\"finding-card\"\n                              initial={{ opacity: 0, x: -20 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              transition={{ delay: index * 0.1 }}\n                            >\n                              <div className=\"finding-header\">\n                                <div className=\"finding-icon\">\n                                  {finding.type === 'regulatory' && <AlertTriangle size={16} />}\n                                  {finding.type === 'financial' && <TrendingUp size={16} />}\n                                  {finding.type === 'competitive' && <Info size={16} />}\n                                  {finding.type === 'market' && <TrendingUp size={16} />}\n                                  {finding.type === 'technology' && <Info size={16} />}\n                                  {finding.type === 'environmental' && <AlertTriangle size={16} />}\n                                  {finding.type === 'social' && <Info size={16} />}\n                                  {finding.type === 'governance' && <Info size={16} />}\n                                </div>\n                                <div className=\"finding-title\">{finding.title}</div>\n                                <div className=\"finding-badges\">\n                                  <div className={`impact-badge ${finding.impact.toLowerCase()}`}>\n                                    {finding.impact} Impact\n                                  </div>\n                                  {finding.urgency && (\n                                    <div className={`urgency-badge ${finding.urgency.toLowerCase()}`}>\n                                      {finding.urgency}\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n                              <p className=\"finding-description\">{finding.description}</p>\n                              {finding.details && (\n                                <div className=\"finding-details\">\n                                  <p>{finding.details}</p>\n                                </div>\n                              )}\n                              {finding.timeline && (\n                                <div className=\"finding-timeline\">\n                                  <strong>Timeline:</strong> {finding.timeline}\n                                </div>\n                              )}\n                              <div className=\"confidence-bar\">\n                                <div className=\"confidence-label\">Confidence: {finding.confidence}%</div>\n                                <div className=\"confidence-progress\">\n                                  <motion.div\n                                    className=\"confidence-fill\"\n                                    initial={{ width: 0 }}\n                                    animate={{ width: `${finding.confidence}%` }}\n                                    transition={{ duration: 1, delay: 0.5 }}\n                                  />\n                                </div>\n                              </div>\n                            </motion.div>\n                          ))}\n                        </div>\n\n                        <div className=\"sources-section\">\n                          <h4>Sources</h4>\n                          <div className=\"sources-grid\">\n                            {message.content.sources.map((source, index) => (\n                              <motion.div\n                                key={index}\n                                className=\"source-card\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: 0.3 + index * 0.1 }}\n                              >\n                                <div className=\"source-header\">\n                                  <ExternalLink size={14} />\n                                  <span className=\"source-title\">{source.title}</span>\n                                </div>\n                                <div className=\"source-meta\">\n                                  <span className=\"source-url\">{source.url}</span>\n                                  <span className=\"source-date\">{source.date}</span>\n                                </div>\n                              </motion.div>\n                            ))}\n                          </div>\n                        </div>\n\n                        <div className=\"action-items\">\n                          <h4>Recommended Actions</h4>\n                          <ul>\n                            {message.content.actionItems.map((item, index) => (\n                              <motion.li\n                                key={index}\n                                initial={{ opacity: 0, x: -10 }}\n                                animate={{ opacity: 1, x: 0 }}\n                                transition={{ delay: 0.5 + index * 0.1 }}\n                              >\n                                {item}\n                              </motion.li>\n                            ))}\n                          </ul>\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"simple-response\">{message.content}</div>\n                    )}\n                  </div>\n                  <div className=\"message-time\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            className=\"loading-message\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"loading-content\">\n              <Loader2 className=\"loading-spinner\" size={20} />\n              <span>Analyzing ESG data and regulations...</span>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"message-input-container\">\n        <div className=\"message-input-box\">\n          <textarea\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask a follow-up question about ESG intelligence...\"\n            className=\"message-input\"\n            rows=\"1\"\n          />\n          <motion.button\n            className=\"send-btn\"\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() || isLoading}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Send size={18} />\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ConversationView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,QACD,cAAc;AACrB,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM2B,cAAc,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED/B,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEdpB,SAAS,CAAC,MAAM;IACd,IAAIiB,YAAY,EAAE;MAChB;MACA,MAAMe,WAAW,GAAG;QAClBC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAElB,YAAY;QACrBmB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDhB,WAAW,CAAC,CAACW,WAAW,CAAC,CAAC;MAC1BP,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAa,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAACvB,YAAY,CAAC;UAC3CmB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDhB,WAAW,CAACoB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,UAAU,CAAC,CAAC;QAC1Cd,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAElB,MAAMuB,oBAAoB,GAAIE,KAAK,IAAK;IACtC;IACA,MAAMC,gBAAgB,GAAID,KAAK,IAAK;MAClC,MAAME,UAAU,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;MAEtC,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;QACpG,OAAOC,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIH,UAAU,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvE,OAAOE,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIJ,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC9E,OAAOG,6BAA6B,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIL,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC5E,OAAOI,wBAAwB,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIN,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACxE,OAAOK,0BAA0B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOC,8BAA8B,CAAC,CAAC;MACzC;IACF,CAAC;IAED,OAAOT,gBAAgB,CAACD,KAAK,CAAC;EAChC,CAAC;EAED,MAAMK,0BAA0B,GAAGA,CAAA,KAAM;IACvC,OAAO;MACLM,UAAU,EAAE,gCAAgC;MAC5CC,gBAAgB,EAAE,uVAAuV;MACzWC,WAAW,EAAE,CACX;QACErB,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,uDAAuD;QAC9DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBC,WAAW,EAAE,mJAAmJ;QAChKC,OAAO,EAAE,8MAA8M;QACvNC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,WAAW;QACjBsB,KAAK,EAAE,oCAAoC;QAC3CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,qHAAqH;QAClIC,OAAO,EAAE,0LAA0L;QACnMC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,QAAQ;QACdsB,KAAK,EAAE,6BAA6B;QACpCC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBC,WAAW,EAAE,6GAA6G;QAC1HC,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,aAAa;QACnBsB,KAAK,EAAE,6BAA6B;QACpCC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,6GAA6G;QAC1HC,OAAO,EAAE,8KAA8K;QACvLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,mCAAmC;QAC1CC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,2HAA2H;QACxIC,OAAO,EAAE,yKAAyK;QAClLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,kDAAkD;QACzDC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBC,WAAW,EAAE,sGAAsG;QACnHC,OAAO,EAAE,iIAAiI;QAC1IC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,yBAAyB;QACxCC,kBAAkB,EAAE,YAAY;QAChCC,qBAAqB,EAAE,yBAAyB;QAChDC,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdC,IAAI,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,sBAAsB,CAAC;QACxFC,MAAM,EAAE,CAAC,yBAAyB,EAAE,mCAAmC,EAAE,8BAA8B,CAAC;QACxGC,GAAG,EAAE,CAAC,qBAAqB,EAAE,+BAA+B;MAC9D,CAAC;MACDC,wBAAwB,EAAE,CACxB;QACEC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,+CAA+C;QACvDZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,mDAAmD;QAC3DZ,QAAQ,EAAE,oBAAoB;QAC9Ba,UAAU,EAAE,WAAW;QACvBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,6CAA6C;QACrDZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE,kCAAkC;QAC1CZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,WAAW;QACvBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDiB,uBAAuB,EAAE,CACvB;QACEC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,qEAAqE;QAC/EC,SAAS,EAAE,uDAAuD;QAClEC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,6DAA6D;QACvEC,SAAS,EAAE,iDAAiD;QAC5DC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE,4DAA4D;QACtEC,SAAS,EAAE,qDAAqD;QAChEC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,2CAA2C;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,YAAY;QAAE2B,UAAU,EAAE;MAAW,CAAC,EAChJ;QAAEL,KAAK,EAAE,qDAAqD;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,UAAU;QAAE2B,UAAU,EAAE;MAAO,CAAC,EAChJ;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,WAAW;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,WAAW;QAAE2B,UAAU,EAAE;MAAO,CAAC,EAChI;QAAEL,KAAK,EAAE,kCAAkC;QAAE0B,GAAG,EAAE,oBAAoB;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,UAAU;QAAE2B,UAAU,EAAE;MAAO,CAAC,EAClI;QAAEL,KAAK,EAAE,wCAAwC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,YAAY;QAAE2B,UAAU,EAAE;MAAS,CAAC,EACtI;QAAEL,KAAK,EAAE,uCAAuC;QAAE0B,GAAG,EAAE,aAAa;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,QAAQ;QAAE2B,UAAU,EAAE;MAAO,CAAC;IAElI,CAAC;EACH,CAAC;EAED,MAAMb,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACLK,UAAU,EAAE,2DAA2D;MACvEC,gBAAgB,EAAE,qRAAqR;MACvSC,WAAW,EAAE,CACX;QACErB,IAAI,EAAE,WAAW;QACjBsB,KAAK,EAAE,yBAAyB;QAChCC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBC,WAAW,EAAE,8FAA8F;QAC3GC,OAAO,EAAE,6IAA6I;QACtJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,aAAa;QACnBsB,KAAK,EAAE,mCAAmC;QAC1CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,uGAAuG;QACpHC,OAAO,EAAE,0JAA0J;QACnKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,0CAA0C;QACjDC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,gFAAgF;QAC7FC,OAAO,EAAE,kJAAkJ;QAC3JC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,8CAA8C;QACrDC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,0GAA0G;QACvHC,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,QAAQ;QACdsB,KAAK,EAAE,wCAAwC;QAC/CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBC,WAAW,EAAE,8FAA8F;QAC3GC,OAAO,EAAE,8IAA8I;QACvJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,YAAY,EAAE;QACZC,aAAa,EAAE,2BAA2B;QAC1CC,kBAAkB,EAAE,4BAA4B;QAChDC,qBAAqB,EAAE,kCAAkC;QACzDC,iBAAiB,EAAE;MACrB,CAAC;MACDK,wBAAwB,EAAE,CACxB;QACEC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,kDAAkD;QAC1DZ,QAAQ,EAAE,oBAAoB;QAC9Ba,UAAU,EAAE,MAAM;QAClBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,yCAAyC;QACjDZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,0CAA0C;QAClDZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDsB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,6BAA6B;QAAE0B,GAAG,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,YAAY;QAAE2B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,+CAA+C;QAAE0B,GAAG,EAAE,+BAA+B;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,UAAU;QAAE2B,UAAU,EAAE;MAAW,CAAC,EAC9J;QAAEL,KAAK,EAAE,8CAA8C;QAAE0B,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,UAAU;QAAE2B,UAAU,EAAE;MAAO,CAAC;IAEvI,CAAC;EACH,CAAC;EAED,MAAMT,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,OAAO;MACLC,UAAU,EAAE,uCAAuC;MACnDC,gBAAgB,EAAE,oWAAoW;MACtXC,WAAW,EAAE,CACX;QACErB,IAAI,EAAE,eAAe;QACrBsB,KAAK,EAAE,sCAAsC;QAC7CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,iHAAiH;QAC9HC,OAAO,EAAE,uKAAuK;QAChLC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,QAAQ;QACdsB,KAAK,EAAE,0CAA0C;QACjDC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,uHAAuH;QACpIC,OAAO,EAAE,sKAAsK;QAC/KC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,4CAA4C;QACnDC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBC,WAAW,EAAE,4GAA4G;QACzHC,OAAO,EAAE,6JAA6J;QACtKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,WAAW;QACjBsB,KAAK,EAAE,iDAAiD;QACxDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBC,WAAW,EAAE,iHAAiH;QAC9HC,OAAO,EAAE,+JAA+J;QACxKC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,EACD;QACE5B,IAAI,EAAE,YAAY;QAClBsB,KAAK,EAAE,iCAAiC;QACxCC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBC,WAAW,EAAE,yHAAyH;QACtIC,OAAO,EAAE,gJAAgJ;QACzJC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDU,wBAAwB,EAAE,CACxB;QACEC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,6CAA6C;QACrDZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,KAAK;QACjBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,8CAA8C;QACtDZ,QAAQ,EAAE,oBAAoB;QAC9Ba,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,EACD;QACEc,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,2CAA2C;QACnDZ,QAAQ,EAAE,qBAAqB;QAC/Ba,UAAU,EAAE,OAAO;QACnBhB,WAAW,EAAE;MACf,CAAC,CACF;MACDsB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,qCAAqC;QAAE0B,GAAG,EAAE,eAAe;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,QAAQ;QAAE2B,UAAU,EAAE;MAAW,CAAC,EAClI;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,WAAW;QAAE2B,UAAU,EAAE;MAAO,CAAC,EAC3H;QAAEL,KAAK,EAAE,oCAAoC;QAAE0B,GAAG,EAAE,cAAc;QAAEC,IAAI,EAAE,YAAY;QAAEjD,IAAI,EAAE,YAAY;QAAE2B,UAAU,EAAE;MAAS,CAAC;IAEtI,CAAC;EACH,CAAC;EAED,MAAMuB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI9D,UAAU,CAAC+D,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMrD,WAAW,GAAG;QAClBC,EAAE,EAAEb,QAAQ,CAACkE,MAAM,GAAG,CAAC;QACvBpD,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEb,UAAU;QACnBc,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDhB,WAAW,CAACoB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAET,WAAW,CAAC,CAAC;MAC3CT,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAa,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBN,EAAE,EAAEb,QAAQ,CAACkE,MAAM,GAAG,CAAC;UACvBpD,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEK,oBAAoB,CAAClB,UAAU,CAAC;UACzCc,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDhB,WAAW,CAACoB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,UAAU,CAAC,CAAC;QAC1Cd,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAM8D,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,WAAW,GAAI5D,OAAO,IAAK;IAC/B6D,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,OAAO/D,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGgE,IAAI,CAACC,SAAS,CAACjE,OAAO,CAAC,CAAC;EAChG,CAAC;EAED,oBACEpB,OAAA,CAACb,MAAM,CAACmG,GAAG;IACTC,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAC7BG,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9B/F,OAAA;MAAKuF,SAAS,EAAC,qBAAqB;MAAAQ,QAAA,gBAClC/F,OAAA,CAACb,MAAM,CAAC6G,MAAM;QACZT,SAAS,EAAC,UAAU;QACpBU,OAAO,EAAE9F,MAAO;QAChB+F,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAJ,QAAA,gBAE1B/F,OAAA,CAACX,SAAS;UAACgH,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBzG,OAAA;QAAKuF,SAAS,EAAC,sBAAsB;QAAAQ,QAAA,gBACnC/F,OAAA,CAACb,MAAM,CAAC6G,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAEpB,WAAY;UACrBqB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B/F,OAAA,CAACT,QAAQ;YAAC8G,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChBzG,OAAA,CAACb,MAAM,CAAC6G,MAAM;UACZT,SAAS,EAAC,YAAY;UACtBW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,gBAE1B/F,OAAA,CAACF,MAAM;YAACuG,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA;MAAKuF,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,gBACjC/F,OAAA,CAACZ,eAAe;QAAA2G,QAAA,EACb1F,QAAQ,CAACqG,GAAG,CAAEC,OAAO,iBACpB3G,OAAA,CAACb,MAAM,CAACmG,GAAG;UAETC,SAAS,EAAE,WAAWoB,OAAO,CAACxF,IAAI,EAAG;UACrCqE,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAG,CAAE;UAC/BjB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE;UAAE,CAAE;UAC9BhB,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEmB,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7Bf,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAC,QAAA,EAE7BY,OAAO,CAACxF,IAAI,KAAK,MAAM,gBACtBnB,OAAA;YAAKuF,SAAS,EAAC,cAAc;YAAAQ,QAAA,gBAC3B/F,OAAA;cAAKuF,SAAS,EAAC,iBAAiB;cAAAQ,QAAA,EAAEY,OAAO,CAACvF;YAAO;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDzG,OAAA;cAAKuF,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BY,OAAO,CAACtF,SAAS,CAACwF,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENzG,OAAA;YAAKuF,SAAS,EAAC,YAAY;YAAAQ,QAAA,gBACzB/F,OAAA;cAAKuF,SAAS,EAAC,aAAa;cAAAQ,QAAA,EACzB,OAAOY,OAAO,CAACvF,OAAO,KAAK,QAAQ,gBAClCpB,OAAA;gBAAKuF,SAAS,EAAC,qBAAqB;gBAAAQ,QAAA,gBAClC/F,OAAA;kBAAKuF,SAAS,EAAC,eAAe;kBAAAQ,QAAA,gBAC5B/F,OAAA;oBAAKuF,SAAS,EAAC,sBAAsB;oBAAAQ,QAAA,gBACnC/F,OAAA;sBAAA+F,QAAA,EAAKY,OAAO,CAACvF,OAAO,CAACkB,UAAU,IAAI;oBAAyB;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClEzG,OAAA;sBAAKuF,SAAS,EAAC,aAAa;sBAAAQ,QAAA,gBAC1B/F,OAAA;wBAAMuF,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,GAAC,aAAW,EAAC,IAAIzE,IAAI,CAAC,CAAC,CAAC0F,kBAAkB,CAAC,CAAC;sBAAA;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtFzG,OAAA;wBAAMuF,SAAS,EAAC,mBAAmB;wBAAAQ,QAAA,EAAC;sBAAwB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzG,OAAA;oBACEuF,SAAS,EAAC,UAAU;oBACpBU,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAAC2B,OAAO,CAACvF,OAAO,CAAE;oBAAA2E,QAAA,eAE5C/F,OAAA,CAACH,IAAI;sBAACwG,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAELE,OAAO,CAACvF,OAAO,CAACmB,gBAAgB,iBAC/BvC,OAAA;kBAAKuF,SAAS,EAAC,mBAAmB;kBAAAQ,QAAA,gBAChC/F,OAAA;oBAAA+F,QAAA,EAAI;kBAAiB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BzG,OAAA;oBAAA+F,QAAA,EAAIY,OAAO,CAACvF,OAAO,CAACmB;kBAAgB;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACN,EAEAE,OAAO,CAACvF,OAAO,CAAC4B,YAAY,iBAC3BhD,OAAA;kBAAKuF,SAAS,EAAC,uBAAuB;kBAAAQ,QAAA,gBACpC/F,OAAA;oBAAA+F,QAAA,EAAI;kBAAsB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/BzG,OAAA;oBAAKuF,SAAS,EAAC,gBAAgB;oBAAAQ,QAAA,gBAC7B/F,OAAA;sBAAKuF,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B/F,OAAA;wBAAMuF,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAe;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrDzG,OAAA;wBAAMuF,SAAS,EAAC,mBAAmB;wBAAAQ,QAAA,EAAEY,OAAO,CAACvF,OAAO,CAAC4B,YAAY,CAACC;sBAAa;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACNzG,OAAA;sBAAKuF,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B/F,OAAA;wBAAMuF,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAmB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzDzG,OAAA;wBAAMuF,SAAS,EAAC,yBAAyB;wBAAAQ,QAAA,EAAEY,OAAO,CAACvF,OAAO,CAAC4B,YAAY,CAACE;sBAAkB;wBAAAoD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC,eACNzG,OAAA;sBAAKuF,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B/F,OAAA;wBAAMuF,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAQ;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9CzG,OAAA;wBAAMuF,SAAS,EAAC,uBAAuB;wBAAAQ,QAAA,EAAEY,OAAO,CAACvF,OAAO,CAAC4B,YAAY,CAACG;sBAAqB;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACNzG,OAAA;sBAAKuF,SAAS,EAAC,eAAe;sBAAAQ,QAAA,gBAC5B/F,OAAA;wBAAMuF,SAAS,EAAC,cAAc;wBAAAQ,QAAA,EAAC;sBAAkB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxDzG,OAAA;wBAAMuF,SAAS,EAAC,0BAA0B;wBAAAQ,QAAA,EAAEY,OAAO,CAACvF,OAAO,CAAC4B,YAAY,CAACI;sBAAiB;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDzG,OAAA;kBAAKuF,SAAS,EAAC,cAAc;kBAAAQ,QAAA,gBAC3B/F,OAAA;oBAAA+F,QAAA,EAAI;kBAAY;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACpBE,OAAO,CAACvF,OAAO,CAACoB,WAAW,CAACkE,GAAG,CAAC,CAACO,OAAO,EAAEC,KAAK,kBAC9ClH,OAAA,CAACb,MAAM,CAACmG,GAAG;oBAETC,SAAS,EAAC,cAAc;oBACxBC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BG,UAAU,EAAE;sBAAEsB,KAAK,EAAED,KAAK,GAAG;oBAAI,CAAE;oBAAAnB,QAAA,gBAEnC/F,OAAA;sBAAKuF,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,gBAC7B/F,OAAA;wBAAKuF,SAAS,EAAC,cAAc;wBAAAQ,QAAA,GAC1BkB,OAAO,CAAC9F,IAAI,KAAK,YAAY,iBAAInB,OAAA,CAACN,aAAa;0BAAC2G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC5DQ,OAAO,CAAC9F,IAAI,KAAK,WAAW,iBAAInB,OAAA,CAACP,UAAU;0BAAC4G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACxDQ,OAAO,CAAC9F,IAAI,KAAK,aAAa,iBAAInB,OAAA,CAACL,IAAI;0BAAC0G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpDQ,OAAO,CAAC9F,IAAI,KAAK,QAAQ,iBAAInB,OAAA,CAACP,UAAU;0BAAC4G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrDQ,OAAO,CAAC9F,IAAI,KAAK,YAAY,iBAAInB,OAAA,CAACL,IAAI;0BAAC0G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACnDQ,OAAO,CAAC9F,IAAI,KAAK,eAAe,iBAAInB,OAAA,CAACN,aAAa;0BAAC2G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC/DQ,OAAO,CAAC9F,IAAI,KAAK,QAAQ,iBAAInB,OAAA,CAACL,IAAI;0BAAC0G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC/CQ,OAAO,CAAC9F,IAAI,KAAK,YAAY,iBAAInB,OAAA,CAACL,IAAI;0BAAC0G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC,eACNzG,OAAA;wBAAKuF,SAAS,EAAC,eAAe;wBAAAQ,QAAA,EAAEkB,OAAO,CAACxE;sBAAK;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpDzG,OAAA;wBAAKuF,SAAS,EAAC,gBAAgB;wBAAAQ,QAAA,gBAC7B/F,OAAA;0BAAKuF,SAAS,EAAE,gBAAgB0B,OAAO,CAACvE,MAAM,CAACZ,WAAW,CAAC,CAAC,EAAG;0BAAAiE,QAAA,GAC5DkB,OAAO,CAACvE,MAAM,EAAC,SAClB;wBAAA;0BAAA4D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EACLQ,OAAO,CAACtE,OAAO,iBACd3C,OAAA;0BAAKuF,SAAS,EAAE,iBAAiB0B,OAAO,CAACtE,OAAO,CAACb,WAAW,CAAC,CAAC,EAAG;0BAAAiE,QAAA,EAC9DkB,OAAO,CAACtE;wBAAO;0BAAA2D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNzG,OAAA;sBAAGuF,SAAS,EAAC,qBAAqB;sBAAAQ,QAAA,EAAEkB,OAAO,CAACrE;oBAAW;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC3DQ,OAAO,CAACpE,OAAO,iBACd7C,OAAA;sBAAKuF,SAAS,EAAC,iBAAiB;sBAAAQ,QAAA,eAC9B/F,OAAA;wBAAA+F,QAAA,EAAIkB,OAAO,CAACpE;sBAAO;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CACN,EACAQ,OAAO,CAAClE,QAAQ,iBACf/C,OAAA;sBAAKuF,SAAS,EAAC,kBAAkB;sBAAAQ,QAAA,gBAC/B/F,OAAA;wBAAA+F,QAAA,EAAQ;sBAAS;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACQ,OAAO,CAAClE,QAAQ;oBAAA;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CACN,eACDzG,OAAA;sBAAKuF,SAAS,EAAC,gBAAgB;sBAAAQ,QAAA,gBAC7B/F,OAAA;wBAAKuF,SAAS,EAAC,kBAAkB;wBAAAQ,QAAA,GAAC,cAAY,EAACkB,OAAO,CAACnE,UAAU,EAAC,GAAC;sBAAA;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzEzG,OAAA;wBAAKuF,SAAS,EAAC,qBAAqB;wBAAAQ,QAAA,eAClC/F,OAAA,CAACb,MAAM,CAACmG,GAAG;0BACTC,SAAS,EAAC,iBAAiB;0BAC3BC,OAAO,EAAE;4BAAE4B,KAAK,EAAE;0BAAE,CAAE;0BACtBzB,OAAO,EAAE;4BAAEyB,KAAK,EAAE,GAAGH,OAAO,CAACnE,UAAU;0BAAI,CAAE;0BAC7C+C,UAAU,EAAE;4BAAEC,QAAQ,EAAE,CAAC;4BAAEqB,KAAK,EAAE;0BAAI;wBAAE;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAlDDS,KAAK;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmDA,CACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENzG,OAAA;kBAAKuF,SAAS,EAAC,iBAAiB;kBAAAQ,QAAA,gBAC9B/F,OAAA;oBAAA+F,QAAA,EAAI;kBAAO;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBzG,OAAA;oBAAKuF,SAAS,EAAC,cAAc;oBAAAQ,QAAA,EAC1BY,OAAO,CAACvF,OAAO,CAAC8C,OAAO,CAACwC,GAAG,CAAC,CAACW,MAAM,EAAEH,KAAK,kBACzClH,OAAA,CAACb,MAAM,CAACmG,GAAG;sBAETC,SAAS,EAAC,aAAa;sBACvBC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEmB,CAAC,EAAE;sBAAG,CAAE;sBAC/BjB,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEmB,CAAC,EAAE;sBAAE,CAAE;sBAC9Bf,UAAU,EAAE;wBAAEsB,KAAK,EAAE,GAAG,GAAGD,KAAK,GAAG;sBAAI,CAAE;sBAAAnB,QAAA,gBAEzC/F,OAAA;wBAAKuF,SAAS,EAAC,eAAe;wBAAAQ,QAAA,gBAC5B/F,OAAA,CAACR,YAAY;0BAAC6G,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1BzG,OAAA;0BAAMuF,SAAS,EAAC,cAAc;0BAAAQ,QAAA,EAAEsB,MAAM,CAAC5E;wBAAK;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC,eACNzG,OAAA;wBAAKuF,SAAS,EAAC,aAAa;wBAAAQ,QAAA,gBAC1B/F,OAAA;0BAAMuF,SAAS,EAAC,YAAY;0BAAAQ,QAAA,EAAEsB,MAAM,CAAClD;wBAAG;0BAAAmC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAChDzG,OAAA;0BAAMuF,SAAS,EAAC,aAAa;0BAAAQ,QAAA,EAAEsB,MAAM,CAACjD;wBAAI;0BAAAkC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC;oBAAA,GAbDS,KAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcA,CACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzG,OAAA;kBAAKuF,SAAS,EAAC,cAAc;kBAAAQ,QAAA,gBAC3B/F,OAAA;oBAAA+F,QAAA,EAAI;kBAAmB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BzG,OAAA;oBAAA+F,QAAA,EACGY,OAAO,CAACvF,OAAO,CAACkG,WAAW,CAACZ,GAAG,CAAC,CAACa,IAAI,EAAEL,KAAK,kBAC3ClH,OAAA,CAACb,MAAM,CAACqI,EAAE;sBAERhC,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE,CAAC;sBAAG,CAAE;sBAChCC,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAE,CAAE;sBAC9BG,UAAU,EAAE;wBAAEsB,KAAK,EAAE,GAAG,GAAGD,KAAK,GAAG;sBAAI,CAAE;sBAAAnB,QAAA,EAExCwB;oBAAI,GALAL,KAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMD,CACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENzG,OAAA;gBAAKuF,SAAS,EAAC,iBAAiB;gBAAAQ,QAAA,EAAEY,OAAO,CAACvF;cAAO;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACxD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzG,OAAA;cAAKuF,SAAS,EAAC,cAAc;cAAAQ,QAAA,EAC1BY,OAAO,CAACtF,SAAS,CAACwF,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN,GA7KIE,OAAO,CAACzF,EAAE;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8KL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEjBhG,SAAS,iBACRT,OAAA,CAACb,MAAM,CAACmG,GAAG;QACTC,SAAS,EAAC,iBAAiB;QAC3BC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAG,CAAE;QAC/BjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAAAb,QAAA,eAE9B/F,OAAA;UAAKuF,SAAS,EAAC,iBAAiB;UAAAQ,QAAA,gBAC9B/F,OAAA,CAACJ,OAAO;YAAC2F,SAAS,EAAC,iBAAiB;YAACc,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDzG,OAAA;YAAA+F,QAAA,EAAM;UAAqC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDzG,OAAA;QAAKyH,GAAG,EAAE9G;MAAe;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNzG,OAAA;MAAKuF,SAAS,EAAC,yBAAyB;MAAAQ,QAAA,eACtC/F,OAAA;QAAKuF,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChC/F,OAAA;UACE0H,KAAK,EAAEnH,UAAW;UAClBoH,QAAQ,EAAGlD,CAAC,IAAKjE,aAAa,CAACiE,CAAC,CAACmD,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAErD,cAAe;UAC3BsD,WAAW,EAAC,oDAAoD;UAChEvC,SAAS,EAAC,eAAe;UACzBwC,IAAI,EAAC;QAAG;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFzG,OAAA,CAACb,MAAM,CAAC6G,MAAM;UACZT,SAAS,EAAC,UAAU;UACpBU,OAAO,EAAE5B,iBAAkB;UAC3B2D,QAAQ,EAAE,CAACzH,UAAU,CAAC+D,IAAI,CAAC,CAAC,IAAI7D,SAAU;UAC1CyF,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAJ,QAAA,eAE1B/F,OAAA,CAACV,IAAI;YAAC+G,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACrG,EAAA,CAlrBIH,gBAAgB;AAAAgI,EAAA,GAAhBhI,gBAAgB;AAorBtB,eAAeA,gBAAgB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}