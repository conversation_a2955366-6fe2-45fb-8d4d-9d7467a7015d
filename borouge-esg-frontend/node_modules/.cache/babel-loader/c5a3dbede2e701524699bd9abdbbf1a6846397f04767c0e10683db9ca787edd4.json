{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1\",\n  key: \"10bnsj\"\n}], [\"path\", {\n  d: \"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9\",\n  key: \"1eqmu1\"\n}], [\"path\", {\n  d: \"M21 21v-2h-4\",\n  key: \"14zm7j\"\n}], [\"path\", {\n  d: \"M3 5h4V3\",\n  key: \"z442eg\"\n}], [\"path\", {\n  d: \"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3\",\n  key: \"ebdjd7\"\n}]];\nconst Cable = createLucideIcon(\"cable\", __iconNode);\nexport { __iconNode, Cable as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Cable", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/cable.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1',\n      key: '10bnsj',\n    },\n  ],\n  ['path', { d: 'M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9', key: '1eqmu1' }],\n  ['path', { d: 'M21 21v-2h-4', key: '14zm7j' }],\n  ['path', { d: 'M3 5h4V3', key: 'z442eg' }],\n  [\n    'path',\n    { d: 'M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3', key: 'ebdjd7' },\n  ],\n];\n\n/**\n * @component @name Cable\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgMjF2LTJhMSAxIDAgMCAxLTEtMXYtMWEyIDIgMCAwIDEgMi0yaDJhMiAyIDAgMCAxIDIgMnYxYTEgMSAwIDAgMS0xIDEiIC8+CiAgPHBhdGggZD0iTTE5IDE1VjYuNWExIDEgMCAwIDAtNyAwdjExYTEgMSAwIDAgMS03IDBWOSIgLz4KICA8cGF0aCBkPSJNMjEgMjF2LTJoLTQiIC8+CiAgPHBhdGggZD0iTTMgNWg0VjMiIC8+CiAgPHBhdGggZD0iTTcgNWExIDEgMCAwIDEgMSAxdjFhMiAyIDAgMCAxLTIgMkg0YTIgMiAwIDAgMS0yLTJWNmExIDEgMCAwIDEgMS0xVjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/cable\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cable = createLucideIcon('cable', __iconNode);\n\nexport default Cable;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EAAED,CAAA,EAAG,sEAAwE;EAAAC,GAAA,EAAK;AAAS,EAC7F,CACF;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}