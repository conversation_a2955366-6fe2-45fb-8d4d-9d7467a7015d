{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20V10\",\n  key: \"g8npz5\"\n}], [\"path\", {\n  d: \"M18 20v-4\",\n  key: \"8uic4z\"\n}], [\"path\", {\n  d: \"M6 20V4\",\n  key: \"1w1bmo\"\n}]];\nconst ChartNoAxesColumnDecreasing = createLucideIcon(\"chart-no-axes-column-decreasing\", __iconNode);\nexport { __iconNode, ChartNoAxesColumnDecreasing as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ChartNoAxesColumnDecreasing", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/chart-no-axes-column-decreasing.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20V10', key: 'g8npz5' }],\n  ['path', { d: 'M18 20v-4', key: '8uic4z' }],\n  ['path', { d: 'M6 20V4', key: '1w1bmo' }],\n];\n\n/**\n * @component @name ChartNoAxesColumnDecreasing\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBWMTAiIC8+CiAgPHBhdGggZD0iTTE4IDIwdi00IiAvPgogIDxwYXRoIGQ9Ik02IDIwVjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-no-axes-column-decreasing\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartNoAxesColumnDecreasing = createLucideIcon('chart-no-axes-column-decreasing', __iconNode);\n\nexport default ChartNoAxesColumnDecreasing;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C;AAaM,MAAAC,2BAAA,GAA8BC,gBAAiB,oCAAmCJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}