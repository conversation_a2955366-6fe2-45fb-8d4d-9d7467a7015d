{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, ChevronDown, MessageSquare, Users, Bookmark, Menu, X } from 'lucide-react';\nimport ConversationView from './components/ConversationView';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [conversations, setConversations] = useState([]);\n  const [currentView, setCurrentView] = useState('search'); // 'search' or 'conversation'\n  const [activeQuery, setActiveQuery] = useState('');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // ESG-focused suggestion chips for Borouge\n  const suggestionChips = ['EU plastic regulations 2024', 'CBAM carbon border adjustment', 'Circular economy packaging', 'SABIC sustainability strategy', 'Petrochemical market trends', 'ESG reporting requirements', 'Renewable feedstock adoption', 'Carbon footprint reduction'];\n  const handleSearch = query => {\n    if (query.trim()) {\n      setActiveQuery(query);\n      setCurrentView('conversation');\n      setSearchQuery('');\n      setSidebarOpen(false);\n\n      // Add to conversations\n      const newConversation = {\n        id: Date.now(),\n        query: query,\n        timestamp: new Date()\n      };\n      setConversations([newConversation, ...conversations]);\n    }\n  };\n  const handleBackToSearch = () => {\n    setCurrentView('search');\n    setActiveQuery('');\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSearch(searchQuery);\n    }\n  };\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n  const selectConversation = conversation => {\n    setActiveQuery(conversation.query);\n    setCurrentView('conversation');\n    setSidebarOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(motion.button, {\n      className: \"mobile-menu-btn\",\n      onClick: toggleSidebar,\n      whileHover: {\n        scale: 1.05\n      },\n      whileTap: {\n        scale: 0.95\n      },\n      style: {\n        position: 'fixed',\n        top: '20px',\n        left: '20px',\n        zIndex: 1001,\n        display: 'none',\n        width: '44px',\n        height: '44px',\n        background: 'var(--background-white)',\n        border: '1px solid var(--border-light)',\n        borderRadius: 'var(--radius-lg)',\n        alignItems: 'center',\n        justifyContent: 'center',\n        boxShadow: 'var(--shadow-md)',\n        cursor: 'pointer'\n      },\n      children: sidebarOpen ? /*#__PURE__*/_jsxDEV(X, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 24\n      }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 42\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: `sidebar ${sidebarOpen ? 'open' : ''}`,\n      initial: {\n        x: -320\n      },\n      animate: {\n        x: 0\n      },\n      transition: {\n        duration: 0.3,\n        ease: \"easeOut\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"logo\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"logo-icon\",\n          whileHover: {\n            rotate: 360\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: \"B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Borouge ESG\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"new-search-btn\",\n        onClick: handleBackToSearch,\n        whileHover: {\n          scale: 1.02\n        },\n        whileTap: {\n          scale: 0.98\n        },\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), \"Start new search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"nav-section\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"nav-item\",\n          whileHover: {\n            x: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Users, {\n              className: \"nav-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), \"All Intelligence\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n            className: \"nav-count\",\n            initial: {\n              scale: 1.2\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              duration: 0.2\n            },\n            children: conversations.length\n          }, conversations.length, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"nav-item\",\n          whileHover: {\n            x: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Bookmark, {\n              className: \"nav-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), \"Saved\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-count\",\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"recent-chats\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Recent Chats\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"no-chats\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            children: \"No saved chats yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: conversations.slice(0, 5).map((conversation, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -20\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              onClick: () => selectConversation(conversation),\n              style: {\n                padding: '12px 16px',\n                fontSize: '15px',\n                color: 'var(--text-secondary)',\n                cursor: 'pointer',\n                overflow: 'hidden',\n                textOverflow: 'ellipsis',\n                whiteSpace: 'nowrap',\n                borderRadius: 'var(--radius-md)',\n                marginBottom: '4px',\n                transition: 'all 0.2s ease'\n              },\n              whileHover: {\n                backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                x: 4,\n                color: 'var(--text-primary)'\n              },\n              children: conversation.query\n            }, conversation.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"The ESG Intelligence Engine for\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subtitle\",\n          children: [\"Petrochemicals\", /*#__PURE__*/_jsxDEV(ChevronDown, {\n            className: \"dropdown-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-box\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"search-input\",\n            placeholder: \"EU plastic regulations impact on Borouge operations\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            onKeyPress: handleKeyPress\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"research-badge\",\n              children: \"INTELLIGENCE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"search-btn\",\n              onClick: () => handleSearch(searchQuery),\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"search-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"suggestions\",\n        children: suggestionChips.map((chip, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"suggestion-chip\",\n          onClick: () => handleSearch(chip),\n          children: chip\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"/SpknX6vu08HVWq1maOnMSaYUI0=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Search", "ChevronDown", "MessageSquare", "Users", "Bookmark", "<PERSON><PERSON>", "X", "ConversationView", "jsxDEV", "_jsxDEV", "App", "_s", "searchQuery", "setSearch<PERSON>uery", "conversations", "setConversations", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "activeQuery", "setActiveQuery", "sidebarOpen", "setSidebarOpen", "suggestionChips", "handleSearch", "query", "trim", "newConversation", "id", "Date", "now", "timestamp", "handleBackToSearch", "handleKeyPress", "e", "key", "toggleSidebar", "selectConversation", "conversation", "className", "children", "button", "onClick", "whileHover", "scale", "whileTap", "style", "position", "top", "left", "zIndex", "display", "width", "height", "background", "border", "borderRadius", "alignItems", "justifyContent", "boxShadow", "cursor", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "x", "animate", "transition", "duration", "ease", "opacity", "y", "delay", "rotate", "span", "length", "exit", "slice", "map", "index", "padding", "fontSize", "color", "overflow", "textOverflow", "whiteSpace", "marginBottom", "backgroundColor", "type", "placeholder", "value", "onChange", "target", "onKeyPress", "chip", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Search, ChevronDown, MessageSquare, Users, Bookmark, Menu, X } from 'lucide-react';\nimport ConversationView from './components/ConversationView';\nimport './App.css';\n\nfunction App() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [conversations, setConversations] = useState([]);\n  const [currentView, setCurrentView] = useState('search'); // 'search' or 'conversation'\n  const [activeQuery, setActiveQuery] = useState('');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // ESG-focused suggestion chips for Borouge\n  const suggestionChips = [\n    'EU plastic regulations 2024',\n    'CBAM carbon border adjustment',\n    'Circular economy packaging',\n    'SABIC sustainability strategy',\n    'Petrochemical market trends',\n    'ESG reporting requirements',\n    'Renewable feedstock adoption',\n    'Carbon footprint reduction'\n  ];\n\n  const handleSearch = (query) => {\n    if (query.trim()) {\n      setActiveQuery(query);\n      setCurrentView('conversation');\n      setSearchQuery('');\n      setSidebarOpen(false);\n\n      // Add to conversations\n      const newConversation = {\n        id: Date.now(),\n        query: query,\n        timestamp: new Date()\n      };\n      setConversations([newConversation, ...conversations]);\n    }\n  };\n\n  const handleBackToSearch = () => {\n    setCurrentView('search');\n    setActiveQuery('');\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      handleSearch(searchQuery);\n    }\n  };\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  const selectConversation = (conversation) => {\n    setActiveQuery(conversation.query);\n    setCurrentView('conversation');\n    setSidebarOpen(false);\n  };\n\n  return (\n    <div className=\"app\">\n      {/* Mobile Menu Button */}\n      <motion.button\n        className=\"mobile-menu-btn\"\n        onClick={toggleSidebar}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        style={{\n          position: 'fixed',\n          top: '20px',\n          left: '20px',\n          zIndex: 1001,\n          display: 'none',\n          width: '44px',\n          height: '44px',\n          background: 'var(--background-white)',\n          border: '1px solid var(--border-light)',\n          borderRadius: 'var(--radius-lg)',\n          alignItems: 'center',\n          justifyContent: 'center',\n          boxShadow: 'var(--shadow-md)',\n          cursor: 'pointer'\n        }}\n      >\n        {sidebarOpen ? <X size={20} /> : <Menu size={20} />}\n      </motion.button>\n\n      {/* Sidebar */}\n      <motion.div\n        className={`sidebar ${sidebarOpen ? 'open' : ''}`}\n        initial={{ x: -320 }}\n        animate={{ x: 0 }}\n        transition={{ duration: 0.3, ease: \"easeOut\" }}\n      >\n        {/* Logo */}\n        <motion.div\n          className=\"logo\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <motion.div\n            className=\"logo-icon\"\n            whileHover={{ rotate: 360 }}\n            transition={{ duration: 0.5 }}\n          >\n            B\n          </motion.div>\n          <span className=\"logo-text\">Borouge ESG</span>\n        </motion.div>\n\n        {/* New Search Button */}\n        <motion.button\n          className=\"new-search-btn\"\n          onClick={handleBackToSearch}\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <MessageSquare size={18} />\n          Start new search\n        </motion.button>\n\n        {/* Navigation */}\n        <motion.div\n          className=\"nav-section\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n        >\n          <motion.div\n            className=\"nav-item\"\n            whileHover={{ x: 4 }}\n          >\n            <div style={{ display: 'flex', alignItems: 'center' }}>\n              <Users className=\"nav-icon\" />\n              All Intelligence\n            </div>\n            <motion.span\n              className=\"nav-count\"\n              key={conversations.length}\n              initial={{ scale: 1.2 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.2 }}\n            >\n              {conversations.length}\n            </motion.span>\n          </motion.div>\n          <motion.div\n            className=\"nav-item\"\n            whileHover={{ x: 4 }}\n          >\n            <div style={{ display: 'flex', alignItems: 'center' }}>\n              <Bookmark className=\"nav-icon\" />\n              Saved\n            </div>\n            <span className=\"nav-count\">0</span>\n          </motion.div>\n        </motion.div>\n\n        {/* Recent Chats */}\n        <motion.div\n          className=\"recent-chats\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <h3>Recent Chats</h3>\n          <AnimatePresence>\n            {conversations.length === 0 ? (\n              <motion.div\n                className=\"no-chats\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n              >\n                No saved chats yet.\n              </motion.div>\n            ) : (\n              <div>\n                {conversations.slice(0, 5).map((conversation, index) => (\n                  <motion.div\n                    key={conversation.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -20 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={() => selectConversation(conversation)}\n                    style={{\n                      padding: '12px 16px',\n                      fontSize: '15px',\n                      color: 'var(--text-secondary)',\n                      cursor: 'pointer',\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis',\n                      whiteSpace: 'nowrap',\n                      borderRadius: 'var(--radius-md)',\n                      marginBottom: '4px',\n                      transition: 'all 0.2s ease'\n                    }}\n                    whileHover={{\n                      backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                      x: 4,\n                      color: 'var(--text-primary)'\n                    }}\n                  >\n                    {conversation.query}\n                  </motion.div>\n                ))}\n              </div>\n            )}\n          </AnimatePresence>\n        </motion.div>\n      </motion.div>\n\n      {/* Main Content */}\n      <div className=\"main-content\">\n        {/* Header */}\n        <div className=\"header\">\n          <h1 className=\"title\">The ESG Intelligence Engine for</h1>\n          <div className=\"subtitle\">\n            Petrochemicals\n            <ChevronDown className=\"dropdown-icon\" />\n          </div>\n        </div>\n\n        {/* Search Container */}\n        <div className=\"search-container\">\n          <div className=\"search-box\">\n            <input\n              type=\"text\"\n              className=\"search-input\"\n              placeholder=\"EU plastic regulations impact on Borouge operations\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              onKeyPress={handleKeyPress}\n            />\n            <div className=\"search-controls\">\n              <span className=\"research-badge\">INTELLIGENCE</span>\n              <button\n                className=\"search-btn\"\n                onClick={() => handleSearch(searchQuery)}\n              >\n                <Search className=\"search-icon\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Suggestion Chips */}\n        <div className=\"suggestions\">\n          {suggestionChips.map((chip, index) => (\n            <button\n              key={index}\n              className=\"suggestion-chip\"\n              onClick={() => handleSearch(chip)}\n            >\n              {chip}\n            </button>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAC3F,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMyB,eAAe,GAAG,CACtB,6BAA6B,EAC7B,+BAA+B,EAC/B,4BAA4B,EAC5B,+BAA+B,EAC/B,6BAA6B,EAC7B,4BAA4B,EAC5B,8BAA8B,EAC9B,4BAA4B,CAC7B;EAED,MAAMC,YAAY,GAAIC,KAAK,IAAK;IAC9B,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MAChBN,cAAc,CAACK,KAAK,CAAC;MACrBP,cAAc,CAAC,cAAc,CAAC;MAC9BJ,cAAc,CAAC,EAAE,CAAC;MAClBQ,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,MAAMK,eAAe,GAAG;QACtBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdL,KAAK,EAAEA,KAAK;QACZM,SAAS,EAAE,IAAIF,IAAI,CAAC;MACtB,CAAC;MACDb,gBAAgB,CAAC,CAACW,eAAe,EAAE,GAAGZ,aAAa,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bd,cAAc,CAAC,QAAQ,CAAC;IACxBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMa,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBX,YAAY,CAACX,WAAW,CAAC;IAC3B;EACF,CAAC;EAED,MAAMuB,aAAa,GAAGA,CAAA,KAAM;IAC1Bd,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,MAAMgB,kBAAkB,GAAIC,YAAY,IAAK;IAC3ClB,cAAc,CAACkB,YAAY,CAACb,KAAK,CAAC;IAClCP,cAAc,CAAC,cAAc,CAAC;IAC9BI,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,oBACEZ,OAAA;IAAK6B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElB9B,OAAA,CAACX,MAAM,CAAC0C,MAAM;MACZF,SAAS,EAAC,iBAAiB;MAC3BG,OAAO,EAAEN,aAAc;MACvBO,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAE;MAC5BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAK,CAAE;MAC1BE,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,yBAAyB;QACrCC,MAAM,EAAE,+BAA+B;QACvCC,YAAY,EAAE,kBAAkB;QAChCC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,kBAAkB;QAC7BC,MAAM,EAAE;MACV,CAAE;MAAApB,QAAA,EAEDnB,WAAW,gBAAGX,OAAA,CAACH,CAAC;QAACsD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACJ,IAAI;QAACuD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGhBvD,OAAA,CAACX,MAAM,CAACmE,GAAG;MACT3B,SAAS,EAAE,WAAWlB,WAAW,GAAG,MAAM,GAAG,EAAE,EAAG;MAClD8C,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAU,CAAE;MAAAhC,QAAA,gBAG/C9B,OAAA,CAACX,MAAM,CAACmE,GAAG;QACT3B,SAAS,EAAC,MAAM;QAChB4B,OAAO,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCL,OAAO,EAAE;UAAEI,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAEK,KAAK,EAAE;QAAI,CAAE;QAAAnC,QAAA,gBAE3B9B,OAAA,CAACX,MAAM,CAACmE,GAAG;UACT3B,SAAS,EAAC,WAAW;UACrBI,UAAU,EAAE;YAAEiC,MAAM,EAAE;UAAI,CAAE;UAC5BN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvD,OAAA;UAAM6B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAGbvD,OAAA,CAACX,MAAM,CAAC0C,MAAM;QACZF,SAAS,EAAC,gBAAgB;QAC1BG,OAAO,EAAEV,kBAAmB;QAC5BW,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAC1BuB,OAAO,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BL,OAAO,EAAE;UAAEI,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAEK,KAAK,EAAE;QAAI,CAAE;QAAAnC,QAAA,gBAE3B9B,OAAA,CAACP,aAAa;UAAC0D,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAGhBvD,OAAA,CAACX,MAAM,CAACmE,GAAG;QACT3B,SAAS,EAAC,aAAa;QACvB4B,OAAO,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BL,OAAO,EAAE;UAAEI,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAEK,KAAK,EAAE;QAAI,CAAE;QAAAnC,QAAA,gBAE3B9B,OAAA,CAACX,MAAM,CAACmE,GAAG;UACT3B,SAAS,EAAC,UAAU;UACpBI,UAAU,EAAE;YAAEyB,CAAC,EAAE;UAAE,CAAE;UAAA5B,QAAA,gBAErB9B,OAAA;YAAKoC,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE;YAAS,CAAE;YAAAjB,QAAA,gBACpD9B,OAAA,CAACN,KAAK;cAACmC,SAAS,EAAC;YAAU;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvD,OAAA,CAACX,MAAM,CAAC8E,IAAI;YACVtC,SAAS,EAAC,WAAW;YAErB4B,OAAO,EAAE;cAAEvB,KAAK,EAAE;YAAI,CAAE;YACxByB,OAAO,EAAE;cAAEzB,KAAK,EAAE;YAAE,CAAE;YACtB0B,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAA/B,QAAA,EAE7BzB,aAAa,CAAC+D;UAAM,GALhB/D,aAAa,CAAC+D,MAAM;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbvD,OAAA,CAACX,MAAM,CAACmE,GAAG;UACT3B,SAAS,EAAC,UAAU;UACpBI,UAAU,EAAE;YAAEyB,CAAC,EAAE;UAAE,CAAE;UAAA5B,QAAA,gBAErB9B,OAAA;YAAKoC,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE;YAAS,CAAE;YAAAjB,QAAA,gBACpD9B,OAAA,CAACL,QAAQ;cAACkC,SAAS,EAAC;YAAU;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvD,OAAA;YAAM6B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGbvD,OAAA,CAACX,MAAM,CAACmE,GAAG;QACT3B,SAAS,EAAC,cAAc;QACxB4B,OAAO,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BL,OAAO,EAAE;UAAEI,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAEK,KAAK,EAAE;QAAI,CAAE;QAAAnC,QAAA,gBAE3B9B,OAAA;UAAA8B,QAAA,EAAI;QAAY;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBvD,OAAA,CAACV,eAAe;UAAAwC,QAAA,EACbzB,aAAa,CAAC+D,MAAM,KAAK,CAAC,gBACzBpE,OAAA,CAACX,MAAM,CAACmE,GAAG;YACT3B,SAAS,EAAC,UAAU;YACpB4B,OAAO,EAAE;cAAEM,OAAO,EAAE;YAAE,CAAE;YACxBJ,OAAO,EAAE;cAAEI,OAAO,EAAE;YAAE,CAAE;YACxBM,IAAI,EAAE;cAAEN,OAAO,EAAE;YAAE,CAAE;YAAAjC,QAAA,EACtB;UAED;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,gBAEbvD,OAAA;YAAA8B,QAAA,EACGzB,aAAa,CAACiE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC3C,YAAY,EAAE4C,KAAK,kBACjDxE,OAAA,CAACX,MAAM,CAACmE,GAAG;cAETC,OAAO,EAAE;gBAAEM,OAAO,EAAE,CAAC;gBAAEL,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCC,OAAO,EAAE;gBAAEI,OAAO,EAAE,CAAC;gBAAEL,CAAC,EAAE;cAAE,CAAE;cAC9BW,IAAI,EAAE;gBAAEN,OAAO,EAAE,CAAC;gBAAEL,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BE,UAAU,EAAE;gBAAEK,KAAK,EAAEO,KAAK,GAAG;cAAI,CAAE;cACnCxC,OAAO,EAAEA,CAAA,KAAML,kBAAkB,CAACC,YAAY,CAAE;cAChDQ,KAAK,EAAE;gBACLqC,OAAO,EAAE,WAAW;gBACpBC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,uBAAuB;gBAC9BzB,MAAM,EAAE,SAAS;gBACjB0B,QAAQ,EAAE,QAAQ;gBAClBC,YAAY,EAAE,UAAU;gBACxBC,UAAU,EAAE,QAAQ;gBACpBhC,YAAY,EAAE,kBAAkB;gBAChCiC,YAAY,EAAE,KAAK;gBACnBnB,UAAU,EAAE;cACd,CAAE;cACF3B,UAAU,EAAE;gBACV+C,eAAe,EAAE,0BAA0B;gBAC3CtB,CAAC,EAAE,CAAC;gBACJiB,KAAK,EAAE;cACT,CAAE;cAAA7C,QAAA,EAEDF,YAAY,CAACb;YAAK,GAxBda,YAAY,CAACV,EAAE;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGbvD,OAAA;MAAK6B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3B9B,OAAA;QAAK6B,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB9B,OAAA;UAAI6B,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAA+B;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DvD,OAAA;UAAK6B,SAAS,EAAC,UAAU;UAAAC,QAAA,GAAC,gBAExB,eAAA9B,OAAA,CAACR,WAAW;YAACqC,SAAS,EAAC;UAAe;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAK6B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B9B,OAAA;UAAK6B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9B,OAAA;YACEiF,IAAI,EAAC,MAAM;YACXpD,SAAS,EAAC,cAAc;YACxBqD,WAAW,EAAC,qDAAqD;YACjEC,KAAK,EAAEhF,WAAY;YACnBiF,QAAQ,EAAG5D,CAAC,IAAKpB,cAAc,CAACoB,CAAC,CAAC6D,MAAM,CAACF,KAAK,CAAE;YAChDG,UAAU,EAAE/D;UAAe;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFvD,OAAA;YAAK6B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B9B,OAAA;cAAM6B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAY;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDvD,OAAA;cACE6B,SAAS,EAAC,YAAY;cACtBG,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACX,WAAW,CAAE;cAAA2B,QAAA,eAEzC9B,OAAA,CAACT,MAAM;gBAACsC,SAAS,EAAC;cAAa;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBjB,eAAe,CAAC0D,GAAG,CAAC,CAACgB,IAAI,EAAEf,KAAK,kBAC/BxE,OAAA;UAEE6B,SAAS,EAAC,iBAAiB;UAC3BG,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACyE,IAAI,CAAE;UAAAzD,QAAA,EAEjCyD;QAAI,GAJAf,KAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrD,EAAA,CAxQQD,GAAG;AAAAuF,EAAA,GAAHvF,GAAG;AA0QZ,eAAeA,GAAG;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}