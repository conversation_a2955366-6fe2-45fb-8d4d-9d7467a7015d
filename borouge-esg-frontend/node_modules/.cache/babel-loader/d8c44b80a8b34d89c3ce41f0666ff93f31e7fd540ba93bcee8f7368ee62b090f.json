{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"path\", {\n  d: \"M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5\",\n  key: \"14wa3c\"\n}], [\"path\", {\n  d: \"M12 7.5V9\",\n  key: \"1oy5b0\"\n}], [\"path\", {\n  d: \"M7.5 12H9\",\n  key: \"eltsq1\"\n}], [\"path\", {\n  d: \"M16.5 12H15\",\n  key: \"vk5kw4\"\n}], [\"path\", {\n  d: \"M12 16.5V15\",\n  key: \"k7eayi\"\n}], [\"path\", {\n  d: \"m8 8 1.88 1.88\",\n  key: \"nxy4qf\"\n}], [\"path\", {\n  d: \"M14.12 9.88 16 8\",\n  key: \"1lst6k\"\n}], [\"path\", {\n  d: \"m8 16 1.88-1.88\",\n  key: \"h2eex1\"\n}], [\"path\", {\n  d: \"M14.12 14.12 16 16\",\n  key: \"uqkrx3\"\n}]];\nconst Flower = createLucideIcon(\"flower\", __iconNode);\nexport { __iconNode, Flower as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Flower", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/flower.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  [\n    'path',\n    {\n      d: 'M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5',\n      key: '14wa3c',\n    },\n  ],\n  ['path', { d: 'M12 7.5V9', key: '1oy5b0' }],\n  ['path', { d: 'M7.5 12H9', key: 'eltsq1' }],\n  ['path', { d: 'M16.5 12H15', key: 'vk5kw4' }],\n  ['path', { d: 'M12 16.5V15', key: 'k7eayi' }],\n  ['path', { d: 'm8 8 1.88 1.88', key: 'nxy4qf' }],\n  ['path', { d: 'M14.12 9.88 16 8', key: '1lst6k' }],\n  ['path', { d: 'm8 16 1.88-1.88', key: 'h2eex1' }],\n  ['path', { d: 'M14.12 14.12 16 16', key: 'uqkrx3' }],\n];\n\n/**\n * @component @name Flower\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNi41QTQuNSA0LjUgMCAxIDEgNy41IDEyIDQuNSA0LjUgMCAxIDEgMTIgNy41YTQuNSA0LjUgMCAxIDEgNC41IDQuNSA0LjUgNC41IDAgMSAxLTQuNSA0LjUiIC8+CiAgPHBhdGggZD0iTTEyIDcuNVY5IiAvPgogIDxwYXRoIGQ9Ik03LjUgMTJIOSIgLz4KICA8cGF0aCBkPSJNMTYuNSAxMkgxNSIgLz4KICA8cGF0aCBkPSJNMTIgMTYuNVYxNSIgLz4KICA8cGF0aCBkPSJtOCA4IDEuODggMS44OCIgLz4KICA8cGF0aCBkPSJNMTQuMTIgOS44OCAxNiA4IiAvPgogIDxwYXRoIGQ9Im04IDE2IDEuODgtMS44OCIgLz4KICA8cGF0aCBkPSJNMTQuMTIgMTQuMTIgMTYgMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/flower\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Flower = createLucideIcon('flower', __iconNode);\n\nexport default Flower;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACrD;AAaM,MAAAE,MAAA,GAASC,gBAAiB,WAAUP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}