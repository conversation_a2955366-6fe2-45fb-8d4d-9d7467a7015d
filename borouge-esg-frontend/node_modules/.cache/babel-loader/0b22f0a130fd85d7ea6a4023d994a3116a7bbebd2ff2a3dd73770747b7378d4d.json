{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}], [\"path\", {\n  d: \"m10 7-3 3 3 3\",\n  key: \"1eugdv\"\n}], [\"path\", {\n  d: \"M17 13v-1a2 2 0 0 0-2-2H7\",\n  key: \"ernfh3\"\n}]];\nconst MessageSquareReply = createLucideIcon(\"message-square-reply\", __iconNode);\nexport { __iconNode, MessageSquareReply as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareReply", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/message-square-reply.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z', key: '1lielz' }],\n  ['path', { d: 'm10 7-3 3 3 3', key: '1eugdv' }],\n  ['path', { d: 'M17 13v-1a2 2 0 0 0-2-2H7', key: 'ernfh3' }],\n];\n\n/**\n * @component @name MessageSquareReply\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgogIDxwYXRoIGQ9Im0xMCA3LTMgMyAzIDMiIC8+CiAgPHBhdGggZD0iTTE3IDEzdi0xYTIgMiAwIDAgMC0yLTJINyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/message-square-reply\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareReply = createLucideIcon('message-square-reply', __iconNode);\n\nexport default MessageSquareReply;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9F,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5D;AAaM,MAAAC,kBAAA,GAAqBC,gBAAiB,yBAAwBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}