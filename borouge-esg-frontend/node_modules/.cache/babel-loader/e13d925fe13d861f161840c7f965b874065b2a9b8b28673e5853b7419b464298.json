{"ast": null, "code": "import { isCSSVariableName } from 'motion-dom';\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n  for (const key in correctors) {\n    scaleCorrectors[key] = correctors[key];\n    if (isCSSVariableName(key)) {\n      scaleCorrectors[key].isCSSVariable = true;\n    }\n  }\n}\nexport { addScaleCorrector, scaleCorrectors };", "map": {"version": 3, "names": ["isCSSVariableName", "scaleCorrectors", "addScaleCorrector", "correctors", "key", "isCSSVariable"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs"], "sourcesContent": ["import { isCSSVariableName } from 'motion-dom';\n\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    for (const key in correctors) {\n        scaleCorrectors[key] = correctors[key];\n        if (isCSSVariableName(key)) {\n            scaleCorrectors[key].isCSSVariable = true;\n        }\n    }\n}\n\nexport { addScaleCorrector, scaleCorrectors };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,YAAY;AAE9C,MAAMC,eAAe,GAAG,CAAC,CAAC;AAC1B,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACnC,KAAK,MAAMC,GAAG,IAAID,UAAU,EAAE;IAC1BF,eAAe,CAACG,GAAG,CAAC,GAAGD,UAAU,CAACC,GAAG,CAAC;IACtC,IAAIJ,iBAAiB,CAACI,GAAG,CAAC,EAAE;MACxBH,eAAe,CAACG,GAAG,CAAC,CAACC,aAAa,GAAG,IAAI;IAC7C;EACJ;AACJ;AAEA,SAASH,iBAAiB,EAAED,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}