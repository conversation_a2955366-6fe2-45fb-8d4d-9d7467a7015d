{"ast": null, "code": "import { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\nfunction scroll(onScroll, {\n  axis = \"y\",\n  container = document.scrollingElement,\n  ...options\n} = {}) {\n  if (!container) return noop;\n  const optionsWithDefaults = {\n    axis,\n    container,\n    ...options\n  };\n  return typeof onScroll === \"function\" ? attachToFunction(onScroll, optionsWithDefaults) : attachToAnimation(onScroll, optionsWithDefaults);\n}\nexport { scroll };", "map": {"version": 3, "names": ["noop", "attachToAnimation", "attachToFunction", "scroll", "onScroll", "axis", "container", "document", "scrollingElement", "options", "optionsWithDefaults"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? attachToFunction(onScroll, optionsWithDefaults)\n        : attachToAnimation(onScroll, optionsWithDefaults);\n}\n\nexport { scroll };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,gBAAgB,QAAQ,uBAAuB;AAExD,SAASC,MAAMA,CAACC,QAAQ,EAAE;EAAEC,IAAI,GAAG,GAAG;EAAEC,SAAS,GAAGC,QAAQ,CAACC,gBAAgB;EAAE,GAAGC;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9F,IAAI,CAACH,SAAS,EACV,OAAON,IAAI;EACf,MAAMU,mBAAmB,GAAG;IAAEL,IAAI;IAAEC,SAAS;IAAE,GAAGG;EAAQ,CAAC;EAC3D,OAAO,OAAOL,QAAQ,KAAK,UAAU,GAC/BF,gBAAgB,CAACE,QAAQ,EAAEM,mBAAmB,CAAC,GAC/CT,iBAAiB,CAACG,QAAQ,EAAEM,mBAAmB,CAAC;AAC1D;AAEA,SAASP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}