{"ast": null, "code": "import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\nfunction startWaapiAnimation(element, valueName, keyframes, {\n  delay = 0,\n  duration = 300,\n  repeat = 0,\n  repeatType = \"loop\",\n  ease = \"easeOut\",\n  times\n} = {}, pseudoElement = undefined) {\n  const keyframeOptions = {\n    [valueName]: keyframes\n  };\n  if (times) keyframeOptions.offset = times;\n  const easing = mapEasingToNativeEasing(ease, duration);\n  /**\n   * If this is an easing array, apply to keyframes, not animation as a whole\n   */\n  if (Array.isArray(easing)) keyframeOptions.easing = easing;\n  if (statsBuffer.value) {\n    activeAnimations.waapi++;\n  }\n  const options = {\n    delay,\n    duration,\n    easing: !Array.isArray(easing) ? easing : \"linear\",\n    fill: \"both\",\n    iterations: repeat + 1,\n    direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\"\n  };\n  if (pseudoElement) options.pseudoElement = pseudoElement;\n  const animation = element.animate(keyframeOptions, options);\n  if (statsBuffer.value) {\n    animation.finished.finally(() => {\n      activeAnimations.waapi--;\n    });\n  }\n  return animation;\n}\nexport { startWaapiAnimation };", "map": {"version": 3, "names": ["activeAnimations", "statsBuffer", "mapEasingToNativeEasing", "startWaapiAnimation", "element", "valueName", "keyframes", "delay", "duration", "repeat", "repeatType", "ease", "times", "pseudoElement", "undefined", "keyframeOptions", "offset", "easing", "Array", "isArray", "value", "waapi", "options", "fill", "iterations", "direction", "animation", "animate", "finished", "finally"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs"], "sourcesContent": ["import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (statsBuffer.value) {\n        activeAnimations.waapi++;\n    }\n    const options = {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    };\n    if (pseudoElement)\n        options.pseudoElement = pseudoElement;\n    const animation = element.animate(keyframeOptions, options);\n    if (statsBuffer.value) {\n        animation.finished.finally(() => {\n            activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\nexport { startWaapiAnimation };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,uBAAuB,QAAQ,yBAAyB;AAEjE,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,QAAQ,GAAG,GAAG;EAAEC,MAAM,GAAG,CAAC;EAAEC,UAAU,GAAG,MAAM;EAAEC,IAAI,GAAG,SAAS;EAAEC;AAAO,CAAC,GAAG,CAAC,CAAC,EAAEC,aAAa,GAAGC,SAAS,EAAE;EAClL,MAAMC,eAAe,GAAG;IACpB,CAACV,SAAS,GAAGC;EACjB,CAAC;EACD,IAAIM,KAAK,EACLG,eAAe,CAACC,MAAM,GAAGJ,KAAK;EAClC,MAAMK,MAAM,GAAGf,uBAAuB,CAACS,IAAI,EAAEH,QAAQ,CAAC;EACtD;AACJ;AACA;EACI,IAAIU,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EACrBF,eAAe,CAACE,MAAM,GAAGA,MAAM;EACnC,IAAIhB,WAAW,CAACmB,KAAK,EAAE;IACnBpB,gBAAgB,CAACqB,KAAK,EAAE;EAC5B;EACA,MAAMC,OAAO,GAAG;IACZf,KAAK;IACLC,QAAQ;IACRS,MAAM,EAAE,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,QAAQ;IAClDM,IAAI,EAAE,MAAM;IACZC,UAAU,EAAEf,MAAM,GAAG,CAAC;IACtBgB,SAAS,EAAEf,UAAU,KAAK,SAAS,GAAG,WAAW,GAAG;EACxD,CAAC;EACD,IAAIG,aAAa,EACbS,OAAO,CAACT,aAAa,GAAGA,aAAa;EACzC,MAAMa,SAAS,GAAGtB,OAAO,CAACuB,OAAO,CAACZ,eAAe,EAAEO,OAAO,CAAC;EAC3D,IAAIrB,WAAW,CAACmB,KAAK,EAAE;IACnBM,SAAS,CAACE,QAAQ,CAACC,OAAO,CAAC,MAAM;MAC7B7B,gBAAgB,CAACqB,KAAK,EAAE;IAC5B,CAAC,CAAC;EACN;EACA,OAAOK,SAAS;AACpB;AAEA,SAASvB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}