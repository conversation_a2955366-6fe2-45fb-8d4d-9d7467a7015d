{"ast": null, "code": "const isMotionValue = value => Boolean(value && value.getVelocity);\nexport { isMotionValue };", "map": {"version": 3, "names": ["isMotionValue", "value", "Boolean", "getVelocity"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs"], "sourcesContent": ["const isMotionValue = (value) => Boolean(value && value.getVelocity);\n\nexport { isMotionValue };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAIC,KAAK,IAAKC,OAAO,CAACD,KAAK,IAAIA,KAAK,CAACE,WAAW,CAAC;AAEpE,SAASH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}