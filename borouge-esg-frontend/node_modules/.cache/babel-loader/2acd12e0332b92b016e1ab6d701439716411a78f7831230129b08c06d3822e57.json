{"ast": null, "code": "import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nfunction makeState({\n  scrapeMotionValuesFromProps,\n  createRenderState\n}, props, context, presenceContext) {\n  const state = {\n    latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n    renderState: createRenderState()\n  };\n  return state;\n}\nconst makeUseVisualState = config => (props, isStatic) => {\n  const context = useContext(MotionContext);\n  const presenceContext = useContext(PresenceContext);\n  const make = () => makeState(config, props, context, presenceContext);\n  return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n  const values = {};\n  const motionValues = scrapeMotionValues(props, {});\n  for (const key in motionValues) {\n    values[key] = resolveMotionValue(motionValues[key]);\n  }\n  let {\n    initial,\n    animate\n  } = props;\n  const isControllingVariants$1 = isControllingVariants(props);\n  const isVariantNode$1 = isVariantNode(props);\n  if (context && isVariantNode$1 && !isControllingVariants$1 && props.inherit !== false) {\n    if (initial === undefined) initial = context.initial;\n    if (animate === undefined) animate = context.animate;\n  }\n  let isInitialAnimationBlocked = presenceContext ? presenceContext.initial === false : false;\n  isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n  const variantToSet = isInitialAnimationBlocked ? animate : initial;\n  if (variantToSet && typeof variantToSet !== \"boolean\" && !isAnimationControls(variantToSet)) {\n    const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n    for (let i = 0; i < list.length; i++) {\n      const resolved = resolveVariantFromProps(props, list[i]);\n      if (resolved) {\n        const {\n          transitionEnd,\n          transition,\n          ...target\n        } = resolved;\n        for (const key in target) {\n          let valueTarget = target[key];\n          if (Array.isArray(valueTarget)) {\n            /**\n             * Take final keyframe if the initial animation is blocked because\n             * we want to initialise at the end of that blocked animation.\n             */\n            const index = isInitialAnimationBlocked ? valueTarget.length - 1 : 0;\n            valueTarget = valueTarget[index];\n          }\n          if (valueTarget !== null) {\n            values[key] = valueTarget;\n          }\n        }\n        for (const key in transitionEnd) {\n          values[key] = transitionEnd[key];\n        }\n      }\n    }\n  }\n  return values;\n}\nexport { makeUseVisualState };", "map": {"version": 3, "names": ["useContext", "isAnimationControls", "MotionContext", "PresenceContext", "isControllingVariants", "isVariantNode", "resolveVariantFromProps", "useConstant", "resolveMotionValue", "makeState", "scrapeMotionValuesFromProps", "createRenderState", "props", "context", "presenceContext", "state", "latestValues", "makeLatestValues", "renderState", "makeUseVisualState", "config", "isStatic", "make", "scrapeMotionValues", "values", "motionValues", "key", "initial", "animate", "isControllingVariants$1", "isVariantNode$1", "inherit", "undefined", "isInitialAnimationBlocked", "variantToSet", "list", "Array", "isArray", "i", "length", "resolved", "transitionEnd", "transition", "target", "valueTarget", "index"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = useContext(MotionContext);\n    const presenceContext = useContext(PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = resolveMotionValue(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = isControllingVariants(props);\n    const isVariantNode$1 = isVariantNode(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !isAnimationControls(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        for (let i = 0; i < list.length; i++) {\n            const resolved = resolveVariantFromProps(props, list[i]);\n            if (resolved) {\n                const { transitionEnd, transition, ...target } = resolved;\n                for (const key in target) {\n                    let valueTarget = target[key];\n                    if (Array.isArray(valueTarget)) {\n                        /**\n                         * Take final keyframe if the initial animation is blocked because\n                         * we want to initialise at the end of that blocked animation.\n                         */\n                        const index = isInitialAnimationBlocked\n                            ? valueTarget.length - 1\n                            : 0;\n                        valueTarget = valueTarget[index];\n                    }\n                    if (valueTarget !== null) {\n                        values[key] = valueTarget;\n                    }\n                }\n                for (const key in transitionEnd) {\n                    values[key] = transitionEnd[key];\n                }\n            }\n        }\n    }\n    return values;\n}\n\nexport { makeUseVisualState };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,gDAAgD;AACrG,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,4CAA4C;AAE/E,SAASC,SAASA,CAAC;EAAEC,2BAA2B;EAAEC;AAAmB,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAE;EACrG,MAAMC,KAAK,GAAG;IACVC,YAAY,EAAEC,gBAAgB,CAACL,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEJ,2BAA2B,CAAC;IAC5FQ,WAAW,EAAEP,iBAAiB,CAAC;EACnC,CAAC;EACD,OAAOI,KAAK;AAChB;AACA,MAAMI,kBAAkB,GAAIC,MAAM,IAAK,CAACR,KAAK,EAAES,QAAQ,KAAK;EACxD,MAAMR,OAAO,GAAGb,UAAU,CAACE,aAAa,CAAC;EACzC,MAAMY,eAAe,GAAGd,UAAU,CAACG,eAAe,CAAC;EACnD,MAAMmB,IAAI,GAAGA,CAAA,KAAMb,SAAS,CAACW,MAAM,EAAER,KAAK,EAAEC,OAAO,EAAEC,eAAe,CAAC;EACrE,OAAOO,QAAQ,GAAGC,IAAI,CAAC,CAAC,GAAGf,WAAW,CAACe,IAAI,CAAC;AAChD,CAAC;AACD,SAASL,gBAAgBA,CAACL,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAES,kBAAkB,EAAE;EAC3E,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAGF,kBAAkB,CAACX,KAAK,EAAE,CAAC,CAAC,CAAC;EAClD,KAAK,MAAMc,GAAG,IAAID,YAAY,EAAE;IAC5BD,MAAM,CAACE,GAAG,CAAC,GAAGlB,kBAAkB,CAACiB,YAAY,CAACC,GAAG,CAAC,CAAC;EACvD;EACA,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGhB,KAAK;EAChC,MAAMiB,uBAAuB,GAAGzB,qBAAqB,CAACQ,KAAK,CAAC;EAC5D,MAAMkB,eAAe,GAAGzB,aAAa,CAACO,KAAK,CAAC;EAC5C,IAAIC,OAAO,IACPiB,eAAe,IACf,CAACD,uBAAuB,IACxBjB,KAAK,CAACmB,OAAO,KAAK,KAAK,EAAE;IACzB,IAAIJ,OAAO,KAAKK,SAAS,EACrBL,OAAO,GAAGd,OAAO,CAACc,OAAO;IAC7B,IAAIC,OAAO,KAAKI,SAAS,EACrBJ,OAAO,GAAGf,OAAO,CAACe,OAAO;EACjC;EACA,IAAIK,yBAAyB,GAAGnB,eAAe,GACzCA,eAAe,CAACa,OAAO,KAAK,KAAK,GACjC,KAAK;EACXM,yBAAyB,GAAGA,yBAAyB,IAAIN,OAAO,KAAK,KAAK;EAC1E,MAAMO,YAAY,GAAGD,yBAAyB,GAAGL,OAAO,GAAGD,OAAO;EAClE,IAAIO,YAAY,IACZ,OAAOA,YAAY,KAAK,SAAS,IACjC,CAACjC,mBAAmB,CAACiC,YAAY,CAAC,EAAE;IACpC,MAAMC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IACxE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,MAAME,QAAQ,GAAGlC,uBAAuB,CAACM,KAAK,EAAEuB,IAAI,CAACG,CAAC,CAAC,CAAC;MACxD,IAAIE,QAAQ,EAAE;QACV,MAAM;UAAEC,aAAa;UAAEC,UAAU;UAAE,GAAGC;QAAO,CAAC,GAAGH,QAAQ;QACzD,KAAK,MAAMd,GAAG,IAAIiB,MAAM,EAAE;UACtB,IAAIC,WAAW,GAAGD,MAAM,CAACjB,GAAG,CAAC;UAC7B,IAAIU,KAAK,CAACC,OAAO,CAACO,WAAW,CAAC,EAAE;YAC5B;AACxB;AACA;AACA;YACwB,MAAMC,KAAK,GAAGZ,yBAAyB,GACjCW,WAAW,CAACL,MAAM,GAAG,CAAC,GACtB,CAAC;YACPK,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC;UACpC;UACA,IAAID,WAAW,KAAK,IAAI,EAAE;YACtBpB,MAAM,CAACE,GAAG,CAAC,GAAGkB,WAAW;UAC7B;QACJ;QACA,KAAK,MAAMlB,GAAG,IAAIe,aAAa,EAAE;UAC7BjB,MAAM,CAACE,GAAG,CAAC,GAAGe,aAAa,CAACf,GAAG,CAAC;QACpC;MACJ;IACJ;EACJ;EACA,OAAOF,MAAM;AACjB;AAEA,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}