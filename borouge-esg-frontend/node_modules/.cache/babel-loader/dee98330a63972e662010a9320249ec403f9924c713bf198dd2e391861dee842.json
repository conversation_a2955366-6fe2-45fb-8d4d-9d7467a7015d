{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m9 7-5 5 5 5\",\n  key: \"j5w590\"\n}], [\"path\", {\n  d: \"m15 7 5 5-5 5\",\n  key: \"1bl6da\"\n}]];\nconst ChevronsLeftRight = createLucideIcon(\"chevrons-left-right\", __iconNode);\nexport { __iconNode, ChevronsLeftRight as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ChevronsLeftRight", "createLucideIcon"], "sources": ["/Users/<USER>/Borouge/borouge-esg-frontend/node_modules/lucide-react/src/icons/chevrons-left-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm9 7-5 5 5 5', key: 'j5w590' }],\n  ['path', { d: 'm15 7 5 5-5 5', key: '1bl6da' }],\n];\n\n/**\n * @component @name ChevronsLeftRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSA3LTUgNSA1IDUiIC8+CiAgPHBhdGggZD0ibTE1IDcgNSA1LTUgNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevrons-left-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronsLeftRight = createLucideIcon('chevrons-left-right', __iconNode);\n\nexport default ChevronsLeftRight;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD;AAaM,MAAAC,iBAAA,GAAoBC,gBAAiB,wBAAuBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}