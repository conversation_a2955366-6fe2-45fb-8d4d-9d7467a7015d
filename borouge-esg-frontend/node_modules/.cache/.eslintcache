[{"/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js": "1", "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js": "3", "/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js": "4"}, {"size": 535, "mtime": 1748419873894, "results": "5", "hashOfConfig": "6"}, {"size": 362, "mtime": 1748419873894, "results": "7", "hashOfConfig": "6"}, {"size": 10887, "mtime": 1748420778829, "results": "8", "hashOfConfig": "6"}, {"size": 12579, "mtime": 1748420825939, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pjo371", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js", [], []]