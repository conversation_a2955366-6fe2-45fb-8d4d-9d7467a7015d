[{"/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js": "1", "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js": "3", "/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js": "4"}, {"size": 535, "mtime": 1748419873894, "results": "5", "hashOfConfig": "6"}, {"size": 362, "mtime": 1748419873894, "results": "7", "hashOfConfig": "6"}, {"size": 10434, "mtime": 1748423002926, "results": "8", "hashOfConfig": "6"}, {"size": 53363, "mtime": 1748426833110, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pjo371", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js", ["22", "23", "24"], [], {"ruleId": "25", "severity": 1, "message": "26", "line": 17, "column": 3, "nodeType": "27", "messageId": "28", "endLine": 17, "endColumn": 13}, {"ruleId": "25", "severity": 1, "message": "29", "line": 18, "column": 3, "nodeType": "27", "messageId": "28", "endLine": 18, "endColumn": 8}, {"ruleId": "30", "severity": 1, "message": "31", "line": 82, "column": 6, "nodeType": "32", "endLine": 82, "endColumn": 20, "suggestions": "33"}, "no-unused-vars", "'DollarSign' is defined but never used.", "Identifier", "unusedVar", "'Clock' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateMockResponse'. Either include it or remove the dependency array.", "ArrayExpression", ["34"], {"desc": "35", "fix": "36"}, "Update the dependencies array to be: [generateMockResponse, initialQuery]", {"range": "37", "text": "38"}, [2042, 2056], "[generateMockResponse, initialQuery]"]