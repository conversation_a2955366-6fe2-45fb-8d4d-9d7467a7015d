[{"/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js": "1", "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js": "3", "/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js": "4"}, {"size": 535, "mtime": 1748419873894, "results": "5", "hashOfConfig": "6"}, {"size": 362, "mtime": 1748419873894, "results": "7", "hashOfConfig": "6"}, {"size": 10434, "mtime": 1748423002926, "results": "8", "hashOfConfig": "6"}, {"size": 54420, "mtime": 1748425623702, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pjo371", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/components/ConversationView.js", ["22"], [], {"ruleId": "23", "severity": 1, "message": "24", "line": 70, "column": 6, "nodeType": "25", "endLine": 70, "endColumn": 20, "suggestions": "26"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateMockResponse'. Either include it or remove the dependency array.", "ArrayExpression", ["27"], {"desc": "28", "fix": "29"}, "Update the dependencies array to be: [generateMockResponse, initialQuery]", {"range": "30", "text": "31"}, [1657, 1671], "[generateMockResponse, initialQuery]"]