[{"/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js": "1", "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js": "3"}, {"size": 535, "mtime": 1748419873894, "results": "4", "hashOfConfig": "5"}, {"size": 362, "mtime": 1748419873894, "results": "6", "hashOfConfig": "5"}, {"size": 4472, "mtime": 1748420010306, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pjo371", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Borouge/borouge-esg-frontend/src/index.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Borouge/borouge-esg-frontend/src/App.js", [], []]