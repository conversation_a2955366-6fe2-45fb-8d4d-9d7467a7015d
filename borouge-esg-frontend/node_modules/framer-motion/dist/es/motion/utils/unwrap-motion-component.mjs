import { isMotionComponent } from './is-motion-component.mjs';
import { motionComponentSymbol } from './symbol.mjs';

/**
 * Unwraps a `motion` component and returns either a string for `motion.div` or
 * the React component for `motion(Component)`.
 *
 * If the component is not a `motion` component it returns undefined.
 */
function unwrapMotionComponent(component) {
    if (isMotionComponent(component)) {
        return component[motionComponentSymbol];
    }
    return undefined;
}

export { unwrapMotionComponent };
