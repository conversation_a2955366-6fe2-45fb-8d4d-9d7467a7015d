{"version": 3, "file": "document-cloner.js", "sourceRoot": "", "sources": ["../../../src/dom/document-cloner.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,6CAgBuB;AACvB,+CAA2E;AAE3E,0DAA+E;AAC/E,+EAA2F;AAC3F,sCAAqF;AACrF,6DAA4D;AAE5D,6CAA2D;AAoB3D,IAAM,gBAAgB,GAAG,yBAAyB,CAAC;AAEnD;IAQI,wBACqB,OAAgB,EACjC,OAAoB,EACH,OAA4B;QAF5B,YAAO,GAAP,OAAO,CAAS;QAEhB,YAAO,GAAP,OAAO,CAAqB;QAE7C,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,sBAAY,EAAE,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACrE;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,CAAgB,CAAC;IACvG,CAAC;IAED,iCAAQ,GAAR,UAAS,aAAuB,EAAE,UAAkB;QAApD,iBAoEC;QAnEG,IAAM,MAAM,GAAsB,qBAAqB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAEnF,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC;SACzD;QAED,IAAM,OAAO,GAAI,aAAa,CAAC,WAAsB,CAAC,WAAW,CAAC;QAClE,IAAM,OAAO,GAAI,aAAa,CAAC,WAAsB,CAAC,WAAW,CAAC;QAElE,IAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC;QACzC,IAAM,aAAa,GAAa,WAAW,CAAC,QAAQ,CAAC;QAErD;;WAEG;QAEH,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;;;;;wBACzC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;wBACjD,IAAI,WAAW,EAAE;4BACb,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;4BACtD,IACI,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;gCAC/C,CAAC,WAAW,CAAC,OAAO,KAAK,UAAU,CAAC,GAAG,IAAI,WAAW,CAAC,OAAO,KAAK,UAAU,CAAC,IAAI,CAAC,EACrF;gCACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gCAClF,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CACrD,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,EACrC,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,EACpC,CAAC,EACD,CAAC,CACJ,CAAC;6BACL;yBACJ;wBAEK,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;wBAE/B,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC;wBAErD,IAAI,OAAO,gBAAgB,KAAK,WAAW,EAAE;4BACzC,sBAAO,OAAO,CAAC,MAAM,CAAC,uBAAqB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,4BAAyB,CAAC,EAAC;yBACvG;6BAEG,CAAA,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,KAAK,CAAA,EAAhD,wBAAgD;wBAChD,qBAAM,aAAa,CAAC,KAAK,CAAC,KAAK,EAAA;;wBAA/B,SAA+B,CAAC;;;6BAGhC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAA1C,wBAA0C;wBAC1C,qBAAM,WAAW,CAAC,aAAa,CAAC,EAAA;;wBAAhC,SAAgC,CAAC;;;wBAGrC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;4BAC/B,sBAAO,OAAO,CAAC,OAAO,EAAE;qCACnB,IAAI,CAAC,cAAM,OAAA,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC,EAAxC,CAAwC,CAAC;qCACpD,IAAI,CAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,EAAC;yBAC3B;wBAED,sBAAO,MAAM,EAAC;;;aACjB,CAAC,CAAC;QAEH,aAAa,CAAC,IAAI,EAAE,CAAC;QACrB,aAAa,CAAC,KAAK,CAAI,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAe,CAAC,CAAC;QAC1E,6FAA6F;QAC7F,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1E,aAAa,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;QACzG,aAAa,CAAC,KAAK,EAAE,CAAC;QAEtB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,2CAAkB,GAAlB,UAAuD,IAAO;QAC1D,IAAI,sBAAW,CAAC,IAAI,gBAAqB,EAAE;YACvC,QAAQ,CAAC;SACZ;QACD,IAAI,6BAAe,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SACvC;QACD,IAAI,4BAAc,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,4BAAc,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;SACtC;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAM,CAAC;QACzC,IAAI,4BAAc,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,4BAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,GAAG,EAAE;gBACzE,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC5B,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;aACrB;YAED,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;gBAC1B,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;aAC3B;SACJ;QAED,IAAI,6BAAe,CAAC,KAAK,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;SAC/C;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,iDAAwB,GAAxB,UAAyB,IAAiB;QACtC,IAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACjE,qBAAa,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAEjC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,yCAAgB,GAAhB,UAAiB,IAAsB;QACnC,IAAI;YACA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAkC,CAAC;YACtD,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE;gBACzB,IAAM,GAAG,GAAW,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,GAAW,EAAE,IAAa;oBACnF,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;wBAC1C,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;qBAC7B;oBACD,OAAO,GAAG,CAAC;gBACf,CAAC,EAAE,EAAE,CAAC,CAAC;gBACP,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAqB,CAAC;gBACxD,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC;gBACxB,OAAO,KAAK,CAAC;aAChB;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,sDAAsD;YACtD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,CAAC,CAAC,CAAC;YACnE,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,EAAE;gBAC5B,MAAM,CAAC,CAAC;aACX;SACJ;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAqB,CAAC;IACrD,CAAC;IAED,0CAAiB,GAAjB,UAAkB,MAAyB;;QACvC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,aAAa,EAAE;YACnD,IAAM,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACtD,IAAI;gBACA,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC7B,OAAO,GAAG,CAAC;aACd;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,qDAAqD,EAAE,MAAM,CAAC,CAAC;aAC3F;SACJ;QAED,IAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAsB,CAAC;QAElE,IAAI;YACA,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAClC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,IAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACpC,IAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,GAAG,EAAE;oBACjC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACrF;qBAAM;oBACH,IAAM,EAAE,GAAG,MAAA,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,mCAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBACrE,IAAI,EAAE,EAAE;wBACJ,IAAM,OAAO,GAAG,EAAE,CAAC,oBAAoB,EAAE,CAAC;wBAC1C,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,qBAAqB,MAAK,KAAK,EAAE;4BAC1C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CACpB,qEAAqE,EACrE,MAAM,CACT,CAAC;yBACL;qBACJ;oBAED,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACrC;aACJ;YACD,OAAO,YAAY,CAAC;SACvB;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,MAAM,CAAC,CAAC;SAC/E;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED,yCAAgB,GAAhB,UAAiB,KAAuB;QACpC,IAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE3D,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;QACjC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;QACnC,IAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI;YACA,IAAI,GAAG,EAAE;gBACL,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;oBAC1B,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;iBACvD;aACJ;YACD,OAAO,MAAM,CAAC;SACjB;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;SAC7E;QAED,IAAM,WAAW,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAEhE,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;QACtC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;QACxC,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,wCAAe,GAAf,UAAgB,KAA+B,EAAE,KAAW,EAAE,UAAmB;QAC7E,IACI,CAAC,2BAAa,CAAC,KAAK,CAAC;YACrB,CAAC,CAAC,6BAAe,CAAC,KAAK,CAAC;gBACpB,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC;gBACrC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EACjG;YACE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,2BAAa,CAAC,KAAK,CAAC,IAAI,CAAC,4BAAc,CAAC,KAAK,CAAC,EAAE;gBAC7E,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;aACxD;SACJ;IACL,CAAC;IAED,wCAAe,GAAf,UAAgB,IAAa,EAAE,KAA+B,EAAE,UAAmB;QAAnF,iBAeC;QAdG,KACI,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAC1E,KAAK,EACL,KAAK,GAAG,KAAK,CAAC,WAAW,EAC3B;YACE,IAAI,2BAAa,CAAC,KAAK,CAAC,IAAI,2BAAa,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,aAAa,KAAK,UAAU,EAAE;gBAC3F,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,EAAiB,CAAC;gBAC3D,IAAI,aAAa,CAAC,MAAM,EAAE;oBACtB,aAAa,CAAC,OAAO,CAAC,UAAC,YAAY,IAAK,OAAA,KAAI,CAAC,eAAe,CAAC,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,EAArD,CAAqD,CAAC,CAAC;iBAClG;aACJ;iBAAM;gBACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;aAClD;SACJ;IACL,CAAC;IAED,kCAAS,GAAT,UAAU,IAAU,EAAE,UAAmB;QACrC,IAAI,wBAAU,CAAC,IAAI,CAAC,EAAE;YAClB,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAChC;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;QAE9C,IAAI,MAAM,IAAI,2BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,+BAAiB,CAAC,IAAI,CAAC,IAAI,8BAAgB,CAAC,IAAI,CAAC,CAAC,EAAE;YACtF,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC5C,KAAK,CAAC,KAAK,CAAC,kBAAkB,GAAG,MAAM,CAAC;YAExC,IAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC7D,IAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE3D,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,+BAAiB,CAAC,KAAK,CAAC,EAAE;gBAC5D,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;aACvC;YACD,IAAI,2BAAa,CAAC,KAAK,CAAC,EAAE;gBACtB,sBAAsB,CAAC,KAAK,CAAC,CAAC;aACjC;YAED,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,mCAA2B,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAC3F,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE7F,IAAI,6BAAe,CAAC,IAAI,CAAC,EAAE;gBACvB,UAAU,GAAG,IAAI,CAAC;aACrB;YAED,IAAI,CAAC,4BAAc,CAAC,IAAI,CAAC,EAAE;gBACvB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;aACjD;YAED,IAAI,MAAM,EAAE;gBACR,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;aAChD;YAED,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC1F,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC5B;YAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE5B,IACI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,8BAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,6BAAe,CAAC,IAAI,CAAC,CAAC;gBACxF,UAAU,EACZ;gBACE,qBAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aAC/B;YAED,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;gBAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aACxE;YAED,IACI,CAAC,+BAAiB,CAAC,IAAI,CAAC,IAAI,6BAAe,CAAC,IAAI,CAAC,CAAC;gBAClD,CAAC,+BAAiB,CAAC,KAAK,CAAC,IAAI,6BAAe,CAAC,KAAK,CAAC,CAAC,EACtD;gBACE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,6CAAoB,GAApB,UACI,IAAa,EACb,KAAc,EACd,KAA0B,EAC1B,SAA4B;QAJhC,iBAqGC;QA/FG,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC;QACrC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,kBAAkB,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;YACrG,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,mCAA2B,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1E,IAAM,WAAW,GAAG,IAAI,kCAA0B,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAExE,IAAM,wBAAwB,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACpF,qBAAa,CAAC,KAAK,EAAE,wBAAwB,CAAC,CAAC;QAE/C,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK;YAC9B,IAAI,KAAK,CAAC,IAAI,yBAA2B,EAAE;gBACvC,wBAAwB,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;aAC9E;iBAAM,IAAI,KAAK,CAAC,IAAI,uBAAwB,EAAE;gBAC3C,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1C,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACxB,wBAAwB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aAC7C;iBAAM,IAAI,KAAK,CAAC,IAAI,sBAAuB,EAAE;gBAC1C,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;oBACvB,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAY,CAAC,CAAC;oBAC/C,IAAI,IAAI,CAAC,MAAM,EAAE;wBACb,wBAAwB,CAAC,WAAW,CAChC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAClE,CAAC;qBACL;iBACJ;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;oBAC3B,IAAA,KAA0B,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,gCAAuB,CAAC,EAArE,OAAO,QAAA,EAAE,YAAY,QAAgD,CAAC;oBAC7E,IAAI,OAAO,IAAI,qBAAY,CAAC,OAAO,CAAC,EAAE;wBAClC,IAAM,YAAY,GAAG,KAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAClE,IAAM,WAAW,GACb,YAAY,IAAI,qBAAY,CAAC,YAAY,CAAC;4BACtC,CAAC,CAAC,+BAAa,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC;4BACvD,CAAC,gBAAwB,CAAC;wBAElC,wBAAwB,CAAC,WAAW,CAChC,QAAQ,CAAC,cAAc,CAAC,2BAAiB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,CAC/E,CAAC;qBACL;iBACJ;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;oBAC5B,IAAA,KAAiC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,gCAAuB,CAAC,EAA5E,OAAO,QAAA,EAAE,KAAK,QAAA,EAAE,YAAY,QAAgD,CAAC;oBACpF,IAAI,OAAO,IAAI,qBAAY,CAAC,OAAO,CAAC,EAAE;wBAClC,IAAM,aAAa,GAAG,KAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACpE,IAAM,aAAW,GACb,YAAY,IAAI,qBAAY,CAAC,YAAY,CAAC;4BACtC,CAAC,CAAC,+BAAa,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC;4BACvD,CAAC,gBAAwB,CAAC;wBAClC,IAAM,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,yBAA2B,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpF,IAAM,IAAI,GAAG,aAAa;6BACrB,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,2BAAiB,CAAC,KAAK,EAAE,aAAW,EAAE,KAAK,CAAC,EAA5C,CAA4C,CAAC;6BAC5D,IAAI,CAAC,SAAS,CAAC,CAAC;wBAErB,wBAAwB,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;qBACvE;iBACJ;qBAAM;oBACH,0CAA0C;iBAC7C;aACJ;iBAAM,IAAI,KAAK,CAAC,IAAI,yBAA0B,EAAE;gBAC7C,QAAQ,KAAK,CAAC,KAAK,EAAE;oBACjB,KAAK,YAAY;wBACb,wBAAwB,CAAC,WAAW,CAChC,QAAQ,CAAC,cAAc,CAAC,iBAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,KAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,CACjF,CAAC;wBACF,MAAM;oBACV,KAAK,aAAa;wBACd,wBAAwB,CAAC,WAAW,CAChC,QAAQ,CAAC,cAAc,CAAC,iBAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,KAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAClF,CAAC;wBACF,MAAM;oBACV;wBACI,yEAAyE;wBACzE,wBAAwB,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;iBAClF;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,wBAAwB,CAAC,SAAS,GAAM,gCAAgC,SAAI,+BAAiC,CAAC;QAC9G,IAAM,YAAY,GACd,SAAS,KAAK,iBAAiB,CAAC,MAAM;YAClC,CAAC,CAAC,MAAI,gCAAkC;YACxC,CAAC,CAAC,MAAI,+BAAiC,CAAC;QAEhD,IAAI,8BAAgB,CAAC,KAAK,CAAC,EAAE;YACzB,KAAK,CAAC,SAAS,CAAC,SAAS,IAAI,YAAY,CAAC;SAC7C;aAAM;YACH,KAAK,CAAC,SAAS,IAAI,YAAY,CAAC;SACnC;QAED,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAEM,sBAAO,GAAd,UAAe,SAA4B;QACvC,IAAI,SAAS,CAAC,UAAU,EAAE;YACtB,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IACL,qBAAC;AAAD,CAAC,AAxbD,IAwbC;AAxbY,wCAAc;AA0b3B,IAAK,iBAGJ;AAHD,WAAK,iBAAiB;IAClB,6DAAM,CAAA;IACN,2DAAK,CAAA;AACT,CAAC,EAHI,iBAAiB,KAAjB,iBAAiB,QAGrB;AAED,IAAM,qBAAqB,GAAG,UAAC,aAAuB,EAAE,MAAc;IAClE,IAAM,oBAAoB,GAAG,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEnE,oBAAoB,CAAC,SAAS,GAAG,uBAAuB,CAAC;IACzD,oBAAoB,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;IACjD,oBAAoB,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC9C,oBAAoB,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC;IAC7C,oBAAoB,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;IACvC,oBAAoB,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;IACxC,oBAAoB,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IACrD,oBAAoB,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;IACvD,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,8BAA8B;IACrE,oBAAoB,CAAC,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IAC5D,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;IAErD,OAAO,oBAAoB,CAAC;AAChC,CAAC,CAAC;AAEF,IAAM,UAAU,GAAG,UAAC,GAAqB;IACrC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;QACvB,IAAI,GAAG,CAAC,QAAQ,EAAE;YACd,OAAO,EAAE,CAAC;YACV,OAAO;SACV;QACD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;YACV,OAAO,EAAE,CAAC;YACV,OAAO;SACV;QACD,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC;QACrB,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,IAAM,WAAW,GAAG,UAAC,QAAsB;IACvC,OAAO,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC;AAEF,IAAM,YAAY,GAAG,UAAC,MAAyB;IAC3C,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QAC/B,IAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC;QAEzC,IAAI,CAAC,WAAW,EAAE;YACd,OAAO,MAAM,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,IAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE3C,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG;YACjC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;YAC1C,IAAM,QAAQ,GAAG,WAAW,CAAC;gBACzB,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,CAAC,UAAU,KAAK,UAAU,EAAE;oBACrF,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,CAAC,MAAM,CAAC,CAAC;iBACnB;YACL,CAAC,EAAE,EAAE,CAAC,CAAC;QACX,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,IAAM,sBAAsB,GAAG;IAC3B,KAAK;IACL,GAAG;IACH,SAAS,CAAC,gDAAgD;CAC7D,CAAC;AAEK,IAAM,aAAa,GAAG,UAAqC,KAA0B,EAAE,MAAS;IACnG,0CAA0C;IAC1C,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACxC,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YACjD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACxE;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AATW,QAAA,aAAa,iBASxB;AAEF,IAAM,gBAAgB,GAAG,UAAC,OAA6B;IACnD,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,OAAO,EAAE;QACT,GAAG,IAAI,YAAY,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,EAAE;YACd,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC;SACvB;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,GAAG,IAAI,OAAO,CAAC,cAAc,CAAC;SACjC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,GAAG,IAAI,OAAI,OAAO,CAAC,QAAQ,OAAG,CAAC;SAClC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,GAAG,IAAI,OAAI,OAAO,CAAC,QAAQ,OAAG,CAAC;SAClC;QAED,GAAG,IAAI,GAAG,CAAC;KACd;IAED,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AAEF,IAAM,kBAAkB,GAAG,UAAC,aAA8B,EAAE,CAAS,EAAE,CAAS;IAC5E,IACI,aAAa;QACb,aAAa,CAAC,WAAW;QACzB,CAAC,CAAC,KAAK,aAAa,CAAC,WAAW,CAAC,WAAW,IAAI,CAAC,KAAK,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,EAC9F;QACE,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC5C;AACL,CAAC,CAAC;AAEF,IAAM,iBAAiB,GAAG,UAAC,EAA8C;QAA7C,OAAO,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA;IACrC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;IACvB,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEF,IAAM,aAAa,GAAG,SAAS,CAAC;AAChC,IAAM,YAAY,GAAG,QAAQ,CAAC;AAC9B,IAAM,gCAAgC,GAAG,uCAAuC,CAAC;AACjF,IAAM,+BAA+B,GAAG,sCAAsC,CAAC;AAE/E,IAAM,yBAAyB,GAAG,oEAGhC,CAAC;AAEH,IAAM,sBAAsB,GAAG,UAAC,IAAiB;IAC7C,YAAY,CACR,IAAI,EACJ,MAAI,gCAAgC,GAAG,aAAa,GAAG,yBAAyB,oBAC5E,+BAA+B,GAAG,YAAY,GAAG,yBAA2B,CACnF,CAAC;AACN,CAAC,CAAC;AAEF,IAAM,YAAY,GAAG,UAAC,IAAiB,EAAE,MAAc;IACnD,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;IACpC,IAAI,QAAQ,EAAE;QACV,IAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KAC3B;AACL,CAAC,CAAC"}