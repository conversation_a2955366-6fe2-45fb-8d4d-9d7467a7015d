# Borouge ESG Intelligence Frontend

A React-based frontend application for Borouge's ESG Intelligence MVP, designed to provide real-time market intelligence, regulatory updates, and competitor analysis for the petrochemical industry.

## 🎯 Overview

This frontend application implements the **Presentation Layer** from the Borouge ESG Intelligence MVP architecture, providing a conversational interface for the two-stage intelligence pipeline (Perplexity + Claude).

### Key Features

- **ESG-Focused Search Interface**: Tailored for petrochemical industry intelligence
- **Borouge Branding**: Custom color scheme and industry-specific context
- **Conversational UI**: Ready for integration with the hybrid AI pipeline
- **Responsive Design**: Modern, clean interface following the reference design
- **Suggestion Chips**: Pre-configured ESG and industry-specific queries

## 🏗️ Architecture Alignment

This frontend aligns with the technical specifications in `check.md`:

- **Presentation Layer**: React conversational interface ✅
- **ESG Context**: Borouge-specific business intelligence focus ✅
- **Two-Stage Pipeline Ready**: UI prepared for Perplexity → Claude workflow ✅
- **Cost-Conscious Design**: Lightweight, efficient frontend ✅

## 🚀 Technology Stack

- **React 18**: Modern React with hooks
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful, customizable icons
- **Custom Borouge Theme**: Brand-aligned color scheme

## 🛠️ Development

### Prerequisites
- Node.js 16+
- npm or yarn

### Installation
```bash
cd borouge-esg-frontend
npm install
```

### Development Server
```bash
npm run dev
```
Access the application at `http://localhost:5173/`

### Build for Production
```bash
npm run build
```

## 🎨 Design Features

### Visual Elements
- **Sidebar Navigation**: Intelligence tracking and conversation history
- **Central Search Interface**: Large, prominent search bar
- **Suggestion Chips**: Industry-specific quick searches
- **Borouge Branding**: Custom blue (#0066CC) and green (#00A651) colors

### ESG-Specific Content
- EU plastic regulations
- CBAM carbon border adjustment
- Circular economy packaging
- Competitor sustainability strategies
- Market intelligence
- Regulatory compliance

## 🔗 Integration Points

The frontend is designed to integrate with:

1. **API Gateway**: For query processing
2. **Perplexity API**: Web search functionality
3. **Claude API**: Business analysis
4. **Supabase**: Conversation history and caching
5. **Authentication**: User management (future)

## 🎯 Business Context

### Borouge-Specific Features
- **Company Profile**: UAE-based petrochemical producer
- **Market Focus**: €2.3B EU exports, $1.8B Asian markets
- **Product Lines**: 5M tonnes polyolefins (PP/PE)
- **Competitive Landscape**: SABIC, Dow, ExxonMobil positioning

### ESG Intelligence Categories
- **Regulatory**: EU CBAM, packaging directives
- **Market**: Sustainability trends, circular economy
- **Competitive**: Peer company strategies
- **Financial**: Cost implications and revenue impact

## 📈 Success Metrics

Aligned with check.md objectives:
- **Cost Efficiency**: Lightweight frontend within budget
- **Quality**: Enterprise-grade user experience
- **Speed**: Real-time response capability
- **Credibility**: Professional, trustworthy interface

---

Built with ❤️ for Borouge's ESG Intelligence initiative
